// Customers Management System
class CustomersManager {
    constructor() {
        this.customers = [];
        this.filteredCustomers = [];
        this.currentEditingCustomer = null;
        this.viewMode = 'grid'; // 'grid' or 'table'
        this.init();
    }

    async init() {
        this.setupEventListeners();
        await this.loadCustomers();
        this.setupFilters();
    }

    setupEventListeners() {
        // Add customer button
        const addCustomerBtn = document.getElementById('addCustomerBtn');
        if (addCustomerBtn) {
            addCustomerBtn.addEventListener('click', () => this.showAddCustomerModal());
        }

        // Toggle view button
        const toggleViewBtn = document.getElementById('toggleCustomersView');
        if (toggleViewBtn) {
            toggleViewBtn.addEventListener('click', () => this.toggleView());
        }

        // Search functionality
        const searchInput = document.getElementById('searchCustomers');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => this.handleSearch(e.target.value));
        }

        // Filter functionality
        const typeFilter = document.getElementById('customerTypeFilter');
        const statusFilter = document.getElementById('customerStatusFilter');
        const cityFilter = document.getElementById('customerCityFilter');

        if (typeFilter) {
            typeFilter.addEventListener('change', () => this.applyFilters());
        }
        if (statusFilter) {
            statusFilter.addEventListener('change', () => this.applyFilters());
        }
        if (cityFilter) {
            cityFilter.addEventListener('change', () => this.applyFilters());
        }
    }

    async loadCustomers() {
        try {
            this.customers = await storage.getAll('customers');
            this.filteredCustomers = [...this.customers];
            this.renderCustomers();
            this.updateCityFilter();
        } catch (error) {
            console.error('Error loading customers:', error);
            window.app.showNotification('خطأ في تحميل العملاء', 'error');
        }
    }

    renderCustomers() {
        if (this.viewMode === 'grid') {
            this.renderCustomersGrid();
        } else {
            this.renderCustomersTable();
        }
    }

    renderCustomersGrid() {
        const container = document.getElementById('customersGrid');
        if (!container) return;

        if (this.filteredCustomers.length === 0) {
            container.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-building fa-3x"></i>
                    <h3>لا يوجد عملاء</h3>
                    <p>ابدأ بإضافة عميل جديد</p>
                    <button class="btn-primary" onclick="customersManager.showAddCustomerModal()">
                        <i class="fas fa-plus"></i>
                        إضافة عميل
                    </button>
                </div>
            `;
            return;
        }

        // Get orders for each customer to calculate stats
        this.getCustomerStats().then(customerStats => {
            container.innerHTML = this.filteredCustomers.map(customer => {
                const stats = customerStats[customer.id] || { totalOrders: 0, totalAmount: 0, deliveredOrders: 0 };
                
                return `
                    <div class="customer-card">
                        <div class="customer-header">
                            <div class="customer-avatar">
                                <i class="fas fa-${customer.type === 'company' ? 'building' : 'user'}"></i>
                            </div>
                            <div class="customer-info">
                                <h3>${customer.name}</h3>
                                <p class="customer-type">
                                    <i class="fas fa-${customer.type === 'company' ? 'building' : 'user'}"></i>
                                    ${customer.type === 'company' ? 'شركة' : 'فرد'}
                                </p>
                                ${customer.phone ? `<p><i class="fas fa-phone"></i> ${customer.phone}</p>` : ''}
                                ${customer.city ? `<p><i class="fas fa-map-marker-alt"></i> ${customer.city}</p>` : ''}
                            </div>
                            <div class="customer-status">
                                <span class="status-badge status-${customer.status}">
                                    ${customer.status === 'active' ? 'نشط' : 'غير نشط'}
                                </span>
                            </div>
                        </div>
                        
                        <div class="customer-stats">
                            <div class="customer-stat">
                                <div class="customer-stat-value">${stats.totalOrders}</div>
                                <div class="customer-stat-label">إجمالي الطلبات</div>
                            </div>
                            <div class="customer-stat">
                                <div class="customer-stat-value">${stats.deliveredOrders}</div>
                                <div class="customer-stat-label">طلبات مسلمة</div>
                            </div>
                            <div class="customer-stat">
                                <div class="customer-stat-value">${stats.totalAmount.toLocaleString()}</div>
                                <div class="customer-stat-label">إجمالي المبلغ (د.ع)</div>
                            </div>
                        </div>

                        ${customer.email ? `
                            <div class="customer-contact">
                                <p><i class="fas fa-envelope"></i> ${customer.email}</p>
                            </div>
                        ` : ''}

                        <div class="customer-actions">
                            <button class="btn-secondary" onclick="customersManager.editCustomer(${customer.id})">
                                <i class="fas fa-edit"></i>
                                تعديل
                            </button>
                            <button class="btn-success" onclick="customersManager.viewCustomerOrders(${customer.id})">
                                <i class="fas fa-list"></i>
                                الطلبات
                            </button>
                            <button class="btn-danger" onclick="customersManager.deleteCustomer(${customer.id})">
                                <i class="fas fa-trash"></i>
                                حذف
                            </button>
                        </div>
                    </div>
                `;
            }).join('');
        });
    }

    renderCustomersTable() {
        const tbody = document.getElementById('customersTableBody');
        if (!tbody) return;

        if (this.filteredCustomers.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="8" class="text-center">لا توجد عملاء</td>
                </tr>
            `;
            return;
        }

        this.getCustomerStats().then(customerStats => {
            tbody.innerHTML = this.filteredCustomers.map(customer => {
                const stats = customerStats[customer.id] || { totalOrders: 0 };
                
                return `
                    <tr>
                        <td>
                            <div class="customer-name-cell">
                                <i class="fas fa-${customer.type === 'company' ? 'building' : 'user'}"></i>
                                <strong>${customer.name}</strong>
                            </div>
                        </td>
                        <td>
                            <span class="type-badge type-${customer.type}">
                                ${customer.type === 'company' ? 'شركة' : 'فرد'}
                            </span>
                        </td>
                        <td>${customer.phone || '-'}</td>
                        <td>${customer.city || '-'}</td>
                        <td>${stats.totalOrders}</td>
                        <td>
                            <span class="status-badge status-${customer.status}">
                                ${customer.status === 'active' ? 'نشط' : 'غير نشط'}
                            </span>
                        </td>
                        <td>${this.formatDate(customer.createdAt)}</td>
                        <td>
                            <div class="action-buttons">
                                <button class="btn-icon" onclick="customersManager.editCustomer(${customer.id})" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn-icon" onclick="customersManager.viewCustomerOrders(${customer.id})" title="الطلبات">
                                    <i class="fas fa-list"></i>
                                </button>
                                <button class="btn-icon" onclick="customersManager.deleteCustomer(${customer.id})" title="حذف">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                `;
            }).join('');
        });
    }

    async getCustomerStats() {
        try {
            const orders = await storage.getAll('orders');
            const stats = {};

            this.customers.forEach(customer => {
                const customerOrders = orders.filter(order => order.customerId == customer.id);
                const deliveredOrders = customerOrders.filter(order => order.status === 'delivered');
                
                stats[customer.id] = {
                    totalOrders: customerOrders.length,
                    deliveredOrders: deliveredOrders.length,
                    totalAmount: deliveredOrders.reduce((sum, order) => sum + order.deliveryPrice, 0)
                };
            });

            return stats;
        } catch (error) {
            console.error('Error calculating customer stats:', error);
            return {};
        }
    }

    toggleView() {
        const toggleBtn = document.getElementById('toggleCustomersView');
        const gridView = document.getElementById('customersGrid');
        const tableView = document.getElementById('customersTable');

        if (this.viewMode === 'grid') {
            this.viewMode = 'table';
            gridView.style.display = 'none';
            tableView.style.display = 'block';
            toggleBtn.innerHTML = '<i class="fas fa-th-large"></i> عرض البطاقات';
        } else {
            this.viewMode = 'grid';
            gridView.style.display = 'block';
            tableView.style.display = 'none';
            toggleBtn.innerHTML = '<i class="fas fa-table"></i> عرض الجدول';
        }

        this.renderCustomers();
    }

    showAddCustomerModal() {
        const modalContent = `
            <div class="modal">
                <div class="modal-header">
                    <h3 class="modal-title">إضافة عميل جديد</h3>
                    <button class="modal-close">&times;</button>
                </div>
                <div class="modal-body">
                    <form id="customerForm">
                        <div class="form-group">
                            <label class="form-label">اسم العميل/الشركة *</label>
                            <input type="text" class="form-input" name="name" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">نوع العميل *</label>
                            <select class="form-select" name="type" required>
                                <option value="">اختر النوع</option>
                                <option value="individual">فرد</option>
                                <option value="company">شركة</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">رقم الهاتف الرئيسي</label>
                            <input type="tel" class="form-input" name="phone">
                        </div>
                        <div class="form-group">
                            <label class="form-label">رقم هاتف إضافي</label>
                            <input type="tel" class="form-input" name="secondaryPhone">
                        </div>
                        <div class="form-group">
                            <label class="form-label">المحافظة/المدينة</label>
                            <select class="form-select" name="city">
                                <option value="">اختر المحافظة</option>
                                <option value="بغداد">بغداد</option>
                                <option value="البصرة">البصرة</option>
                                <option value="أربيل">أربيل</option>
                                <option value="النجف">النجف</option>
                                <option value="كربلاء">كربلاء</option>
                                <option value="الموصل">الموصل</option>
                                <option value="السليمانية">السليمانية</option>
                                <option value="دهوك">دهوك</option>
                                <option value="الأنبار">الأنبار</option>
                                <option value="بابل">بابل</option>
                                <option value="ديالى">ديالى</option>
                                <option value="ذي قار">ذي قار</option>
                                <option value="المثنى">المثنى</option>
                                <option value="القادسية">القادسية</option>
                                <option value="واسط">واسط</option>
                                <option value="ميسان">ميسان</option>
                                <option value="كركوك">كركوك</option>
                                <option value="صلاح الدين">صلاح الدين</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">العنوان التفصيلي</label>
                            <textarea class="form-textarea" name="address"></textarea>
                        </div>
                        <div class="form-group">
                            <label class="form-label">البريد الإلكتروني</label>
                            <input type="email" class="form-input" name="email">
                        </div>
                        <div class="form-group">
                            <label class="form-label">حالة العميل</label>
                            <select class="form-select" name="status">
                                <option value="active">نشط</option>
                                <option value="inactive">غير نشط</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">ملاحظات خاصة</label>
                            <textarea class="form-textarea" name="notes"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn-secondary modal-close">إلغاء</button>
                    <button type="button" class="btn-primary" onclick="customersManager.saveCustomer()">حفظ العميل</button>
                </div>
            </div>
        `;

        window.app.showModal(modalContent);
    }

    async saveCustomer() {
        const form = document.getElementById('customerForm');
        const formData = new FormData(form);

        if (!form.checkValidity()) {
            form.reportValidity();
            return;
        }

        const customerData = {
            name: formData.get('name'),
            type: formData.get('type'),
            phone: formData.get('phone') || '',
            secondaryPhone: formData.get('secondaryPhone') || '',
            city: formData.get('city') || '',
            address: formData.get('address') || '',
            email: formData.get('email') || '',
            status: formData.get('status') || 'active',
            notes: formData.get('notes') || '',
            createdAt: new Date().toISOString()
        };

        try {
            if (this.currentEditingCustomer) {
                customerData.id = this.currentEditingCustomer.id;
                customerData.createdAt = this.currentEditingCustomer.createdAt;
                await storage.update('customers', customerData);
                window.app.showNotification('تم تحديث العميل بنجاح', 'success');
            } else {
                await storage.add('customers', customerData);
                window.app.showNotification('تم إضافة العميل بنجاح', 'success');
            }

            this.currentEditingCustomer = null;
            window.app.closeModal();
            await this.loadCustomers();
        } catch (error) {
            console.error('Error saving customer:', error);
            window.app.showNotification('خطأ في حفظ العميل', 'error');
        }
    }

    async editCustomer(customerId) {
        try {
            const customer = await storage.getById('customers', customerId);
            if (!customer) {
                window.app.showNotification('العميل غير موجود', 'error');
                return;
            }

            this.currentEditingCustomer = customer;
            this.showAddCustomerModal();

            // Fill form with customer data
            setTimeout(() => {
                const form = document.getElementById('customerForm');
                if (form) {
                    form.name.value = customer.name;
                    form.type.value = customer.type;
                    form.phone.value = customer.phone || '';
                    form.secondaryPhone.value = customer.secondaryPhone || '';
                    form.city.value = customer.city || '';
                    form.address.value = customer.address || '';
                    form.email.value = customer.email || '';
                    form.status.value = customer.status;
                    form.notes.value = customer.notes || '';
                }

                // Update modal title
                const modalTitle = document.querySelector('.modal-title');
                if (modalTitle) {
                    modalTitle.textContent = 'تعديل العميل';
                }
            }, 100);

        } catch (error) {
            console.error('Error loading customer for edit:', error);
            window.app.showNotification('خطأ في تحميل العميل', 'error');
        }
    }

    async deleteCustomer(customerId) {
        if (!confirm('هل أنت متأكد من حذف هذا العميل؟\nسيتم إلغاء ربطه بجميع الطلبات المرتبطة به.')) {
            return;
        }

        try {
            // Remove customer from all orders
            const orders = await storage.getAll('orders');
            const customerOrders = orders.filter(order => order.customerId == customerId);

            for (const order of customerOrders) {
                order.customerId = null;
                await storage.update('orders', order);
            }

            // Delete customer
            await storage.delete('customers', customerId);
            window.app.showNotification('تم حذف العميل بنجاح', 'success');
            await this.loadCustomers();
        } catch (error) {
            console.error('Error deleting customer:', error);
            window.app.showNotification('خطأ في حذف العميل', 'error');
        }
    }

    async viewCustomerOrders(customerId) {
        try {
            const customer = await storage.getById('customers', customerId);
            const orders = await storage.getAll('orders');
            const customerOrders = orders.filter(order => order.customerId == customerId);

            const modalContent = `
                <div class="modal" style="max-width: 900px;">
                    <div class="modal-header">
                        <h3 class="modal-title">طلبات العميل: ${customer.name}</h3>
                        <button class="modal-close">&times;</button>
                    </div>
                    <div class="modal-body">
                        ${customerOrders.length === 0 ?
                            '<p class="text-center">لا توجد طلبات لهذا العميل</p>' :
                            `<div class="table-container">
                                <table class="data-table">
                                    <thead>
                                        <tr>
                                            <th>رقم الطلب</th>
                                            <th>اسم الزبون</th>
                                            <th>المحافظة</th>
                                            <th>السعر</th>
                                            <th>الحالة</th>
                                            <th>التاريخ</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        ${customerOrders.map(order => `
                                            <tr>
                                                <td>#${order.id}</td>
                                                <td>${order.customerName}</td>
                                                <td>${order.city}</td>
                                                <td>${order.deliveryPrice} د.ع</td>
                                                <td>
                                                    <span class="status-badge status-${order.status}">
                                                        ${this.getStatusText(order.status)}
                                                    </span>
                                                </td>
                                                <td>${this.formatDate(order.date)}</td>
                                            </tr>
                                        `).join('')}
                                    </tbody>
                                </table>
                            </div>`
                        }

                        <div class="customer-summary">
                            <h4>ملخص العميل:</h4>
                            <div class="summary-stats">
                                <div class="summary-item">
                                    <span>إجمالي الطلبات:</span>
                                    <strong>${customerOrders.length}</strong>
                                </div>
                                <div class="summary-item">
                                    <span>طلبات مسلمة:</span>
                                    <strong>${customerOrders.filter(o => o.status === 'delivered').length}</strong>
                                </div>
                                <div class="summary-item">
                                    <span>طلبات راجعة:</span>
                                    <strong>${customerOrders.filter(o => o.status === 'returned' || o.status === 'partial_return').length}</strong>
                                </div>
                                <div class="summary-item">
                                    <span>إجمالي المبلغ:</span>
                                    <strong>${customerOrders.filter(o => o.status === 'delivered').reduce((sum, order) =>
                                        sum + order.deliveryPrice, 0).toLocaleString()} د.ع</strong>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn-secondary modal-close">إغلاق</button>
                        <button type="button" class="btn-primary" onclick="customersManager.exportCustomerReport(${customerId})">
                            <i class="fas fa-download"></i>
                            تصدير تقرير العميل
                        </button>
                    </div>
                </div>
            `;

            window.app.showModal(modalContent);
        } catch (error) {
            console.error('Error loading customer orders:', error);
            window.app.showNotification('خطأ في تحميل طلبات العميل', 'error');
        }
    }

    handleSearch(searchTerm) {
        if (!searchTerm.trim()) {
            this.filteredCustomers = [...this.customers];
        } else {
            const term = searchTerm.toLowerCase();
            this.filteredCustomers = this.customers.filter(customer =>
                customer.name.toLowerCase().includes(term) ||
                (customer.phone && customer.phone.includes(term)) ||
                (customer.email && customer.email.toLowerCase().includes(term)) ||
                (customer.city && customer.city.toLowerCase().includes(term))
            );
        }
        this.renderCustomers();
    }

    applyFilters() {
        const typeFilter = document.getElementById('customerTypeFilter').value;
        const statusFilter = document.getElementById('customerStatusFilter').value;
        const cityFilter = document.getElementById('customerCityFilter').value;

        this.filteredCustomers = this.customers.filter(customer => {
            let matches = true;

            if (typeFilter && customer.type !== typeFilter) {
                matches = false;
            }

            if (statusFilter && customer.status !== statusFilter) {
                matches = false;
            }

            if (cityFilter && customer.city !== cityFilter) {
                matches = false;
            }

            return matches;
        });

        this.renderCustomers();
    }

    updateCityFilter() {
        const cityFilter = document.getElementById('customerCityFilter');
        if (!cityFilter) return;

        const cities = [...new Set(this.customers.map(customer => customer.city).filter(city => city))].sort();
        cityFilter.innerHTML = '<option value="">جميع المحافظات</option>' +
            cities.map(city => `<option value="${city}">${city}</option>`).join('');
    }

    async exportCustomerReport(customerId) {
        try {
            const customer = await storage.getById('customers', customerId);
            const orders = await storage.getAll('orders');
            const customerOrders = orders.filter(order => order.customerId == customerId);

            let csvContent = 'رقم الطلب,اسم الزبون,المحافظة,السعر,الحالة,التاريخ\n';

            customerOrders.forEach(order => {
                csvContent += `${order.id},"${order.customerName}","${order.city}",${order.deliveryPrice},"${this.getStatusText(order.status)}","${this.formatDate(order.date)}"\n`;
            });

            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', `تقرير-العميل-${customer.name}-${new Date().toISOString().split('T')[0]}.csv`);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            window.app.showNotification('تم تصدير تقرير العميل بنجاح', 'success');
        } catch (error) {
            console.error('Error exporting customer report:', error);
            window.app.showNotification('خطأ في تصدير التقرير', 'error');
        }
    }

    getStatusText(status) {
        const statusMap = {
            'delivered': 'مسلم',
            'pending': 'معلق',
            'returned': 'راجع',
            'partial_return': 'راجع جزئي'
        };
        return statusMap[status] || status;
    }

    formatDate(dateString) {
        return new Date(dateString).toLocaleDateString('ar-IQ');
    }
}

// Initialize customers manager
document.addEventListener('DOMContentLoaded', () => {
    window.customersManager = new CustomersManager();
});
