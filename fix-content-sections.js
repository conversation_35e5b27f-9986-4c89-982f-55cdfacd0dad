// إصلاح سريع لمشكلة content-section
// يمكن تشغيل هذا الملف في Console أو إضافته للصفحة

(function() {
    'use strict';
    
    console.log('🔧 بدء إصلاح مشكلة content-section...');
    
    // دالة إصلاح شاملة
    function fixContentSections() {
        try {
            // 1. إزالة جميع الأنماط المتضاربة
            const allSections = document.querySelectorAll('.content-section');
            console.log(`📄 تم العثور على ${allSections.length} أقسام`);
            
            allSections.forEach(section => {
                // إزالة جميع الكلاسات والأنماط
                section.classList.remove('active', 'fade-in', 'fade-out');
                section.removeAttribute('style');
                
                // إضافة الأنماط الأساسية
                section.style.display = 'none';
                section.style.opacity = '0';
                section.style.visibility = 'hidden';
            });
            
            // 2. إظهار لوحة التحكم كافتراضي
            const dashboard = document.getElementById('dashboard');
            if (dashboard) {
                dashboard.classList.add('active');
                dashboard.style.display = 'block';
                dashboard.style.opacity = '1';
                dashboard.style.visibility = 'visible';
                console.log('✅ تم إظهار لوحة التحكم');
            } else {
                console.error('❌ لم يتم العثور على لوحة التحكم');
            }
            
            // 3. إضافة CSS إضافي للإصلاح
            addFixCSS();
            
            // 4. إصلاح دالة showSection
            fixShowSectionFunction();
            
            console.log('✅ تم إصلاح مشكلة content-section بنجاح');
            
        } catch (error) {
            console.error('❌ خطأ في إصلاح content-section:', error);
        }
    }
    
    // إضافة CSS إضافي للإصلاح
    function addFixCSS() {
        const existingStyle = document.getElementById('content-section-fix');
        if (existingStyle) {
            existingStyle.remove();
        }
        
        const style = document.createElement('style');
        style.id = 'content-section-fix';
        style.textContent = `
            /* إصلاح content-section */
            .content-section {
                display: none !important;
                opacity: 0 !important;
                visibility: hidden !important;
                padding: 2rem;
                transition: all 0.3s ease;
            }
            
            .content-section.active {
                display: block !important;
                opacity: 1 !important;
                visibility: visible !important;
            }
            
            /* إصلاح محدد لكل قسم */
            #dashboard.content-section,
            #orders.content-section,
            #drivers.content-section,
            #assignment.content-section,
            #customers.content-section,
            #reports.content-section,
            #accounting.content-section,
            #settings.content-section {
                display: none !important;
            }
            
            #dashboard.content-section.active,
            #orders.content-section.active,
            #drivers.content-section.active,
            #assignment.content-section.active,
            #customers.content-section.active,
            #reports.content-section.active,
            #accounting.content-section.active,
            #settings.content-section.active {
                display: block !important;
                opacity: 1 !important;
                visibility: visible !important;
            }
        `;
        
        document.head.appendChild(style);
        console.log('✅ تم إضافة CSS الإصلاح');
    }
    
    // إصلاح دالة showSection
    function fixShowSectionFunction() {
        window.showSectionFixed = function(sectionName) {
            console.log(`🔄 عرض القسم: ${sectionName}`);
            
            try {
                // إخفاء جميع الأقسام
                document.querySelectorAll('.content-section').forEach(section => {
                    section.classList.remove('active');
                    section.style.display = 'none';
                    section.style.opacity = '0';
                    section.style.visibility = 'hidden';
                });
                
                // إظهار القسم المطلوب
                const targetSection = document.getElementById(sectionName);
                if (targetSection) {
                    targetSection.classList.add('active');
                    targetSection.style.display = 'block';
                    targetSection.style.opacity = '1';
                    targetSection.style.visibility = 'visible';
                    
                    // تحديث التنقل
                    updateNavigation(sectionName);
                    
                    console.log(`✅ تم عرض القسم ${sectionName}`);
                } else {
                    console.error(`❌ القسم ${sectionName} غير موجود`);
                }
                
            } catch (error) {
                console.error('❌ خطأ في عرض القسم:', error);
            }
        };
        
        // تحديث التنقل
        function updateNavigation(sectionName) {
            // إزالة active من جميع روابط التنقل
            document.querySelectorAll('.nav-link').forEach(link => {
                link.classList.remove('active');
            });
            
            // إضافة active للرابط الحالي
            const activeLink = document.querySelector(`[data-section="${sectionName}"]`);
            if (activeLink) {
                activeLink.classList.add('active');
            }
        }
        
        console.log('✅ تم إصلاح دالة showSection');
    }
    
    // دالة اختبار شاملة
    function testSections() {
        console.log('🧪 بدء اختبار الأقسام...');
        
        const sections = ['dashboard', 'orders', 'drivers', 'customers', 'reports'];
        let index = 0;
        
        function testNext() {
            if (index < sections.length) {
                const section = sections[index];
                console.log(`🎯 اختبار: ${section}`);
                window.showSectionFixed(section);
                index++;
                setTimeout(testNext, 1000);
            } else {
                console.log('✅ انتهى الاختبار - العودة للوحة التحكم');
                window.showSectionFixed('dashboard');
            }
        }
        
        testNext();
    }
    
    // دالة فحص حالة الأقسام
    function debugSections() {
        console.log('🔍 فحص حالة الأقسام:');
        
        document.querySelectorAll('.content-section').forEach(section => {
            const styles = window.getComputedStyle(section);
            const isActive = section.classList.contains('active');
            
            console.log(`📄 ${section.id}:`, {
                active: isActive,
                display: styles.display,
                opacity: styles.opacity,
                visibility: styles.visibility
            });
        });
    }
    
    // إضافة الدوال للنافذة العامة
    window.fixContentSections = fixContentSections;
    window.testSections = testSections;
    window.debugSections = debugSections;
    
    // تشغيل الإصلاح تلقائياً
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', fixContentSections);
    } else {
        fixContentSections();
    }
    
    // إضافة أزرار تشخيص إذا كان في وضع debug
    if (window.location.search.includes('debug') || window.location.search.includes('fix')) {
        setTimeout(() => {
            addDebugButtons();
        }, 1000);
    }
    
    function addDebugButtons() {
        const existingButtons = document.getElementById('debug-buttons');
        if (existingButtons) return;
        
        const buttonContainer = document.createElement('div');
        buttonContainer.id = 'debug-buttons';
        buttonContainer.style.cssText = `
            position: fixed;
            top: 10px;
            left: 10px;
            z-index: 10000;
            display: flex;
            gap: 5px;
            flex-direction: column;
        `;
        
        const buttons = [
            { text: '🔧 إصلاح', action: 'fixContentSections()' },
            { text: '🧪 اختبار', action: 'testSections()' },
            { text: '🔍 فحص', action: 'debugSections()' },
            { text: '🏠 الرئيسية', action: 'showSectionFixed("dashboard")' }
        ];
        
        buttons.forEach(btn => {
            const button = document.createElement('button');
            button.textContent = btn.text;
            button.onclick = () => eval(btn.action);
            button.style.cssText = `
                padding: 5px 10px;
                background: #007bff;
                color: white;
                border: none;
                border-radius: 4px;
                cursor: pointer;
                font-size: 12px;
            `;
            buttonContainer.appendChild(button);
        });
        
        document.body.appendChild(buttonContainer);
        console.log('✅ تم إضافة أزرار التشخيص');
    }
    
    console.log('🎯 الأوامر المتاحة:');
    console.log('  - fixContentSections(): إصلاح الأقسام');
    console.log('  - showSectionFixed(name): عرض قسم معين');
    console.log('  - testSections(): اختبار جميع الأقسام');
    console.log('  - debugSections(): فحص حالة الأقسام');
    
})();
