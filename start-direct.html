<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تشغيل نظام إدارة التوصيل</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .container {
            background: white;
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.2);
            max-width: 600px;
            text-align: center;
        }
        
        .logo {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 2rem;
        }
        
        h1 {
            color: #333;
            margin-bottom: 10px;
        }
        
        .subtitle {
            color: #666;
            margin-bottom: 30px;
        }
        
        .options {
            display: grid;
            gap: 20px;
            margin: 30px 0;
        }
        
        .option {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 15px;
            border: 2px solid transparent;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .option:hover {
            border-color: #667eea;
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.2);
        }
        
        .option.recommended {
            border-color: #28a745;
            background: #f8fff9;
        }
        
        .option-title {
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        
        .option-desc {
            color: #666;
            font-size: 0.9rem;
            margin-bottom: 15px;
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }
        
        .btn.success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }
        
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 10px;
            margin: 20px 0;
            color: #856404;
        }
        
        .info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            padding: 15px;
            border-radius: 10px;
            margin: 20px 0;
            color: #0c5460;
        }
        
        .status {
            margin: 20px 0;
            padding: 15px;
            border-radius: 10px;
            display: none;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">
            🚚
        </div>
        
        <h1>نظام إدارة شركة التوصيل</h1>
        <p class="subtitle">اختر طريقة التشغيل المناسبة</p>
        
        <div class="options">
            <!-- Option 1: With Server (Recommended) -->
            <div class="option recommended" onclick="startWithServer()">
                <div class="option-title">🚀 تشغيل مع خادم محلي (موصى به)</div>
                <div class="option-desc">
                    يتطلب Python - جميع الميزات تعمل بشكل كامل
                </div>
                <button class="btn success">تشغيل مع خادم</button>
            </div>
            
            <!-- Option 2: Direct File -->
            <div class="option" onclick="startDirect()">
                <div class="option-title">📁 فتح مباشر (بدون خادم)</div>
                <div class="option-desc">
                    فتح الملفات مباشرة - قد لا تعمل بعض الميزات
                </div>
                <button class="btn">فتح مباشر</button>
            </div>
            
            <!-- Option 3: Test Page -->
            <div class="option" onclick="openTestPage()">
                <div class="option-title">🧪 صفحة الاختبار</div>
                <div class="option-desc">
                    لاختبار النظام وحل المشاكل
                </div>
                <button class="btn">صفحة الاختبار</button>
            </div>
        </div>
        
        <div class="warning">
            ⚠️ <strong>ملاحظة:</strong> للحصول على أفضل تجربة، استخدم الخيار الأول مع خادم محلي
        </div>
        
        <div class="info">
            💡 <strong>نصيحة:</strong> إذا لم يعمل الخادم، جرب فتح الملفات مباشرة أو استخدم VS Code مع Live Server
        </div>
        
        <div id="status" class="status"></div>
        
        <div style="margin-top: 30px;">
            <h3>روابط مفيدة:</h3>
            <a href="login.html" class="btn" style="margin: 5px;">صفحة تسجيل الدخول</a>
            <a href="test-login.html" class="btn" style="margin: 5px;">اختبار تسجيل الدخول</a>
            <a href="SETUP.md" class="btn" style="margin: 5px;">دليل التشغيل</a>
        </div>
    </div>

    <script>
        function showStatus(message, type) {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = `status ${type}`;
            status.style.display = 'block';
            
            if (type === 'success') {
                setTimeout(() => {
                    status.style.display = 'none';
                }, 5000);
            }
        }
        
        function startWithServer() {
            showStatus('جاري محاولة تشغيل الخادم...', 'success');
            
            // Try to run the batch file
            try {
                // This won't work in browser, but we'll show instructions
                setTimeout(() => {
                    showStatus('يرجى تشغيل ملف start-simple.bat أو start-server.bat', 'error');
                }, 2000);
            } catch (error) {
                showStatus('يرجى تشغيل ملف start-simple.bat يدوياً', 'error');
            }
        }
        
        function startDirect() {
            showStatus('جاري فتح صفحة تسجيل الدخول...', 'success');
            setTimeout(() => {
                window.location.href = 'login.html';
            }, 1000);
        }
        
        function openTestPage() {
            showStatus('جاري فتح صفحة الاختبار...', 'success');
            setTimeout(() => {
                window.location.href = 'test-login.html';
            }, 1000);
        }
        
        // Check if we're running on a server
        window.addEventListener('load', () => {
            if (location.protocol === 'file:') {
                showStatus('تم فتح الملف مباشرة. للحصول على أفضل تجربة، استخدم خادم محلي.', 'error');
            } else {
                showStatus('✅ يعمل على خادم محلي - جميع الميزات متاحة', 'success');
            }
        });
    </script>
</body>
</html>
