<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نموذج التنقل المحسن - نظام إدارة التوصيل</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #e0e5ec;
            direction: rtl;
            overflow-x: hidden;
        }

        /* Enhanced Neumorphic Variables */
        :root {
            --primary-color: #667eea;
            --bg-primary: #e0e5ec;
            --bg-secondary: #f0f2f5;
            --text-primary: #2d3748;
            --text-secondary: #4a5568;
            --shadow-light: rgba(255, 255, 255, 0.8);
            --shadow-dark: rgba(163, 177, 198, 0.8);
            --transition: all 0.3s ease-in-out;
        }

        /* Container */
        .app-container {
            display: flex;
            min-height: 100vh;
        }

        /* Enhanced Sidebar */
        .sidebar {
            width: 280px;
            background: var(--bg-primary);
            box-shadow: 
                8px 0 16px var(--shadow-dark),
                -8px 0 16px var(--shadow-light);
            transition: var(--transition);
            position: relative;
            z-index: 100;
        }

        .sidebar-header {
            padding: 2rem;
            text-align: center;
            border-bottom: 1px solid rgba(163, 177, 198, 0.2);
        }

        .logo {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, var(--primary-color), #764ba2);
            border-radius: 50%;
            margin: 0 auto 1rem;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            box-shadow: 
                4px 4px 8px var(--shadow-dark),
                -4px -4px 8px var(--shadow-light);
        }

        .logo-text {
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--text-primary);
        }

        /* Enhanced Navigation */
        .nav-menu {
            padding: 1rem;
        }

        .nav-item {
            margin-bottom: 0.5rem;
        }

        .nav-link {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem 1.5rem;
            border-radius: 12px;
            text-decoration: none;
            color: var(--text-secondary);
            transition: var(--transition);
            background: var(--bg-primary);
            box-shadow: 
                4px 4px 8px var(--shadow-dark),
                -4px -4px 8px var(--shadow-light);
            position: relative;
            overflow: hidden;
        }

        .nav-link::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .nav-link:hover::before {
            left: 100%;
        }

        .nav-link:hover {
            color: var(--primary-color);
            transform: translateY(-2px);
            box-shadow: 
                6px 6px 12px var(--shadow-dark),
                -6px -6px 12px var(--shadow-light);
        }

        .nav-link.active {
            background: linear-gradient(135deg, var(--primary-color), #764ba2);
            color: white;
            box-shadow: 
                inset 2px 2px 4px rgba(0,0,0,0.2),
                inset -2px -2px 4px rgba(255,255,255,0.1);
        }

        .nav-link.active:hover {
            transform: translateY(-1px);
        }

        .nav-icon {
            font-size: 1.2rem;
            width: 20px;
            text-align: center;
        }

        /* Main Content */
        .main-content {
            flex: 1;
            background: var(--bg-secondary);
            position: relative;
        }

        /* Enhanced Content Sections */
        .content-section {
            display: none;
            padding: 2rem;
            opacity: 0;
            transform: translateY(20px);
            transition: var(--transition);
        }

        .content-section.active {
            display: block;
            opacity: 1;
            transform: translateY(0);
        }

        .section-header {
            background: var(--bg-primary);
            padding: 2rem;
            border-radius: 16px;
            margin-bottom: 2rem;
            box-shadow: 
                8px 8px 16px var(--shadow-dark),
                -8px -8px 16px var(--shadow-light);
        }

        .section-title {
            font-size: 2rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }

        .section-subtitle {
            color: var(--text-secondary);
            font-size: 1.1rem;
        }

        .section-content {
            background: var(--bg-primary);
            padding: 2rem;
            border-radius: 16px;
            box-shadow: 
                8px 8px 16px var(--shadow-dark),
                -8px -8px 16px var(--shadow-light);
            min-height: 400px;
        }

        /* Enhanced Buttons */
        .btn {
            padding: 0.8rem 1.5rem;
            border: none;
            border-radius: 10px;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition);
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            text-decoration: none;
            box-shadow: 
                4px 4px 8px var(--shadow-dark),
                -4px -4px 8px var(--shadow-light);
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color), #764ba2);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 
                6px 6px 12px var(--shadow-dark),
                -6px -6px 12px var(--shadow-light);
        }

        .btn:active {
            transform: translateY(0);
            box-shadow: 
                inset 2px 2px 4px var(--shadow-dark),
                inset -2px -2px 4px var(--shadow-light);
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            .sidebar {
                position: fixed;
                left: 0;
                top: 0;
                height: 100vh;
                transform: translateX(-100%);
                z-index: 1000;
            }

            .sidebar.open {
                transform: translateX(0);
            }

            .main-content {
                margin-right: 0;
            }

            .mobile-toggle {
                display: block;
                position: fixed;
                top: 1rem;
                right: 1rem;
                z-index: 1001;
                background: var(--bg-primary);
                border: none;
                padding: 0.8rem;
                border-radius: 50%;
                box-shadow: 
                    4px 4px 8px var(--shadow-dark),
                    -4px -4px 8px var(--shadow-light);
                cursor: pointer;
            }
        }

        .mobile-toggle {
            display: none;
        }

        /* Loading Animation */
        .loading {
            position: relative;
            overflow: hidden;
        }

        .loading::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            animation: loading 1.5s infinite;
        }

        @keyframes loading {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        /* Success Animation */
        .success-animation {
            animation: successPulse 0.6s ease-out;
        }

        @keyframes successPulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
    </style>
</head>
<body>
    <div class="app-container">
        <!-- Enhanced Sidebar -->
        <aside class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <i class="fas fa-shipping-fast"></i>
                </div>
                <div class="logo-text">نظام إدارة التوصيل</div>
            </div>
            
            <nav class="nav-menu">
                <div class="nav-item">
                    <a href="#" class="nav-link active" data-section="dashboard">
                        <i class="nav-icon fas fa-tachometer-alt"></i>
                        <span>لوحة التحكم</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="#" class="nav-link" data-section="orders">
                        <i class="nav-icon fas fa-box"></i>
                        <span>الطلبات</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="#" class="nav-link" data-section="drivers">
                        <i class="nav-icon fas fa-users"></i>
                        <span>المندوبين</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="#" class="nav-link" data-section="customers">
                        <i class="nav-icon fas fa-user-friends"></i>
                        <span>العملاء</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="#" class="nav-link" data-section="reports">
                        <i class="nav-icon fas fa-chart-bar"></i>
                        <span>التقارير</span>
                    </a>
                </div>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Mobile Toggle -->
            <button class="mobile-toggle" id="mobileToggle">
                <i class="fas fa-bars"></i>
            </button>

            <!-- Dashboard Section -->
            <section class="content-section active" id="dashboard">
                <div class="section-header">
                    <h1 class="section-title">لوحة التحكم</h1>
                    <p class="section-subtitle">نظرة عامة على النظام والإحصائيات</p>
                </div>
                <div class="section-content">
                    <h3>مرحباً بك في لوحة التحكم</h3>
                    <p>هذا هو القسم الرئيسي للنظام حيث يمكنك مراقبة جميع العمليات.</p>
                    <button class="btn btn-primary">
                        <i class="fas fa-plus"></i>
                        إضافة طلب جديد
                    </button>
                </div>
            </section>

            <!-- Orders Section -->
            <section class="content-section" id="orders">
                <div class="section-header">
                    <h1 class="section-title">إدارة الطلبات</h1>
                    <p class="section-subtitle">عرض وإدارة جميع طلبات التوصيل</p>
                </div>
                <div class="section-content">
                    <h3>قائمة الطلبات</h3>
                    <p>هنا يمكنك إدارة جميع الطلبات وتتبع حالتها.</p>
                </div>
            </section>

            <!-- Drivers Section -->
            <section class="content-section" id="drivers">
                <div class="section-header">
                    <h1 class="section-title">إدارة المندوبين</h1>
                    <p class="section-subtitle">إدارة فريق التوصيل والعمولات</p>
                </div>
                <div class="section-content">
                    <h3>قائمة المندوبين</h3>
                    <p>إدارة المندوبين وحساب العمولات والأداء.</p>
                </div>
            </section>

            <!-- Customers Section -->
            <section class="content-section" id="customers">
                <div class="section-header">
                    <h1 class="section-title">إدارة العملاء</h1>
                    <p class="section-subtitle">قاعدة بيانات العملاء والشركات</p>
                </div>
                <div class="section-content">
                    <h3>قائمة العملاء</h3>
                    <p>إدارة بيانات العملاء والشركات المتعاملة.</p>
                </div>
            </section>

            <!-- Reports Section -->
            <section class="content-section" id="reports">
                <div class="section-header">
                    <h1 class="section-title">التقارير والإحصائيات</h1>
                    <p class="section-subtitle">تقارير مفصلة عن الأداء والمبيعات</p>
                </div>
                <div class="section-content">
                    <h3>التقارير المالية</h3>
                    <p>عرض التقارير والإحصائيات المختلفة للنظام.</p>
                </div>
            </section>
        </main>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/js/all.min.js"></script>
    <script>
        class EnhancedNavigation {
            constructor() {
                this.currentSection = 'dashboard';
                this.isTransitioning = false;
                this.init();
            }

            init() {
                this.setupNavigation();
                this.setupMobileToggle();
                console.log('✅ Enhanced Navigation initialized');
            }

            setupNavigation() {
                const navLinks = document.querySelectorAll('.nav-link');
                
                navLinks.forEach(link => {
                    link.addEventListener('click', (e) => {
                        e.preventDefault();
                        const section = link.dataset.section;
                        
                        if (section && section !== this.currentSection) {
                            this.showSection(section);
                        }
                    });
                });
            }

            setupMobileToggle() {
                const mobileToggle = document.getElementById('mobileToggle');
                const sidebar = document.getElementById('sidebar');
                
                if (mobileToggle && sidebar) {
                    mobileToggle.addEventListener('click', () => {
                        sidebar.classList.toggle('open');
                    });

                    // Close sidebar when clicking outside on mobile
                    document.addEventListener('click', (e) => {
                        if (window.innerWidth <= 768 && 
                            !sidebar.contains(e.target) && 
                            !mobileToggle.contains(e.target)) {
                            sidebar.classList.remove('open');
                        }
                    });
                }
            }

            showSection(sectionName) {
                if (this.isTransitioning) return;
                
                console.log(`🔄 Navigating to: ${sectionName}`);
                this.isTransitioning = true;

                // Update navigation
                this.updateNavigation(sectionName);

                // Hide current section
                const currentSection = document.querySelector('.content-section.active');
                if (currentSection) {
                    currentSection.style.opacity = '0';
                    currentSection.style.transform = 'translateY(20px)';
                    
                    setTimeout(() => {
                        currentSection.classList.remove('active');
                        this.showTargetSection(sectionName);
                    }, 200);
                } else {
                    this.showTargetSection(sectionName);
                }
            }

            showTargetSection(sectionName) {
                const targetSection = document.getElementById(sectionName);
                
                if (targetSection) {
                    // Reset styles
                    targetSection.style.opacity = '0';
                    targetSection.style.transform = 'translateY(20px)';
                    targetSection.classList.add('active');
                    
                    // Force reflow
                    targetSection.offsetHeight;
                    
                    // Animate in
                    setTimeout(() => {
                        targetSection.style.opacity = '1';
                        targetSection.style.transform = 'translateY(0)';
                        
                        // Add success animation
                        targetSection.classList.add('success-animation');
                        setTimeout(() => {
                            targetSection.classList.remove('success-animation');
                        }, 600);
                    }, 50);
                    
                    this.currentSection = sectionName;
                    console.log(`✅ Section ${sectionName} activated`);
                } else {
                    console.error(`❌ Section ${sectionName} not found`);
                }
                
                // Reset transition flag
                setTimeout(() => {
                    this.isTransitioning = false;
                }, 300);
                
                // Close mobile sidebar
                if (window.innerWidth <= 768) {
                    document.getElementById('sidebar').classList.remove('open');
                }
            }

            updateNavigation(sectionName) {
                // Remove active class from all nav links
                document.querySelectorAll('.nav-link').forEach(link => {
                    link.classList.remove('active');
                });
                
                // Add active class to current nav link
                const activeLink = document.querySelector(`[data-section="${sectionName}"]`);
                if (activeLink) {
                    activeLink.classList.add('active');
                }
            }
        }

        // Initialize when DOM is loaded
        document.addEventListener('DOMContentLoaded', () => {
            new EnhancedNavigation();
        });
    </script>
</body>
</html>
