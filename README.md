# 📦 نظام إدارة شركة التوصيل

نظام محاسبة وإدارة متكامل لشركات التوصيل مع واجهة عربية كاملة وتصميم نيومورفيك حديث.

## ✨ المميزات الرئيسية

- **واجهة عربية بالكامل (RTL)** - دعم كامل للغة العربية مع اتجاه النص من اليمين لليسار
- **تصميم نيومورفيك حديث** - واجهة مستخدم عصرية وجذابة
- **تصميم متجاوب** - يعمل على جميع الأجهزة (كمبيوتر، تابلت، موبايل)
- **دعم عملة الدينار العراقي** - نظام محاسبة مخصص للسوق العراقي
- **تخزين محلي متقدم** - استخدام IndexedDB مع إمكانية النسخ الاحتياطي
- **تقارير قابلة للطباعة والتصدير** - تقارير شاملة بصيغ مختلفة

## 🧩 الوحدات الأساسية

### 1. 📝 وحدة إدارة الطلبات
- إضافة وتعديل الطلبات مع جميع البيانات المطلوبة
- إدارة حالات الطلبات (مسلم، معلق، راجع، راجع جزئي)
- بحث وفلترة متقدمة للطلبات
- ربط الطلبات بالمندوبين

### 2. 👤 وحدة إدارة المندوبين
- إضافة وإدارة بيانات المندوبين
- تحديد نسب العمولة لكل مندوب
- متابعة أداء المندوبين وإحصائياتهم
- حساب العمولات المستحقة تلقائياً

### 3. 📊 وحدة التقارير المالية
- تقارير شاملة للشركات والمندوبين
- تقارير حسب الفترة الزمنية
- إحصائيات مفصلة عن الأداء
- إمكانية الطباعة والتصدير (PDF, Excel)

### 4. 💰 وحدة المحاسبة
- حساب المستحقات والعمولات تلقائياً
- متابعة أرصدة الشركات والمندوبين
- إدارة المدفوعات والمعاملات المالية
- حساب صافي الربح

### 5. 🔍 وحدة البحث والفلترة
- بحث متقدم في جميع البيانات
- فلترة حسب معايير متعددة
- نتائج فورية ومرتبة

## 🚀 كيفية التشغيل

### متطلبات النظام
- متصفح ويب حديث (Chrome, Firefox, Safari, Edge)
- خادم ويب محلي (اختياري للتطوير)

### التشغيل المحلي
1. قم بتحميل الملفات
2. افتح ملف `index.html` في المتصفح مباشرة
   أو
3. استخدم خادم ويب محلي:
   ```bash
   python -m http.server 8000
   ```
   ثم افتح `http://localhost:8000`

### البيانات التجريبية
- عند التشغيل لأول مرة، ستجد أزرار لتحميل بيانات تجريبية
- يمكنك استخدام هذه البيانات لاختبار النظام
- يمكن حذف البيانات وإعادة تحميلها في أي وقت

## 📱 كيفية الاستخدام

### إضافة طلب جديد
1. اذهب إلى قسم "إدارة الطلبات"
2. اضغط على "إضافة طلب جديد"
3. املأ جميع البيانات المطلوبة
4. اختر المندوب المناسب
5. احفظ الطلب

### إدارة المندوبين
1. اذهب إلى قسم "إدارة المندوبين"
2. اضغط على "إضافة مندوب جديد"
3. أدخل بيانات المندوب ونسبة العمولة
4. احفظ البيانات

### عرض التقارير
1. اذهب إلى قسم "التقارير"
2. اختر نوع التقرير المطلوب
3. حدد الفترة الزمنية
4. اضغط على "إنشاء التقرير"
5. يمكنك طباعة أو تصدير التقرير

### المحاسبة
1. اذهب إلى قسم "المحاسبة"
2. راجع الملخص المالي
3. تابع أرصدة الشركات والمندوبين
4. أضف المدفوعات عند الحاجة

## 🛠️ التقنيات المستخدمة

- **HTML5** - هيكل الصفحات
- **CSS3** - التصميم والتنسيق
- **JavaScript (ES6+)** - المنطق والتفاعل
- **IndexedDB** - قاعدة البيانات المحلية
- **Font Awesome** - الأيقونات
- **Google Fonts (Cairo)** - الخط العربي

## 📁 هيكل المشروع

```
├── index.html              # الصفحة الرئيسية
├── styles/
│   ├── main.css           # الأنماط الرئيسية
│   └── neumorphic.css     # أنماط التصميم النيومورفيك
├── js/
│   ├── main.js            # الملف الرئيسي للتطبيق
│   ├── storage.js         # إدارة التخزين المحلي
│   ├── orders.js          # وحدة إدارة الطلبات
│   ├── drivers.js         # وحدة إدارة المندوبين
│   ├── reports.js         # وحدة التقارير
│   ├── accounting.js      # وحدة المحاسبة
│   └── sample-data.js     # البيانات التجريبية
└── README.md              # هذا الملف
```

## 🔧 التخصيص والتطوير

### إضافة محافظات جديدة
يمكنك إضافة محافظات جديدة في ملفات:
- `js/orders.js` (في دالة showAddOrderModal)
- `js/drivers.js` (في دالة showAddDriverModal)

### تغيير العملة
يمكنك تغيير العملة الافتراضية من قسم الإعدادات أو تعديل المتغير في الكود.

### إضافة حقول جديدة
يمكنك إضافة حقول جديدة للطلبات أو المندوبين عبر تعديل:
1. نماذج HTML في الملفات المناسبة
2. دوال الحفظ والتحديث
3. جداول العرض

## 🔒 الأمان والخصوصية

- جميع البيانات تُحفظ محلياً في متصفح المستخدم
- لا يتم إرسال أي بيانات لخوادم خارجية
- يمكن عمل نسخ احتياطية وتصديرها
- البيانات محمية بواسطة أمان المتصفح

## 📞 الدعم والمساعدة

### المشاكل الشائعة
- **البيانات لا تظهر**: تأكد من تفعيل JavaScript في المتصفح
- **التصميم لا يظهر بشكل صحيح**: تأكد من تحميل ملفات CSS
- **البحث لا يعمل**: تحقق من وجود بيانات في النظام

### النسخ الاحتياطي
- استخدم خاصية "تصدير البيانات" من قسم الإعدادات
- احفظ الملف المُصدر في مكان آمن
- يمكنك استيراد البيانات في أي وقت

## 🚀 التطوير المستقبلي

### مميزات مخططة
- [ ] نظام المستخدمين والصلاحيات
- [ ] إشعارات وتنبيهات
- [ ] ربط مع خدمات SMS
- [ ] تطبيق موبايل
- [ ] قاعدة بيانات سحابية
- [ ] تقارير متقدمة مع الرسوم البيانية

### المساهمة
نرحب بالمساهمات والاقتراحات لتطوير النظام وإضافة مميزات جديدة.

---

**تم تطوير هذا النظام خصيصاً لشركات التوصيل في العراق مع مراعاة الاحتياجات المحلية والثقافية.**
