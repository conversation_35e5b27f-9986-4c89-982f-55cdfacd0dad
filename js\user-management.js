// User Management and Permissions System
class UserManagementSystem {
    constructor() {
        this.currentUser = null;
        this.userRoles = {
            admin: {
                name: 'مدير النظام',
                permissions: ['all'],
                description: 'صلاحيات كاملة على جميع أجزاء النظام'
            },
            manager: {
                name: 'مدير العمليات',
                permissions: ['orders', 'drivers', 'customers', 'reports', 'assignment'],
                description: 'إدارة العمليات والتقارير'
            },
            accountant: {
                name: 'محاسب',
                permissions: ['reports', 'accounting', 'orders_view'],
                description: 'المحاسبة والتقارير المالية'
            },
            supervisor: {
                name: 'مشرف',
                permissions: ['orders', 'assignment', 'drivers_view'],
                description: 'إشراف على الطلبات والإسناد'
            },
            operator: {
                name: 'مشغ<PERSON>',
                permissions: ['orders_view', 'assignment'],
                description: 'عرض الطلبات والإسناد فقط'
            }
        };
        this.init();
    }

    async init() {
        await this.loadCurrentUser();
        this.setupEventListeners();
        this.renderUserInterface();
    }

    setupEventListeners() {
        // Login/Logout events
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('login-btn')) {
                this.showLoginModal();
            }
            
            if (e.target.classList.contains('logout-btn')) {
                this.logout();
            }
            
            if (e.target.classList.contains('add-user-btn')) {
                this.showAddUserModal();
            }
            
            if (e.target.classList.contains('edit-user-btn')) {
                const userId = e.target.dataset.userId;
                this.showEditUserModal(userId);
            }
            
            if (e.target.classList.contains('delete-user-btn')) {
                const userId = e.target.dataset.userId;
                this.deleteUser(userId);
            }
        });

        // Check permissions on navigation
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('nav-link')) {
                const section = e.target.dataset.section;
                if (!this.hasPermission(section)) {
                    e.preventDefault();
                    window.app.showNotification('ليس لديك صلاحية للوصول إلى هذا القسم', 'error');
                }
            }
        });
    }

    async loadCurrentUser() {
        try {
            // Check for session from login page
            const sessionData = localStorage.getItem('userSession') || sessionStorage.getItem('userSession');

            if (sessionData) {
                const session = JSON.parse(sessionData);

                // Validate session
                const loginTime = new Date(session.loginTime);
                const now = new Date();
                const hoursDiff = (now - loginTime) / (1000 * 60 * 60);
                const maxHours = session.rememberMe ? 24 : 8;

                if (hoursDiff < maxHours) {
                    this.currentUser = {
                        id: session.id,
                        username: session.username,
                        name: session.name,
                        email: session.email,
                        role: session.role,
                        permissions: this.userRoles[session.role]?.permissions || []
                    };
                    this.applyUserPermissions();
                } else {
                    // Session expired
                    this.clearSession();
                    this.redirectToLogin();
                }
            } else {
                // No session found, check if we're not on login page
                if (!window.location.pathname.includes('login.html')) {
                    this.redirectToLogin();
                }
            }
        } catch (error) {
            console.error('Error loading current user:', error);
            this.clearSession();
            this.redirectToLogin();
        }
    }

    showLoginModal() {
        const modalContent = `
            <div class="modal login-modal">
                <div class="modal-header">
                    <h3 class="modal-title">تسجيل الدخول</h3>
                    <button class="modal-close">&times;</button>
                </div>
                <div class="modal-body">
                    <form id="loginForm">
                        <div class="form-group">
                            <label class="form-label">اسم المستخدم</label>
                            <input type="text" class="form-input" name="username" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">كلمة المرور</label>
                            <input type="password" class="form-input" name="password" required>
                        </div>
                        <div class="form-group">
                            <label class="checkbox-label">
                                <input type="checkbox" name="rememberMe">
                                <span>تذكرني</span>
                            </label>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn-secondary modal-close">إلغاء</button>
                    <button type="button" class="btn-primary" onclick="userManagement.login()">تسجيل الدخول</button>
                </div>
            </div>
        `;

        window.app.showModal(modalContent);
    }

    async login() {
        // This method is now handled by the dedicated login page
        // Redirect to login page if not authenticated
        if (!this.currentUser) {
            this.redirectToLogin();
        }
    }

    logout() {
        this.currentUser = null;
        this.clearSession();
        this.redirectToLogin();
    }

    clearSession() {
        localStorage.removeItem('userSession');
        sessionStorage.removeItem('userSession');
        localStorage.removeItem('currentUser'); // Legacy support
    }

    redirectToLogin() {
        window.location.href = 'login.html';
    }

    hasPermission(permission) {
        if (!this.currentUser) return false;
        if (this.currentUser.permissions.includes('all')) return true;
        return this.currentUser.permissions.includes(permission);
    }

    applyUserPermissions() {
        // Hide/show navigation items based on permissions
        const navLinks = document.querySelectorAll('.nav-link');
        navLinks.forEach(link => {
            const section = link.dataset.section;
            if (this.currentUser && this.hasPermission(section)) {
                link.style.display = 'flex';
            } else if (!this.currentUser) {
                // Show all for non-authenticated users (demo mode)
                link.style.display = 'flex';
            } else {
                link.style.display = 'none';
            }
        });

        // Hide/show action buttons
        const actionButtons = document.querySelectorAll('[data-permission]');
        actionButtons.forEach(button => {
            const permission = button.dataset.permission;
            if (this.currentUser && this.hasPermission(permission)) {
                button.style.display = 'inline-flex';
            } else if (!this.currentUser) {
                button.style.display = 'inline-flex';
            } else {
                button.style.display = 'none';
            }
        });
    }

    renderUserInterface() {
        const userInfoContainer = document.getElementById('userInfo');
        if (!userInfoContainer) return;

        if (this.currentUser) {
            userInfoContainer.innerHTML = `
                <div class="user-info">
                    <div class="user-avatar">
                        <i class="fas fa-user"></i>
                    </div>
                    <div class="user-details">
                        <div class="user-name">${this.currentUser.name}</div>
                        <div class="user-role">${this.userRoles[this.currentUser.role]?.name || this.currentUser.role}</div>
                    </div>
                    <button class="logout-btn btn-outline">
                        <i class="fas fa-sign-out-alt"></i>
                        خروج
                    </button>
                </div>
            `;
        } else {
            userInfoContainer.innerHTML = `
                <div class="user-info">
                    <button class="login-btn btn-primary">
                        <i class="fas fa-sign-in-alt"></i>
                        تسجيل الدخول
                    </button>
                </div>
            `;
        }
    }

    showAddUserModal() {
        if (!this.hasPermission('all')) {
            window.app.showNotification('ليس لديك صلاحية لإضافة مستخدمين', 'error');
            return;
        }

        const modalContent = `
            <div class="modal add-user-modal">
                <div class="modal-header">
                    <h3 class="modal-title">إضافة مستخدم جديد</h3>
                    <button class="modal-close">&times;</button>
                </div>
                <div class="modal-body">
                    <form id="addUserForm">
                        <div class="form-group">
                            <label class="form-label">الاسم الكامل</label>
                            <input type="text" class="form-input" name="name" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">اسم المستخدم</label>
                            <input type="text" class="form-input" name="username" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">كلمة المرور</label>
                            <input type="password" class="form-input" name="password" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">تأكيد كلمة المرور</label>
                            <input type="password" class="form-input" name="confirmPassword" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">الدور الوظيفي</label>
                            <select class="form-select" name="role" required>
                                <option value="">اختر الدور</option>
                                ${Object.entries(this.userRoles).map(([key, role]) => `
                                    <option value="${key}">${role.name}</option>
                                `).join('')}
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">البريد الإلكتروني</label>
                            <input type="email" class="form-input" name="email">
                        </div>
                        <div class="form-group">
                            <label class="form-label">رقم الهاتف</label>
                            <input type="tel" class="form-input" name="phone">
                        </div>
                        <div class="form-group">
                            <label class="checkbox-label">
                                <input type="checkbox" name="active" checked>
                                <span>مستخدم نشط</span>
                            </label>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn-secondary modal-close">إلغاء</button>
                    <button type="button" class="btn-primary" onclick="userManagement.saveUser()">حفظ المستخدم</button>
                </div>
            </div>
        `;

        window.app.showModal(modalContent);
    }

    async saveUser() {
        const form = document.getElementById('addUserForm');
        if (!form || !form.checkValidity()) {
            form?.reportValidity();
            return;
        }

        const formData = new FormData(form);
        const password = formData.get('password');
        const confirmPassword = formData.get('confirmPassword');

        if (password !== confirmPassword) {
            window.app.showNotification('كلمة المرور وتأكيدها غير متطابقتين', 'error');
            return;
        }

        const userData = {
            id: Date.now(),
            name: formData.get('name'),
            username: formData.get('username'),
            password: formData.get('password'), // In real system, this should be hashed
            role: formData.get('role'),
            email: formData.get('email'),
            phone: formData.get('phone'),
            active: formData.get('active') === 'on',
            createdAt: new Date().toISOString(),
            createdBy: this.currentUser?.id
        };

        try {
            // Check if username already exists
            const users = await storage.getAll('users') || [];
            if (users.some(u => u.username === userData.username)) {
                window.app.showNotification('اسم المستخدم موجود بالفعل', 'error');
                return;
            }

            await storage.add('users', userData);
            window.app.closeModal();
            window.app.showNotification('تم إضافة المستخدم بنجاح', 'success');
            this.renderUsersTable();
        } catch (error) {
            console.error('Error saving user:', error);
            window.app.showNotification('خطأ في حفظ المستخدم', 'error');
        }
    }

    async renderUsersTable() {
        const container = document.getElementById('usersTableContainer');
        if (!container) return;

        try {
            const users = await storage.getAll('users') || [];
            
            const tableHTML = `
                <div class="table-container">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>الاسم</th>
                                <th>اسم المستخدم</th>
                                <th>الدور</th>
                                <th>البريد الإلكتروني</th>
                                <th>الحالة</th>
                                <th>تاريخ الإنشاء</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${users.map(user => `
                                <tr>
                                    <td>${user.name}</td>
                                    <td>${user.username}</td>
                                    <td>${this.userRoles[user.role]?.name || user.role}</td>
                                    <td>${user.email || '-'}</td>
                                    <td>
                                        <span class="status-badge ${user.active ? 'status-delivered' : 'status-returned'}">
                                            ${user.active ? 'نشط' : 'غير نشط'}
                                        </span>
                                    </td>
                                    <td>${new Date(user.createdAt).toLocaleDateString('ar-IQ')}</td>
                                    <td>
                                        <div class="action-buttons">
                                            <button class="btn-icon edit-user-btn" data-user-id="${user.id}" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn-icon btn-danger delete-user-btn" data-user-id="${user.id}" title="حذف">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
            `;

            container.innerHTML = tableHTML;
        } catch (error) {
            console.error('Error rendering users table:', error);
        }
    }

    async deleteUser(userId) {
        if (!this.hasPermission('all')) {
            window.app.showNotification('ليس لديك صلاحية لحذف المستخدمين', 'error');
            return;
        }

        if (confirm('هل أنت متأكد من حذف هذا المستخدم؟')) {
            try {
                await storage.delete('users', parseInt(userId));
                window.app.showNotification('تم حذف المستخدم بنجاح', 'success');
                this.renderUsersTable();
            } catch (error) {
                console.error('Error deleting user:', error);
                window.app.showNotification('خطأ في حذف المستخدم', 'error');
            }
        }
    }

    // Initialize default admin user if no users exist
    async initializeDefaultUser() {
        try {
            const users = await storage.getAll('users') || [];
            if (users.length === 0) {
                const defaultAdmin = {
                    id: 1,
                    name: 'مدير النظام',
                    username: 'admin',
                    password: 'admin123', // Should be hashed in production
                    role: 'admin',
                    email: '<EMAIL>',
                    phone: '07901234567',
                    active: true,
                    createdAt: new Date().toISOString()
                };

                await storage.add('users', defaultAdmin);
                console.log('Default admin user created');
            }
        } catch (error) {
            console.error('Error initializing default user:', error);
        }
    }
}

// Initialize user management system
const userManagement = new UserManagementSystem();

// Initialize default user
userManagement.initializeDefaultUser();

// Export for use in other modules
window.userManagement = userManagement;
