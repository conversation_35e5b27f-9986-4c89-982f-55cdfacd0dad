{"name": "delivery-management-system", "version": "1.0.0", "description": "نظام إدارة شركة التوصيل - نظام شامل لإدارة الطلبات والمندوبين والعملاء", "main": "server.js", "scripts": {"start": "node server.js", "dev": "node server.js", "test": "echo \"No tests specified\" && exit 0"}, "keywords": ["delivery", "management", "system", "orders", "drivers", "customers", "arabic", "rtl"], "author": "Delivery Management Team", "license": "MIT", "engines": {"node": ">=12.0.0"}, "repository": {"type": "git", "url": "local"}, "bugs": {"url": "local"}, "homepage": "http://localhost:8000"}