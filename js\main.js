// Main Application Controller
class DeliveryApp {
    constructor() {
        this.currentSection = 'dashboard';
        this.theme = localStorage.getItem('theme') || 'light';
        this.isNavigating = false;
        this.isTransitioning = false;
        this.init();
    }

    async init() {
        try {
            console.log('Initializing Delivery App...');

            // Check authentication first
            if (!this.checkAuthentication()) {
                return; // Will redirect to login
            }

            // Wait for storage to initialize
            await this.waitForStorage();
            console.log('Storage ready');

            // Initialize UI
            this.initializeUI();
            console.log('UI initialized');

            this.setupEventListeners();
            console.log('Event listeners setup');

            this.applyTheme();
            console.log('Theme applied');

            // Load initial data
            await this.loadDashboardData();
            console.log('Dashboard data loaded');

            console.log('✅ Delivery App initialized successfully');

            // Make app globally available for debugging
            window.app = this;
            console.log('🌍 App instance available globally as window.app');
        } catch (error) {
            console.error('❌ Error initializing Delivery App:', error);
            this.showNotification('خطأ في تهيئة النظام', 'error');
        }
    }

    waitForStorage() {
        return new Promise((resolve) => {
            const checkStorage = () => {
                if (window.storage) {
                    console.log('Storage is available');
                    resolve();
                } else {
                    console.log('Waiting for storage...');
                    setTimeout(checkStorage, 100);
                }
            };
            checkStorage();
        });
    }

    initializeUI() {
        // Initialize sidebar
        this.setupSidebar();
        
        // Initialize modals
        this.setupModals();
        
        // Set initial section
        console.log('🏠 Setting initial section to dashboard');
        this.showSection('dashboard');

        // Add debugging info
        this.debugNavigation();
    }

    setupEventListeners() {
        console.log('Setting up event listeners...');

        // Navigation
        this.setupNavigation();

        // Theme toggle
        const themeToggle = document.getElementById('themeToggle');
        if (themeToggle) {
            themeToggle.addEventListener('click', () => this.toggleTheme());
        }

        // Sidebar toggle
        const sidebarToggle = document.getElementById('sidebarToggle');
        if (sidebarToggle) {
            sidebarToggle.addEventListener('click', () => this.toggleSidebar());
        }

        // Modal overlay click to close
        const modalOverlay = document.getElementById('modalOverlay');
        if (modalOverlay) {
            modalOverlay.addEventListener('click', (e) => {
                if (e.target === modalOverlay) {
                    this.closeModal();
                }
            });
        }

        // Settings
        this.setupSettingsListeners();
    }

    setupSidebar() {
        const sidebar = document.getElementById('sidebar');
        const mainContent = document.getElementById('mainContent');
        
        // Handle responsive behavior
        const handleResize = () => {
            if (window.innerWidth <= 768) {
                sidebar.classList.remove('open');
                mainContent.style.marginRight = '0';
            } else {
                mainContent.style.marginRight = '280px';
            }
        };

        window.addEventListener('resize', handleResize);
        handleResize();
    }

    setupNavigation() {
        const navLinks = document.querySelectorAll('.nav-link');
        console.log(`🔗 Setting up ${navLinks.length} navigation links`);

        navLinks.forEach((link, index) => {
            const section = link.dataset.section;
            console.log(`Setting up nav link ${index + 1}: ${section}`);

            if (!section) {
                console.warn(`⚠️ Nav link ${index + 1} has no data-section attribute`);
                return;
            }

            // Remove any existing listeners to prevent duplicates
            const newLink = link.cloneNode(true);
            link.parentNode.replaceChild(newLink, link);

            // Add click listener
            newLink.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();

                console.log(`🎯 Navigation clicked: ${section}`);

                // Prevent double clicks
                if (this.isNavigating) {
                    console.log('Navigation in progress, ignoring click');
                    return;
                }

                this.isNavigating = true;

                try {
                    this.showSection(section);
                } catch (error) {
                    console.error('Navigation error:', error);
                } finally {
                    setTimeout(() => {
                        this.isNavigating = false;
                    }, 300);
                }
            });
        });

        console.log('✅ Navigation setup complete');
    }

    toggleSidebar() {
        const sidebar = document.getElementById('sidebar');
        if (sidebar) {
            sidebar.classList.toggle('open');
        }
    }

    showSection(sectionName) {
        console.log(`🔄 Showing section: ${sectionName}`);

        // Prevent multiple simultaneous transitions
        if (this.isTransitioning) {
            console.log('⏳ Transition in progress, ignoring request');
            return;
        }

        this.isTransitioning = true;

        try {
            // Get all sections
            const allSections = document.querySelectorAll('.content-section');
            console.log(`Found ${allSections.length} content sections`);

            // Get target section
            const targetSection = document.getElementById(sectionName);

            if (!targetSection) {
                console.error(`❌ Section ${sectionName} not found!`);
                this.showNotification(`القسم ${sectionName} غير موجود`, 'error');

                // Fallback to dashboard
                if (sectionName !== 'dashboard') {
                    console.log('Falling back to dashboard...');
                    setTimeout(() => this.showSection('dashboard'), 100);
                }
                return;
            }

            // Force hide all sections first
            allSections.forEach(section => {
                section.classList.remove('active');
                section.style.display = 'none';
                section.style.opacity = '0';
                section.style.visibility = 'hidden';
            });

            // Force show target section
            targetSection.style.display = 'block';
            targetSection.style.opacity = '1';
            targetSection.style.visibility = 'visible';
            targetSection.classList.add('active');

            // Force reflow to ensure changes are applied
            targetSection.offsetHeight;

            console.log(`✅ Section ${sectionName} is now visible`);

            // Update navigation
            this.updateNavigation(sectionName);

            // Update current section
            this.currentSection = sectionName;

            // Load section-specific data
            this.loadSectionData(sectionName);

            // Close sidebar on mobile after navigation
            if (window.innerWidth <= 768) {
                const sidebar = document.getElementById('sidebar');
                if (sidebar) {
                    sidebar.classList.remove('open');
                }
            }

        } catch (error) {
            console.error('Error in showSection:', error);
            this.showNotification('خطأ في التنقل', 'error');
        } finally {
            // Reset transition flag
            setTimeout(() => {
                this.isTransitioning = false;
            }, 300);
        }
    }

    updateNavigation(sectionName) {
        // Update navigation items
        document.querySelectorAll('.nav-item').forEach(item => {
            item.classList.remove('active');
        });

        // Find and activate the correct nav item
        const activeNavLink = document.querySelector(`[data-section="${sectionName}"]`);
        if (activeNavLink) {
            const navItem = activeNavLink.closest('.nav-item');
            if (navItem) {
                navItem.classList.add('active');
                console.log(`✅ Navigation updated for ${sectionName}`);
            }
        } else {
            console.warn(`⚠️ Navigation item for ${sectionName} not found`);
        }
    }

    async loadSectionData(sectionName) {
        switch (sectionName) {
            case 'dashboard':
                await this.loadDashboardData();
                break;
            case 'orders':
                if (window.ordersManager) {
                    await window.ordersManager.loadOrders();
                }
                break;
            case 'drivers':
                if (window.driversManager) {
                    await window.driversManager.loadDrivers();
                }
                break;
            case 'customers':
                if (window.customersManager) {
                    await window.customersManager.loadCustomers();
                }
                break;
            case 'reports':
                if (window.reportsManager) {
                    await window.reportsManager.initializeReports();
                }
                break;
            case 'accounting':
                if (window.accountingManager) {
                    await window.accountingManager.loadAccountingData();
                }
                break;
        }
    }

    async loadDashboardData() {
        try {
            // Load orders for statistics
            const orders = await storage.getAll('orders');
            
            // Calculate statistics
            const stats = this.calculateStats(orders);
            
            // Update dashboard
            this.updateDashboardStats(stats);
            this.updateRecentOrders(orders.slice(-5).reverse());
            
        } catch (error) {
            console.error('Error loading dashboard data:', error);
        }
    }

    calculateStats(orders) {
        const stats = {
            total: orders.length,
            delivered: 0,
            pending: 0,
            returned: 0,
            partialReturn: 0
        };

        orders.forEach(order => {
            switch (order.status) {
                case 'delivered':
                    stats.delivered++;
                    break;
                case 'pending':
                    stats.pending++;
                    break;
                case 'returned':
                    stats.returned++;
                    break;
                case 'partial_return':
                    stats.partialReturn++;
                    break;
            }
        });

        return stats;
    }

    updateDashboardStats(stats) {
        document.getElementById('totalOrders').textContent = stats.total;
        document.getElementById('deliveredOrders').textContent = stats.delivered;
        document.getElementById('pendingOrders').textContent = stats.pending;
        document.getElementById('returnedOrders').textContent = stats.returned + stats.partialReturn;
    }

    updateRecentOrders(orders) {
        const container = document.getElementById('recentOrders');
        if (!container) return;

        if (orders.length === 0) {
            container.innerHTML = '<p class="text-center">لا توجد طلبات حديثة</p>';
            return;
        }

        container.innerHTML = orders.map(order => `
            <div class="recent-order-item">
                <div class="order-info">
                    <h4>${order.clientName}</h4>
                    <p>${order.customerName} - ${order.city}</p>
                </div>
                <div class="order-status">
                    <span class="status-badge status-${order.status}">
                        ${this.getStatusText(order.status)}
                    </span>
                </div>
                <div class="order-amount">
                    ${order.deliveryPrice} د.ع
                </div>
            </div>
        `).join('');
    }

    getStatusText(status) {
        const statusMap = {
            'delivered': 'مسلم',
            'pending': 'معلق',
            'returned': 'راجع',
            'partial_return': 'راجع جزئي'
        };
        return statusMap[status] || status;
    }

    // Theme management
    toggleTheme() {
        this.theme = this.theme === 'light' ? 'dark' : 'light';
        this.applyTheme();
        localStorage.setItem('theme', this.theme);
    }

    applyTheme() {
        document.documentElement.setAttribute('data-theme', this.theme);
        const themeIcon = document.querySelector('#themeToggle i');
        if (themeIcon) {
            themeIcon.className = this.theme === 'light' ? 'fas fa-moon' : 'fas fa-sun';
        }
    }

    // Authentication methods
    checkAuthentication() {
        const sessionData = localStorage.getItem('userSession') || sessionStorage.getItem('userSession');

        if (!sessionData) {
            this.redirectToLogin();
            return false;
        }

        try {
            const session = JSON.parse(sessionData);
            const loginTime = new Date(session.loginTime);
            const now = new Date();
            const hoursDiff = (now - loginTime) / (1000 * 60 * 60);
            const maxHours = session.rememberMe ? 24 : 8;

            if (hoursDiff >= maxHours) {
                // Session expired
                this.clearSession();
                this.redirectToLogin();
                return false;
            }

            return true;
        } catch (error) {
            console.error('Error checking authentication:', error);
            this.clearSession();
            this.redirectToLogin();
            return false;
        }
    }

    clearSession() {
        localStorage.removeItem('userSession');
        sessionStorage.removeItem('userSession');
    }

    redirectToLogin() {
        window.location.href = 'login.html';
    }

    debugNavigation() {
        console.log('🔍 Navigation Debug Info:');

        // Check nav links
        const navLinks = document.querySelectorAll('.nav-link');
        console.log(`📍 Found ${navLinks.length} nav links:`);
        navLinks.forEach((link, index) => {
            console.log(`  ${index + 1}. ${link.dataset.section} - ${link.textContent.trim()}`);
        });

        // Check sections
        const sections = document.querySelectorAll('.content-section');
        console.log(`📄 Found ${sections.length} content sections:`);
        sections.forEach((section, index) => {
            const isActive = section.classList.contains('active');
            const display = window.getComputedStyle(section).display;
            const opacity = window.getComputedStyle(section).opacity;
            console.log(`  ${index + 1}. ${section.id} - Active: ${isActive}, Display: ${display}, Opacity: ${opacity}`);
        });

        // Add test buttons for debugging
        if (window.location.search.includes('debug')) {
            this.addDebugButtons();
        }

        // Add global debug function
        window.debugSections = () => {
            console.log('🔍 Current Sections Status:');
            document.querySelectorAll('.content-section').forEach(section => {
                const styles = window.getComputedStyle(section);
                console.log(`${section.id}:`, {
                    active: section.classList.contains('active'),
                    display: styles.display,
                    opacity: styles.opacity,
                    visibility: styles.visibility
                });
            });
        };

        // Add global fix function
        window.fixSections = () => {
            console.log('🔧 Fixing sections...');
            document.querySelectorAll('.content-section').forEach(section => {
                section.classList.remove('active');
                section.style.display = 'none';
                section.style.opacity = '';
                section.style.visibility = '';
                section.style.transform = '';
            });

            const dashboard = document.getElementById('dashboard');
            if (dashboard) {
                dashboard.classList.add('active');
                dashboard.style.display = 'block';
                dashboard.style.opacity = '1';
                dashboard.style.visibility = 'visible';
                console.log('✅ Dashboard restored');
            }
        };

        // Add emergency section fix
        window.emergencyFix = () => {
            console.log('🚨 Emergency section fix...');

            // Force remove all inline styles
            document.querySelectorAll('.content-section').forEach(section => {
                section.removeAttribute('style');
                section.classList.remove('active', 'fade-in', 'fade-out');
            });

            // Force show dashboard
            const dashboard = document.getElementById('dashboard');
            if (dashboard) {
                dashboard.classList.add('active');
                console.log('✅ Emergency fix completed');
            }
        };
    }

    addDebugButtons() {
        const debugContainer = document.createElement('div');
        debugContainer.style.cssText = `
            position: fixed;
            top: 10px;
            left: 10px;
            z-index: 10000;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-size: 12px;
        `;

        const sections = ['dashboard', 'orders', 'drivers', 'customers', 'assignment', 'reports', 'accounting', 'settings'];
        sections.forEach(section => {
            const btn = document.createElement('button');
            btn.textContent = section;
            btn.style.cssText = 'margin: 2px; padding: 5px; font-size: 10px;';
            btn.onclick = () => {
                console.log(`🧪 Debug: Navigating to ${section}`);
                this.showSection(section);
            };
            debugContainer.appendChild(btn);
        });

        document.body.appendChild(debugContainer);
        console.log('🧪 Debug buttons added (top-left corner)');
    }

    // Modal management
    showModal(modalContent) {
        const modalOverlay = document.getElementById('modalOverlay');
        modalOverlay.innerHTML = modalContent;
        modalOverlay.classList.add('active');
        
        // Setup modal close buttons
        modalOverlay.querySelectorAll('.modal-close').forEach(btn => {
            btn.addEventListener('click', () => this.closeModal());
        });
    }

    closeModal() {
        const modalOverlay = document.getElementById('modalOverlay');
        modalOverlay.classList.remove('active');
        setTimeout(() => {
            modalOverlay.innerHTML = '';
        }, 300);
    }

    hideModal() {
        this.closeModal();
    }

    setupModals() {
        // ESC key to close modal
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.closeModal();
            }
        });
    }

    // Settings
    setupSettingsListeners() {
        // Backup data
        const backupBtn = document.getElementById('backupData');
        if (backupBtn) {
            backupBtn.addEventListener('click', async () => {
                try {
                    await storage.exportData();
                    this.showNotification('تم تصدير البيانات بنجاح', 'success');
                } catch (error) {
                    this.showNotification('خطأ في تصدير البيانات', 'error');
                }
            });
        }

        // Import data
        const importBtn = document.getElementById('importData');
        if (importBtn) {
            importBtn.addEventListener('click', () => {
                const input = document.createElement('input');
                input.type = 'file';
                input.accept = '.json';
                input.onchange = async (e) => {
                    const file = e.target.files[0];
                    if (file) {
                        try {
                            await storage.importData(file);
                            this.showNotification('تم استيراد البيانات بنجاح', 'success');
                            location.reload(); // Reload to reflect changes
                        } catch (error) {
                            this.showNotification('خطأ في استيراد البيانات', 'error');
                        }
                    }
                };
                input.click();
            });
        }
    }

    // Notifications
    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
            <span>${message}</span>
        `;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.classList.add('show');
        }, 100);
        
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, 3000);
    }

    // Utility methods
    formatCurrency(amount) {
        return new Intl.NumberFormat('ar-IQ', {
            style: 'currency',
            currency: 'IQD',
            minimumFractionDigits: 0
        }).format(amount);
    }

    formatDate(date) {
        return new Intl.DateTimeFormat('ar-IQ', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        }).format(new Date(date));
    }
}



// Add notification styles
const notificationStyles = `
    .notification {
        position: fixed;
        top: 20px;
        left: 20px;
        background: var(--bg-secondary);
        color: var(--text-primary);
        padding: 1rem 1.5rem;
        border-radius: var(--radius-md);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        display: flex;
        align-items: center;
        gap: 0.5rem;
        transform: translateX(-100%);
        transition: transform 0.3s ease;
        z-index: 3000;
    }
    
    .notification.show {
        transform: translateX(0);
    }
    
    .notification-success {
        border-left: 4px solid var(--success-color);
    }
    
    .notification-error {
        border-left: 4px solid var(--danger-color);
    }
    
    .notification-info {
        border-left: 4px solid var(--info-color);
    }
`;

// Add styles to head
const styleSheet = document.createElement('style');
styleSheet.textContent = notificationStyles;
document.head.appendChild(styleSheet);

}

// Enhanced Application Initialization with Error Handling
function initializeApp() {
    try {
        console.log('🚀 Initializing Delivery Management System...');

        // Check if all required dependencies are loaded
        const requiredModules = ['storage'];
        const missingModules = requiredModules.filter(module => !window[module]);

        if (missingModules.length > 0) {
            console.warn('⚠️ Missing modules:', missingModules);
            // Retry after a short delay
            setTimeout(initializeApp, 100);
            return;
        }

        // Initialize the main app
        const app = new DeliveryApp();

        // Make app globally available for debugging
        window.deliveryApp = app;

        console.log('✅ Delivery Management System initialized successfully');

        // Performance monitoring
        if (window.performance && window.performance.mark) {
            window.performance.mark('app-initialized');
        }

    } catch (error) {
        console.error('❌ Failed to initialize app:', error);

        // Show user-friendly error message
        const errorDiv = document.createElement('div');
        errorDiv.innerHTML = `
            <div style="
                position: fixed; top: 20px; right: 20px;
                background: #f8d7da; color: #721c24;
                padding: 1rem; border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                z-index: 10000; max-width: 400px;
                font-family: Arial, sans-serif;
            ">
                <h4 style="margin: 0 0 0.5rem 0;">خطأ في تحميل النظام</h4>
                <p style="margin: 0; font-size: 0.9rem;">
                    يرجى إعادة تحميل الصفحة أو التحقق من اتصال الإنترنت
                </p>
                <button onclick="location.reload()" style="
                    margin-top: 0.5rem; padding: 0.5rem 1rem;
                    background: #721c24; color: white;
                    border: none; border-radius: 4px; cursor: pointer;
                ">
                    إعادة تحميل
                </button>
            </div>
        `;
        document.body.appendChild(errorDiv);

        // Auto-retry after 3 seconds
        setTimeout(() => {
            console.log('🔄 Auto-retrying initialization...');
            initializeApp();
        }, 3000);
    }
}

// Multiple DOM ready checks for better compatibility
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeApp);
} else {
    // DOM is already loaded
    initializeApp();
}

// Fallback initialization
window.addEventListener('load', () => {
    if (!window.deliveryApp) {
        console.log('🔄 Fallback initialization triggered');
        initializeApp();
    }
});

// Performance monitoring
window.addEventListener('load', () => {
    if (window.performance && window.performance.getEntriesByType) {
        const navigationTiming = window.performance.getEntriesByType('navigation')[0];
        console.log('📊 Page Load Performance:', {
            'DOM Content Loaded': navigationTiming.domContentLoadedEventEnd - navigationTiming.domContentLoadedEventStart,
            'Load Complete': navigationTiming.loadEventEnd - navigationTiming.loadEventStart,
            'Total Load Time': navigationTiming.loadEventEnd - navigationTiming.fetchStart
        });
    }
});
