// Main Application Controller
class DeliveryApp {
    constructor() {
        this.currentSection = 'dashboard';
        this.theme = localStorage.getItem('theme') || 'light';
        this.init();
    }

    async init() {
        // Wait for storage to initialize
        await this.waitForStorage();
        
        // Initialize UI
        this.initializeUI();
        this.setupEventListeners();
        this.applyTheme();
        
        // Load initial data
        await this.loadDashboardData();
        
        console.log('Delivery App initialized successfully');
    }

    waitForStorage() {
        return new Promise((resolve) => {
            const checkStorage = () => {
                if (window.storage && window.storage.db) {
                    resolve();
                } else {
                    setTimeout(checkStorage, 100);
                }
            };
            checkStorage();
        });
    }

    initializeUI() {
        // Initialize sidebar
        this.setupSidebar();
        
        // Initialize modals
        this.setupModals();
        
        // Set initial section
        this.showSection('dashboard');
    }

    setupEventListeners() {
        // Navigation
        document.querySelectorAll('.nav-link').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const section = link.dataset.section;
                this.showSection(section);
            });
        });

        // Theme toggle
        const themeToggle = document.getElementById('themeToggle');
        if (themeToggle) {
            themeToggle.addEventListener('click', () => this.toggleTheme());
        }

        // Sidebar toggle
        const sidebarToggle = document.getElementById('sidebarToggle');
        if (sidebarToggle) {
            sidebarToggle.addEventListener('click', () => this.toggleSidebar());
        }

        // Modal overlay click to close
        const modalOverlay = document.getElementById('modalOverlay');
        if (modalOverlay) {
            modalOverlay.addEventListener('click', (e) => {
                if (e.target === modalOverlay) {
                    this.closeModal();
                }
            });
        }

        // Settings
        this.setupSettingsListeners();
    }

    setupSidebar() {
        const sidebar = document.getElementById('sidebar');
        const mainContent = document.getElementById('mainContent');
        
        // Handle responsive behavior
        const handleResize = () => {
            if (window.innerWidth <= 768) {
                sidebar.classList.remove('open');
                mainContent.style.marginRight = '0';
            } else {
                mainContent.style.marginRight = '280px';
            }
        };

        window.addEventListener('resize', handleResize);
        handleResize();
    }

    toggleSidebar() {
        const sidebar = document.getElementById('sidebar');
        sidebar.classList.toggle('open');
    }

    showSection(sectionName) {
        // Hide all sections
        document.querySelectorAll('.content-section').forEach(section => {
            section.classList.remove('active');
        });

        // Show target section
        const targetSection = document.getElementById(sectionName);
        if (targetSection) {
            targetSection.classList.add('active');
            targetSection.classList.add('fade-in');
        }

        // Update navigation
        document.querySelectorAll('.nav-item').forEach(item => {
            item.classList.remove('active');
        });

        const activeNavItem = document.querySelector(`[data-section="${sectionName}"]`)?.parentElement;
        if (activeNavItem) {
            activeNavItem.classList.add('active');
        }

        this.currentSection = sectionName;

        // Load section-specific data
        this.loadSectionData(sectionName);
    }

    async loadSectionData(sectionName) {
        switch (sectionName) {
            case 'dashboard':
                await this.loadDashboardData();
                break;
            case 'orders':
                if (window.ordersManager) {
                    await window.ordersManager.loadOrders();
                }
                break;
            case 'drivers':
                if (window.driversManager) {
                    await window.driversManager.loadDrivers();
                }
                break;
            case 'customers':
                if (window.customersManager) {
                    await window.customersManager.loadCustomers();
                }
                break;
            case 'reports':
                if (window.reportsManager) {
                    await window.reportsManager.initializeReports();
                }
                break;
            case 'accounting':
                if (window.accountingManager) {
                    await window.accountingManager.loadAccountingData();
                }
                break;
        }
    }

    async loadDashboardData() {
        try {
            // Load orders for statistics
            const orders = await storage.getAll('orders');
            
            // Calculate statistics
            const stats = this.calculateStats(orders);
            
            // Update dashboard
            this.updateDashboardStats(stats);
            this.updateRecentOrders(orders.slice(-5).reverse());
            
        } catch (error) {
            console.error('Error loading dashboard data:', error);
        }
    }

    calculateStats(orders) {
        const stats = {
            total: orders.length,
            delivered: 0,
            pending: 0,
            returned: 0,
            partialReturn: 0
        };

        orders.forEach(order => {
            switch (order.status) {
                case 'delivered':
                    stats.delivered++;
                    break;
                case 'pending':
                    stats.pending++;
                    break;
                case 'returned':
                    stats.returned++;
                    break;
                case 'partial_return':
                    stats.partialReturn++;
                    break;
            }
        });

        return stats;
    }

    updateDashboardStats(stats) {
        document.getElementById('totalOrders').textContent = stats.total;
        document.getElementById('deliveredOrders').textContent = stats.delivered;
        document.getElementById('pendingOrders').textContent = stats.pending;
        document.getElementById('returnedOrders').textContent = stats.returned + stats.partialReturn;
    }

    updateRecentOrders(orders) {
        const container = document.getElementById('recentOrders');
        if (!container) return;

        if (orders.length === 0) {
            container.innerHTML = '<p class="text-center">لا توجد طلبات حديثة</p>';
            return;
        }

        container.innerHTML = orders.map(order => `
            <div class="recent-order-item">
                <div class="order-info">
                    <h4>${order.clientName}</h4>
                    <p>${order.customerName} - ${order.city}</p>
                </div>
                <div class="order-status">
                    <span class="status-badge status-${order.status}">
                        ${this.getStatusText(order.status)}
                    </span>
                </div>
                <div class="order-amount">
                    ${order.deliveryPrice} د.ع
                </div>
            </div>
        `).join('');
    }

    getStatusText(status) {
        const statusMap = {
            'delivered': 'مسلم',
            'pending': 'معلق',
            'returned': 'راجع',
            'partial_return': 'راجع جزئي'
        };
        return statusMap[status] || status;
    }

    // Theme management
    toggleTheme() {
        this.theme = this.theme === 'light' ? 'dark' : 'light';
        this.applyTheme();
        localStorage.setItem('theme', this.theme);
    }

    applyTheme() {
        document.documentElement.setAttribute('data-theme', this.theme);
        const themeIcon = document.querySelector('#themeToggle i');
        if (themeIcon) {
            themeIcon.className = this.theme === 'light' ? 'fas fa-moon' : 'fas fa-sun';
        }
    }

    // Modal management
    showModal(modalContent) {
        const modalOverlay = document.getElementById('modalOverlay');
        modalOverlay.innerHTML = modalContent;
        modalOverlay.classList.add('active');
        
        // Setup modal close buttons
        modalOverlay.querySelectorAll('.modal-close').forEach(btn => {
            btn.addEventListener('click', () => this.closeModal());
        });
    }

    closeModal() {
        const modalOverlay = document.getElementById('modalOverlay');
        modalOverlay.classList.remove('active');
        setTimeout(() => {
            modalOverlay.innerHTML = '';
        }, 300);
    }

    setupModals() {
        // ESC key to close modal
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.closeModal();
            }
        });
    }

    // Settings
    setupSettingsListeners() {
        // Backup data
        const backupBtn = document.getElementById('backupData');
        if (backupBtn) {
            backupBtn.addEventListener('click', async () => {
                try {
                    await storage.exportData();
                    this.showNotification('تم تصدير البيانات بنجاح', 'success');
                } catch (error) {
                    this.showNotification('خطأ في تصدير البيانات', 'error');
                }
            });
        }

        // Import data
        const importBtn = document.getElementById('importData');
        if (importBtn) {
            importBtn.addEventListener('click', () => {
                const input = document.createElement('input');
                input.type = 'file';
                input.accept = '.json';
                input.onchange = async (e) => {
                    const file = e.target.files[0];
                    if (file) {
                        try {
                            await storage.importData(file);
                            this.showNotification('تم استيراد البيانات بنجاح', 'success');
                            location.reload(); // Reload to reflect changes
                        } catch (error) {
                            this.showNotification('خطأ في استيراد البيانات', 'error');
                        }
                    }
                };
                input.click();
            });
        }
    }

    // Notifications
    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
            <span>${message}</span>
        `;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.classList.add('show');
        }, 100);
        
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, 3000);
    }

    // Utility methods
    formatCurrency(amount) {
        return new Intl.NumberFormat('ar-IQ', {
            style: 'currency',
            currency: 'IQD',
            minimumFractionDigits: 0
        }).format(amount);
    }

    formatDate(date) {
        return new Intl.DateTimeFormat('ar-IQ', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        }).format(new Date(date));
    }
}

// Initialize app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.app = new DeliveryApp();
});

// Add notification styles
const notificationStyles = `
    .notification {
        position: fixed;
        top: 20px;
        left: 20px;
        background: var(--bg-secondary);
        color: var(--text-primary);
        padding: 1rem 1.5rem;
        border-radius: var(--radius-md);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        display: flex;
        align-items: center;
        gap: 0.5rem;
        transform: translateX(-100%);
        transition: transform 0.3s ease;
        z-index: 3000;
    }
    
    .notification.show {
        transform: translateX(0);
    }
    
    .notification-success {
        border-left: 4px solid var(--success-color);
    }
    
    .notification-error {
        border-left: 4px solid var(--danger-color);
    }
    
    .notification-info {
        border-left: 4px solid var(--info-color);
    }
`;

// Add styles to head
const styleSheet = document.createElement('style');
styleSheet.textContent = notificationStyles;
document.head.appendChild(styleSheet);
