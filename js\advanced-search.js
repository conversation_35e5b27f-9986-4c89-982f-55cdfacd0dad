// Advanced Search and Filter System
class AdvancedSearchManager {
    constructor() {
        this.searchCriteria = {
            text: '',
            clientName: '',
            recipientPhone: '',
            driverName: '',
            orderId: '',
            trackingNumber: '',
            status: '',
            governorate: '',
            city: '',
            dateFrom: '',
            dateTo: '',
            priceMin: '',
            priceMax: ''
        };
        this.originalOrders = [];
        this.filteredOrders = [];
        this.init();
    }

    async init() {
        this.setupEventListeners();
        await this.loadOrders();
        this.setupAdvancedSearchModal();
    }

    setupEventListeners() {
        // Basic search input
        const searchInput = document.getElementById('searchOrders');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.searchCriteria.text = e.target.value;
                this.performSearch();
            });
        }

        // Advanced search button
        const advancedSearchBtn = document.getElementById('advancedSearchBtn');
        if (advancedSearchBtn) {
            advancedSearchBtn.addEventListener('click', () => {
                this.showAdvancedSearchModal();
            });
        }

        // Quick filters
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('quick-filter')) {
                const filterType = e.target.dataset.filter;
                const filterValue = e.target.dataset.value;
                this.applyQuickFilter(filterType, filterValue);
            }
        });

        // Clear filters
        const clearFiltersBtn = document.getElementById('clearFiltersBtn');
        if (clearFiltersBtn) {
            clearFiltersBtn.addEventListener('click', () => {
                this.clearAllFilters();
            });
        }
    }

    async loadOrders() {
        try {
            this.originalOrders = await storage.getAll('orders');
            this.filteredOrders = [...this.originalOrders];
        } catch (error) {
            console.error('Error loading orders for search:', error);
        }
    }

    setupAdvancedSearchModal() {
        // Add advanced search button to search section
        const searchSection = document.querySelector('.search-filter-section');
        if (searchSection && !document.getElementById('advancedSearchBtn')) {
            const advancedBtn = document.createElement('button');
            advancedBtn.id = 'advancedSearchBtn';
            advancedBtn.className = 'btn-secondary';
            advancedBtn.innerHTML = '<i class="fas fa-search-plus"></i> بحث متقدم';
            searchSection.appendChild(advancedBtn);

            const clearBtn = document.createElement('button');
            clearBtn.id = 'clearFiltersBtn';
            clearBtn.className = 'btn-outline';
            clearBtn.innerHTML = '<i class="fas fa-times"></i> مسح الفلاتر';
            searchSection.appendChild(clearBtn);
        }
    }

    showAdvancedSearchModal() {
        const modalContent = `
            <div class="modal advanced-search-modal">
                <div class="modal-header">
                    <h3 class="modal-title">البحث المتقدم</h3>
                    <button class="modal-close">&times;</button>
                </div>
                <div class="modal-body">
                    <form id="advancedSearchForm">
                        <div class="search-grid">
                            <div class="search-section">
                                <h4>معلومات الطلب</h4>
                                <div class="form-group">
                                    <label class="form-label">رقم الطلب</label>
                                    <input type="text" class="form-input" name="orderId" placeholder="مثال: 123">
                                </div>
                                <div class="form-group">
                                    <label class="form-label">رقم الوصل</label>
                                    <input type="text" class="form-input" name="trackingNumber" placeholder="مثال: TRK001234567">
                                </div>
                                <div class="form-group">
                                    <label class="form-label">حالة الطلب</label>
                                    <select class="form-select" name="status">
                                        <option value="">جميع الحالات</option>
                                        <option value="pending">معلق</option>
                                        <option value="delivered">مسلم</option>
                                        <option value="returned">راجع</option>
                                        <option value="partial">راجع جزئي</option>
                                        <option value="delayed">مؤجل</option>
                                    </select>
                                </div>
                            </div>

                            <div class="search-section">
                                <h4>معلومات العميل والزبون</h4>
                                <div class="form-group">
                                    <label class="form-label">اسم العميل/الشركة</label>
                                    <input type="text" class="form-input" name="clientName" placeholder="مثال: شركة الأمل">
                                </div>
                                <div class="form-group">
                                    <label class="form-label">رقم هاتف الزبون</label>
                                    <input type="tel" class="form-input" name="recipientPhone" placeholder="مثال: 07901234567">
                                </div>
                                <div class="form-group">
                                    <label class="form-label">اسم المندوب</label>
                                    <select class="form-select" name="driverName">
                                        <option value="">جميع المندوبين</option>
                                        <!-- Will be populated dynamically -->
                                    </select>
                                </div>
                            </div>

                            <div class="search-section">
                                <h4>الموقع والمنطقة</h4>
                                <div class="form-group">
                                    <label class="form-label">المحافظة</label>
                                    <select class="form-select" name="governorate">
                                        <option value="">جميع المحافظات</option>
                                        <option value="بغداد">بغداد</option>
                                        <option value="البصرة">البصرة</option>
                                        <option value="أربيل">أربيل</option>
                                        <option value="النجف">النجف</option>
                                        <option value="كربلاء">كربلاء</option>
                                        <option value="الموصل">الموصل</option>
                                        <option value="السليمانية">السليمانية</option>
                                        <option value="دهوك">دهوك</option>
                                        <option value="الأنبار">الأنبار</option>
                                        <option value="بابل">بابل</option>
                                        <option value="ديالى">ديالى</option>
                                        <option value="ذي قار">ذي قار</option>
                                        <option value="المثنى">المثنى</option>
                                        <option value="القادسية">القادسية</option>
                                        <option value="واسط">واسط</option>
                                        <option value="ميسان">ميسان</option>
                                        <option value="كركوك">كركوك</option>
                                        <option value="صلاح الدين">صلاح الدين</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">المدينة/المنطقة</label>
                                    <input type="text" class="form-input" name="city" placeholder="مثال: الكرادة، العشار">
                                </div>
                            </div>

                            <div class="search-section">
                                <h4>التاريخ والسعر</h4>
                                <div class="form-group">
                                    <label class="form-label">من تاريخ</label>
                                    <input type="date" class="form-input" name="dateFrom">
                                </div>
                                <div class="form-group">
                                    <label class="form-label">إلى تاريخ</label>
                                    <input type="date" class="form-input" name="dateTo">
                                </div>
                                <div class="form-group">
                                    <label class="form-label">السعر من (د.ع)</label>
                                    <input type="number" class="form-input" name="priceMin" placeholder="0">
                                </div>
                                <div class="form-group">
                                    <label class="form-label">السعر إلى (د.ع)</label>
                                    <input type="number" class="form-input" name="priceMax" placeholder="100000">
                                </div>
                            </div>
                        </div>

                        <div class="search-results-preview">
                            <div class="results-count">
                                <span id="searchResultsCount">0</span> نتيجة
                            </div>
                            <div class="quick-filters">
                                <h5>فلاتر سريعة:</h5>
                                <div class="quick-filter-buttons">
                                    <button type="button" class="quick-filter" data-filter="status" data-value="pending">معلقة</button>
                                    <button type="button" class="quick-filter" data-filter="status" data-value="delivered">مسلمة</button>
                                    <button type="button" class="quick-filter" data-filter="status" data-value="returned">راجعة</button>
                                    <button type="button" class="quick-filter" data-filter="governorate" data-value="بغداد">بغداد</button>
                                    <button type="button" class="quick-filter" data-filter="governorate" data-value="البصرة">البصرة</button>
                                    <button type="button" class="quick-filter" data-filter="today" data-value="true">اليوم</button>
                                    <button type="button" class="quick-filter" data-filter="week" data-value="true">هذا الأسبوع</button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn-secondary modal-close">إلغاء</button>
                    <button type="button" class="btn-outline" onclick="advancedSearch.clearAllFilters()">مسح الكل</button>
                    <button type="button" class="btn-primary" onclick="advancedSearch.applyAdvancedSearch()">تطبيق البحث</button>
                </div>
            </div>
        `;

        window.app.showModal(modalContent);
        this.populateDriversList();
        this.fillCurrentCriteria();
        this.setupRealTimePreview();
    }

    async populateDriversList() {
        try {
            const drivers = await storage.getAll('drivers');
            const driverSelect = document.querySelector('select[name="driverName"]');
            if (driverSelect) {
                drivers.forEach(driver => {
                    const option = document.createElement('option');
                    option.value = driver.name;
                    option.textContent = driver.name;
                    driverSelect.appendChild(option);
                });
            }
        } catch (error) {
            console.error('Error loading drivers for search:', error);
        }
    }

    fillCurrentCriteria() {
        const form = document.getElementById('advancedSearchForm');
        if (form) {
            Object.keys(this.searchCriteria).forEach(key => {
                const input = form.querySelector(`[name="${key}"]`);
                if (input && this.searchCriteria[key]) {
                    input.value = this.searchCriteria[key];
                }
            });
        }
    }

    setupRealTimePreview() {
        const form = document.getElementById('advancedSearchForm');
        if (form) {
            form.addEventListener('input', () => {
                this.updateSearchPreview();
            });
        }
    }

    updateSearchPreview() {
        const form = document.getElementById('advancedSearchForm');
        if (!form) return;

        const formData = new FormData(form);
        const tempCriteria = {};
        
        for (let [key, value] of formData.entries()) {
            tempCriteria[key] = value;
        }

        const previewResults = this.filterOrders(this.originalOrders, tempCriteria);
        const countElement = document.getElementById('searchResultsCount');
        if (countElement) {
            countElement.textContent = previewResults.length;
        }
    }

    applyAdvancedSearch() {
        const form = document.getElementById('advancedSearchForm');
        if (!form) return;

        const formData = new FormData(form);
        
        // Update search criteria
        for (let [key, value] of formData.entries()) {
            this.searchCriteria[key] = value;
        }

        this.performSearch();
        window.app.closeModal();
        
        const activeFiltersCount = Object.values(this.searchCriteria).filter(v => v !== '').length;
        window.app.showNotification(`تم تطبيق ${activeFiltersCount} فلتر - ${this.filteredOrders.length} نتيجة`, 'success');
    }

    applyQuickFilter(filterType, filterValue) {
        if (filterType === 'today') {
            const today = new Date().toISOString().split('T')[0];
            this.searchCriteria.dateFrom = today;
            this.searchCriteria.dateTo = today;
        } else if (filterType === 'week') {
            const today = new Date();
            const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
            this.searchCriteria.dateFrom = weekAgo.toISOString().split('T')[0];
            this.searchCriteria.dateTo = today.toISOString().split('T')[0];
        } else {
            this.searchCriteria[filterType] = filterValue;
        }

        this.performSearch();
        window.app.showNotification(`تم تطبيق فلتر ${filterType}`, 'info');
    }

    performSearch() {
        this.filteredOrders = this.filterOrders(this.originalOrders, this.searchCriteria);
        
        // Trigger update in orders manager
        const event = new CustomEvent('searchResults', {
            detail: { orders: this.filteredOrders, criteria: this.searchCriteria }
        });
        document.dispatchEvent(event);
    }

    filterOrders(orders, criteria) {
        return orders.filter(order => {
            // Text search (searches in multiple fields)
            if (criteria.text) {
                const searchText = criteria.text.toLowerCase();
                const searchableText = [
                    order.clientName,
                    order.recipientName || order.customerName,
                    order.recipientPhone || order.customerPhone,
                    order.address,
                    order.notes,
                    order.trackingNumber,
                    order.id?.toString()
                ].join(' ').toLowerCase();
                
                if (!searchableText.includes(searchText)) {
                    return false;
                }
            }

            // Specific field filters
            if (criteria.orderId && !order.id?.toString().includes(criteria.orderId)) {
                return false;
            }

            if (criteria.trackingNumber && !order.trackingNumber?.toLowerCase().includes(criteria.trackingNumber.toLowerCase())) {
                return false;
            }

            if (criteria.clientName && !order.clientName?.toLowerCase().includes(criteria.clientName.toLowerCase())) {
                return false;
            }

            if (criteria.recipientPhone && !order.recipientPhone?.includes(criteria.recipientPhone) && !order.customerPhone?.includes(criteria.recipientPhone)) {
                return false;
            }

            if (criteria.status && order.status !== criteria.status) {
                return false;
            }

            if (criteria.governorate && order.governorate !== criteria.governorate && order.city !== criteria.governorate) {
                return false;
            }

            if (criteria.city && !order.city?.toLowerCase().includes(criteria.city.toLowerCase())) {
                return false;
            }

            // Date range filter
            if (criteria.dateFrom || criteria.dateTo) {
                const orderDate = new Date(order.createdAt || order.date);
                if (criteria.dateFrom && orderDate < new Date(criteria.dateFrom)) {
                    return false;
                }
                if (criteria.dateTo && orderDate > new Date(criteria.dateTo + 'T23:59:59')) {
                    return false;
                }
            }

            // Price range filter
            if (criteria.priceMin && order.deliveryPrice < parseFloat(criteria.priceMin)) {
                return false;
            }
            if (criteria.priceMax && order.deliveryPrice > parseFloat(criteria.priceMax)) {
                return false;
            }

            return true;
        });
    }

    clearAllFilters() {
        this.searchCriteria = {
            text: '',
            clientName: '',
            recipientPhone: '',
            driverName: '',
            orderId: '',
            trackingNumber: '',
            status: '',
            governorate: '',
            city: '',
            dateFrom: '',
            dateTo: '',
            priceMin: '',
            priceMax: ''
        };

        // Clear search input
        const searchInput = document.getElementById('searchOrders');
        if (searchInput) {
            searchInput.value = '';
        }

        this.filteredOrders = [...this.originalOrders];
        
        // Trigger update
        const event = new CustomEvent('searchResults', {
            detail: { orders: this.filteredOrders, criteria: this.searchCriteria }
        });
        document.dispatchEvent(event);

        window.app.showNotification('تم مسح جميع الفلاتر', 'info');
    }

    // Export search results
    exportSearchResults(format = 'csv') {
        if (this.filteredOrders.length === 0) {
            window.app.showNotification('لا توجد نتائج للتصدير', 'warning');
            return;
        }

        if (format === 'csv') {
            this.exportToCSV();
        } else if (format === 'json') {
            this.exportToJSON();
        }
    }

    exportToCSV() {
        const headers = ['رقم الطلب', 'رقم الوصل', 'اسم العميل', 'اسم الزبون', 'رقم الهاتف', 'المحافظة', 'المدينة', 'العنوان', 'السعر', 'الحالة', 'التاريخ'];
        const csvContent = [
            headers.join(','),
            ...this.filteredOrders.map(order => [
                order.id,
                order.trackingNumber || '',
                order.clientName,
                order.recipientName || order.customerName,
                order.recipientPhone || order.customerPhone,
                order.governorate || order.city,
                order.city || '',
                order.address,
                order.deliveryPrice,
                order.status,
                new Date(order.createdAt || order.date).toLocaleDateString('ar-IQ')
            ].join(','))
        ].join('\n');

        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = `search_results_${new Date().toISOString().split('T')[0]}.csv`;
        link.click();
    }

    exportToJSON() {
        const jsonContent = JSON.stringify(this.filteredOrders, null, 2);
        const blob = new Blob([jsonContent], { type: 'application/json' });
        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = `search_results_${new Date().toISOString().split('T')[0]}.json`;
        link.click();
    }
}

// Initialize advanced search manager
const advancedSearch = new AdvancedSearchManager();

// Export for use in other modules
window.advancedSearch = advancedSearch;
