/* Main CSS - Arabic RTL Support */
:root {
    /* Light Theme Colors */
    --primary-color: #4f46e5;
    --secondary-color: #06b6d4;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --info-color: #3b82f6;
    
    /* Neumorphic Colors */
    --bg-primary: #f0f2f5;
    --bg-secondary: #ffffff;
    --shadow-light: #ffffff;
    --shadow-dark: #d1d9e6;
    --text-primary: #2d3748;
    --text-secondary: #718096;
    --border-color: #e2e8f0;
    
    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    
    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    
    /* Transitions */
    --transition-fast: 0.15s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;

    /* RTL Support Variables */
    --text-align-start: right;
    --text-align-end: left;
    --margin-start: margin-right;
    --margin-end: margin-left;
    --padding-start: padding-right;
    --padding-end: padding-left;
    --border-start: border-right;
    --border-end: border-left;
}

/* Dark Theme */
[data-theme="dark"] {
    --bg-primary: #1a202c;
    --bg-secondary: #2d3748;
    --shadow-light: #4a5568;
    --shadow-dark: #0f1419;
    --text-primary: #f7fafc;
    --text-secondary: #a0aec0;
    --border-color: #4a5568;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    background-color: var(--bg-primary);
    color: var(--text-primary);
    line-height: 1.6;
    direction: rtl;
    text-align: right;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-md);
}

/* Header */
.header {
    background: var(--bg-secondary);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    height: 70px;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 70px;
}

.logo {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.logo i {
    font-size: 2rem;
    color: var(--primary-color);
}

.logo h1 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
}

.header-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.user-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    color: var(--text-secondary);
}

/* Sidebar */
.sidebar {
    position: fixed;
    top: 70px;
    right: 0;
    width: 280px;
    height: calc(100vh - 70px);
    background: var(--bg-secondary);
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
    z-index: 999;
    transition: transform var(--transition-normal);
}

.sidebar-header {
    padding: var(--spacing-md);
    border-bottom: 1px solid var(--border-color);
}

.nav-menu {
    list-style: none;
    padding: var(--spacing-md) 0;
}

.nav-item {
    margin-bottom: var(--spacing-xs);
}

.nav-link {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md) var(--spacing-lg);
    color: var(--text-secondary);
    text-decoration: none;
    transition: all var(--transition-fast);
    border-radius: var(--radius-md);
    margin: 0 var(--spacing-md);
}

.nav-link:hover {
    background: var(--bg-primary);
    color: var(--primary-color);
}

.nav-item.active .nav-link {
    background: var(--primary-color);
    color: white;
}

.nav-link i {
    font-size: 1.2rem;
    width: 20px;
}

/* Main Content */
.main-content {
    margin-right: 280px;
    margin-top: 70px;
    padding: var(--spacing-xl);
    min-height: calc(100vh - 70px);
}

.content-section {
    display: none;
    padding: 2rem;
    opacity: 0;
    visibility: hidden;
    transform: translateY(30px);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    will-change: opacity, transform, visibility;
}

.content-section.active {
    display: block;
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.content-section.fade-in {
    animation: enhancedFadeIn 0.5s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.content-section.fade-out {
    animation: enhancedFadeOut 0.3s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.fade-in {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-2xl);
}

.page-header h2 {
    font-size: 2rem;
    font-weight: 600;
    color: var(--text-primary);
}

.page-header p {
    color: var(--text-secondary);
    margin-top: var(--spacing-xs);
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-2xl);
}

.stat-card {
    background: var(--bg-secondary);
    padding: var(--spacing-xl);
    border-radius: var(--radius-xl);
    box-shadow: 
        8px 8px 16px var(--shadow-dark),
        -8px -8px 16px var(--shadow-light);
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    transition: transform var(--transition-fast);
}

.stat-card:hover {
    transform: translateY(-2px);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--primary-color);
    color: white;
    font-size: 1.5rem;
}

.stat-icon.delivered {
    background: var(--success-color);
}

.stat-icon.pending {
    background: var(--warning-color);
}

.stat-icon.returned {
    background: var(--danger-color);
}

.stat-content h3 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.stat-content p {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

/* Dashboard Section */
.dashboard-section {
    background: var(--bg-secondary);
    padding: var(--spacing-xl);
    border-radius: var(--radius-xl);
    box-shadow: 
        8px 8px 16px var(--shadow-dark),
        -8px -8px 16px var(--shadow-light);
    margin-bottom: var(--spacing-xl);
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
}

.section-header h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
}

/* Search and Filter */
.search-filter-section {
    background: var(--bg-secondary);
    padding: var(--spacing-lg);
    border-radius: var(--radius-lg);
    box-shadow: 
        4px 4px 8px var(--shadow-dark),
        -4px -4px 8px var(--shadow-light);
    margin-bottom: var(--spacing-xl);
    display: flex;
    gap: var(--spacing-lg);
    align-items: center;
    flex-wrap: wrap;
}

.search-box {
    position: relative;
    flex: 1;
    min-width: 250px;
}

.search-box i {
    position: absolute;
    right: var(--spacing-md);
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-secondary);
}

.search-box input {
    width: 100%;
    padding: var(--spacing-md) var(--spacing-md) var(--spacing-md) 3rem;
    border: none;
    border-radius: var(--radius-md);
    background: var(--bg-primary);
    color: var(--text-primary);
    font-family: inherit;
    box-shadow: inset 2px 2px 4px var(--shadow-dark),
                inset -2px -2px 4px var(--shadow-light);
}

.filter-controls {
    display: flex;
    gap: var(--spacing-md);
    flex-wrap: wrap;
}

.filter-select,
.filter-date {
    padding: var(--spacing-sm) var(--spacing-md);
    border: none;
    border-radius: var(--radius-md);
    background: var(--bg-primary);
    color: var(--text-primary);
    font-family: inherit;
    box-shadow: inset 2px 2px 4px var(--shadow-dark),
                inset -2px -2px 4px var(--shadow-light);
}

/* Table */
.table-container {
    background: var(--bg-secondary);
    border-radius: var(--radius-lg);
    box-shadow: 
        8px 8px 16px var(--shadow-dark),
        -8px -8px 16px var(--shadow-light);
    overflow: hidden;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
}

.data-table th,
.data-table td {
    padding: var(--spacing-md);
    text-align: right;
    border-bottom: 1px solid var(--border-color);
}

.data-table th {
    background: var(--bg-primary);
    font-weight: 600;
    color: var(--text-primary);
}

.data-table tr:hover {
    background: var(--bg-primary);
}

/* Action Buttons */
.action-buttons {
    display: flex;
    gap: var(--spacing-xs);
    justify-content: center;
}

/* Recent Orders */
.recent-order-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-md);
    border-bottom: 1px solid var(--border-color);
    transition: background-color var(--transition-fast);
}

.recent-order-item:hover {
    background-color: var(--bg-primary);
}

.recent-order-item:last-child {
    border-bottom: none;
}

.order-info h4 {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.order-info p {
    font-size: 0.9rem;
    color: var(--text-secondary);
}

.order-amount {
    font-weight: 600;
    color: var(--primary-color);
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: var(--spacing-2xl);
    color: var(--text-secondary);
}

.empty-state i {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-lg);
}

.empty-state h3 {
    font-size: 1.5rem;
    margin-bottom: var(--spacing-md);
    color: var(--text-primary);
}

/* Text Utilities */
.text-center {
    text-align: center;
}

.text-success {
    color: var(--success-color);
}

.text-warning {
    color: var(--warning-color);
}

.text-danger {
    color: var(--danger-color);
}

.text-primary {
    color: var(--primary-color);
}

/* Progress Bar */
.progress-bar {
    width: 100%;
    height: 8px;
    background-color: var(--bg-primary);
    border-radius: var(--radius-sm);
    overflow: hidden;
    margin-top: var(--spacing-xs);
}

.progress-fill {
    height: 100%;
    background-color: var(--primary-color);
    transition: width var(--transition-normal);
}

/* Distribution Items */
.distribution-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-sm);
    background: var(--bg-primary);
    border-radius: var(--radius-md);
}

.city-name {
    font-weight: 500;
    color: var(--text-primary);
    flex: 1;
}

.city-count {
    color: var(--text-secondary);
    margin-left: var(--spacing-md);
    margin-right: var(--spacing-md);
}

/* Summary Stats */
.summary-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md);
    background: var(--bg-primary);
    border-radius: var(--radius-md);
}

/* Balances Header */
.balances-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
}

.balances-header h4 {
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--text-primary);
}

/* Report Filters */
.report-filters {
    display: flex;
    gap: var(--spacing-lg);
    align-items: end;
    margin-bottom: var(--spacing-xl);
    padding: var(--spacing-lg);
    background: var(--bg-secondary);
    border-radius: var(--radius-lg);
    box-shadow:
        4px 4px 8px var(--shadow-dark),
        -4px -4px 8px var(--shadow-light);
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.filter-group label {
    font-weight: 500;
    color: var(--text-primary);
    font-size: 0.9rem;
}

.filter-group select,
.filter-group input {
    padding: var(--spacing-sm) var(--spacing-md);
    border: none;
    border-radius: var(--radius-md);
    background: var(--bg-primary);
    color: var(--text-primary);
    font-family: inherit;
    box-shadow: inset 2px 2px 4px var(--shadow-dark),
                inset -2px -2px 4px var(--shadow-light);
}

/* Report Actions */
.report-actions {
    display: flex;
    gap: var(--spacing-md);
}

/* Responsive Design */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(100%);
    }

    .sidebar.open {
        transform: translateX(0);
    }

    .main-content {
        margin-right: 0;
        padding: var(--spacing-md);
    }

    .page-header {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-md);
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .search-filter-section {
        flex-direction: column;
        align-items: stretch;
    }

    .filter-controls {
        justify-content: stretch;
    }

    .filter-select,
    .filter-date {
        flex: 1;
    }

    .report-filters {
        flex-direction: column;
        align-items: stretch;
    }

    .summary-stats {
        grid-template-columns: 1fr;
    }

    .distribution-item {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-sm);
    }

    .action-buttons {
        flex-wrap: wrap;
    }
}

/* Enhanced Order Display */
.recipient-info, .location-info {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.recipient-name, .governorate {
    font-weight: 500;
    color: var(--text-primary);
}

.recipient-phone, .city {
    font-size: 0.85rem;
    color: var(--text-secondary);
    direction: ltr;
    text-align: right;
}

.recipient-phone::before {
    content: "📱 ";
    margin-left: 0.25rem;
}

.city {
    font-style: italic;
}

/* Enhanced Status Badges */
.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.8rem;
    font-weight: 500;
    text-align: center;
    display: inline-block;
    min-width: 80px;
}

.status-pending {
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.status-delivered {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.status-returned {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.status-partial {
    background: #e2e3e5;
    color: #383d41;
    border: 1px solid #d6d8db;
}

.status-delayed {
    background: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

/* Order Classification Dashboard */
.classification-dashboard {
    background: var(--bg-secondary);
    border-radius: var(--radius-xl);
    padding: var(--spacing-xl);
    box-shadow:
        8px 8px 16px var(--shadow-dark),
        -8px -8px 16px var(--shadow-light);
    margin-bottom: var(--spacing-xl);
}

.classification-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-xl);
    padding-bottom: var(--spacing-lg);
    border-bottom: 2px solid var(--border-color);
}

.classification-header h3 {
    font-size: 1.8rem;
    font-weight: 600;
    color: var(--text-primary);
}

.classification-summary {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
}

.total-orders {
    font-weight: 500;
    color: var(--text-secondary);
    background: var(--bg-primary);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-md);
}

.refresh-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.classification-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-2xl);
}

.classification-card {
    background: var(--bg-primary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    box-shadow:
        4px 4px 8px var(--shadow-dark),
        -4px -4px 8px var(--shadow-light);
    transition: transform var(--transition-fast);
    position: relative;
    overflow: hidden;
}

.classification-card:hover {
    transform: translateY(-2px);
}

.classification-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--primary-color);
}

.classification-card.delivered::before {
    background: var(--success-color);
}

.classification-card.returned::before {
    background: var(--danger-color);
}

.classification-card.partial::before {
    background: var(--warning-color);
}

.classification-card.delayed::before {
    background: var(--info-color);
}

.classification-card.pending::before {
    background: var(--text-secondary);
}

.classification-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--bg-secondary);
    color: var(--primary-color);
    font-size: 1.5rem;
    margin-bottom: var(--spacing-md);
    box-shadow:
        2px 2px 4px var(--shadow-dark),
        -2px -2px 4px var(--shadow-light);
}

.classification-card.delivered .classification-icon {
    color: var(--success-color);
}

.classification-card.returned .classification-icon {
    color: var(--danger-color);
}

.classification-card.partial .classification-icon {
    color: var(--warning-color);
}

.classification-card.delayed .classification-icon {
    color: var(--info-color);
}

.classification-card.pending .classification-icon {
    color: var(--text-secondary);
}

.classification-content h4 {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

.classification-stats {
    display: flex;
    align-items: baseline;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-sm);
}

.classification-stats .count {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
}

.classification-stats .percentage {
    font-size: 1rem;
    color: var(--text-secondary);
    background: var(--bg-secondary);
    padding: 0.2rem 0.5rem;
    border-radius: var(--radius-sm);
}

.classification-content .description {
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin-bottom: var(--spacing-md);
    line-height: 1.4;
}

.classification-filter {
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-md);
    border: 2px solid var(--border-color);
    border-radius: var(--radius-md);
    background: var(--bg-secondary);
    color: var(--text-primary);
    font-weight: 500;
    transition: all var(--transition-fast);
    cursor: pointer;
}

.classification-filter:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.classification-progress {
    margin-top: var(--spacing-md);
}

/* Financial Impact */
.financial-impact {
    margin-bottom: var(--spacing-2xl);
}

.financial-impact h4 {
    font-size: 1.4rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-lg);
}

.financial-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-md);
}

.financial-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-lg);
    background: var(--bg-primary);
    border-radius: var(--radius-md);
    box-shadow:
        2px 2px 4px var(--shadow-dark),
        -2px -2px 4px var(--shadow-light);
}

.financial-item i {
    font-size: 1.5rem;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
}

.financial-item.success i {
    background: var(--success-color);
}

.financial-item.warning i {
    background: var(--warning-color);
}

.financial-item.danger i {
    background: var(--danger-color);
}

.financial-item.info i {
    background: var(--info-color);
}

.financial-item .label {
    display: block;
    font-size: 0.9rem;
    color: var(--text-secondary);
    margin-bottom: 0.2rem;
}

.financial-item .value {
    display: block;
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-primary);
}

/* Classification Insights */
.classification-insights h4 {
    font-size: 1.4rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-lg);
}

.insights-grid {
    display: grid;
    gap: var(--spacing-md);
}

.insight-item {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-md);
    padding: var(--spacing-lg);
    background: var(--bg-primary);
    border-radius: var(--radius-md);
    border-left: 4px solid var(--primary-color);
    box-shadow:
        2px 2px 4px var(--shadow-dark),
        -2px -2px 4px var(--shadow-light);
}

.insight-item.success {
    border-left-color: var(--success-color);
}

.insight-item.warning {
    border-left-color: var(--warning-color);
}

.insight-item.danger {
    border-left-color: var(--danger-color);
}

.insight-item.info {
    border-left-color: var(--info-color);
}

.insight-item i {
    font-size: 1.2rem;
    color: var(--primary-color);
    margin-top: 0.2rem;
}

.insight-item.success i {
    color: var(--success-color);
}

.insight-item.warning i {
    color: var(--warning-color);
}

.insight-item.danger i {
    color: var(--danger-color);
}

.insight-item.info i {
    color: var(--info-color);
}

.insight-item h5 {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.3rem;
}

.insight-item p {
    font-size: 0.9rem;
    color: var(--text-secondary);
    line-height: 1.4;
    margin: 0;
}

/* Mobile Responsive for Enhanced Fields */
@media (max-width: 768px) {
    .recipient-info, .location-info {
        gap: 0.1rem;
    }

    .recipient-phone, .city {
        font-size: 0.75rem;
    }

    .data-table th,
    .data-table td {
        padding: 0.5rem 0.25rem;
        font-size: 0.8rem;
    }

    .classification-grid {
        grid-template-columns: 1fr;
    }

    .financial-grid {
        grid-template-columns: 1fr;
    }

    .classification-header {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-md);
    }

    .classification-summary {
        width: 100%;
        justify-content: space-between;
    }
}

/* Advanced Search Modal */
.advanced-search-modal {
    max-width: 900px;
    width: 90vw;
}

.search-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-xl);
}

.search-section {
    background: var(--bg-primary);
    padding: var(--spacing-lg);
    border-radius: var(--radius-lg);
    box-shadow:
        2px 2px 4px var(--shadow-dark),
        -2px -2px 4px var(--shadow-light);
}

.search-section h4 {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-sm);
    border-bottom: 2px solid var(--border-color);
}

.search-results-preview {
    background: var(--bg-primary);
    padding: var(--spacing-lg);
    border-radius: var(--radius-lg);
    box-shadow:
        2px 2px 4px var(--shadow-dark),
        -2px -2px 4px var(--shadow-light);
    margin-top: var(--spacing-lg);
}

.results-count {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: var(--spacing-md);
    text-align: center;
    padding: var(--spacing-sm);
    background: var(--bg-secondary);
    border-radius: var(--radius-md);
}

.quick-filters h5 {
    font-size: 1rem;
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

.quick-filter-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-sm);
}

.quick-filter {
    padding: var(--spacing-xs) var(--spacing-sm);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-sm);
    background: var(--bg-secondary);
    color: var(--text-secondary);
    font-size: 0.85rem;
    cursor: pointer;
    transition: all var(--transition-fast);
}

.quick-filter:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
    background: var(--bg-primary);
}

.quick-filter.active {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

/* Enhanced Search Filter Section */
.search-filter-section {
    position: relative;
}

.search-filter-section .btn-secondary,
.search-filter-section .btn-outline {
    white-space: nowrap;
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.search-filter-section .btn-outline {
    border: 2px solid var(--danger-color);
    color: var(--danger-color);
    background: transparent;
}

.search-filter-section .btn-outline:hover {
    background: var(--danger-color);
    color: white;
}

/* Search Results Highlighting */
.search-highlight {
    background: yellow;
    padding: 0.1rem 0.2rem;
    border-radius: var(--radius-sm);
    font-weight: 600;
}

/* Filter Status Indicator */
.filter-status {
    position: absolute;
    top: -8px;
    right: -8px;
    background: var(--primary-color);
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.7rem;
    font-weight: 600;
}

.search-box {
    position: relative;
}

.search-box.has-filters::after {
    content: attr(data-filter-count);
    position: absolute;
    top: -8px;
    left: -8px;
    background: var(--primary-color);
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.7rem;
    font-weight: 600;
}

/* Export Buttons */
.export-buttons {
    display: flex;
    gap: var(--spacing-sm);
    margin-top: var(--spacing-md);
}

.export-btn {
    padding: var(--spacing-xs) var(--spacing-sm);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-sm);
    background: var(--bg-secondary);
    color: var(--text-primary);
    font-size: 0.85rem;
    cursor: pointer;
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.export-btn:hover {
    border-color: var(--success-color);
    color: var(--success-color);
}

/* Mobile Responsive for Advanced Search */
@media (max-width: 768px) {
    .advanced-search-modal {
        width: 95vw;
        max-width: none;
    }

    .search-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }

    .quick-filter-buttons {
        justify-content: center;
    }

    .search-filter-section {
        flex-direction: column;
        align-items: stretch;
        gap: var(--spacing-md);
    }

    .search-filter-section .btn-secondary,
    .search-filter-section .btn-outline {
        justify-content: center;
    }
}

/* Advanced Reports Styles */
.reports-dashboard {
    background: var(--bg-secondary);
    border-radius: var(--radius-xl);
    padding: var(--spacing-xl);
    box-shadow:
        8px 8px 16px var(--shadow-dark),
        -8px -8px 16px var(--shadow-light);
    margin-bottom: var(--spacing-xl);
}

.reports-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-xl);
    padding-bottom: var(--spacing-lg);
    border-bottom: 2px solid var(--border-color);
}

.reports-header h3 {
    font-size: 1.8rem;
    font-weight: 600;
    color: var(--text-primary);
}

.report-types-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-2xl);
}

.report-type-card {
    background: var(--bg-primary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    box-shadow:
        4px 4px 8px var(--shadow-dark),
        -4px -4px 8px var(--shadow-light);
    transition: transform var(--transition-fast);
    text-align: center;
}

.report-type-card:hover {
    transform: translateY(-2px);
}

.report-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--primary-color);
    color: white;
    font-size: 1.8rem;
    margin: 0 auto var(--spacing-md);
    box-shadow:
        2px 2px 4px var(--shadow-dark),
        -2px -2px 4px var(--shadow-light);
}

.report-content h4 {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

.report-content p {
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin-bottom: var(--spacing-lg);
    line-height: 1.4;
}

/* Report Configuration Modal */
.report-config-modal {
    max-width: 800px;
    width: 90vw;
}

.config-section {
    background: var(--bg-primary);
    padding: var(--spacing-lg);
    border-radius: var(--radius-lg);
    margin-bottom: var(--spacing-lg);
    box-shadow:
        2px 2px 4px var(--shadow-dark),
        -2px -2px 4px var(--shadow-light);
}

.config-section h4 {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-sm);
    border-bottom: 2px solid var(--border-color);
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-lg);
}

.quick-periods {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-sm);
    margin-top: var(--spacing-md);
}

.period-btn {
    padding: var(--spacing-xs) var(--spacing-sm);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-sm);
    background: var(--bg-secondary);
    color: var(--text-secondary);
    font-size: 0.85rem;
    cursor: pointer;
    transition: all var(--transition-fast);
}

.period-btn:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.checkbox-group {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: var(--radius-sm);
    transition: background-color var(--transition-fast);
}

.checkbox-label:hover {
    background: var(--bg-secondary);
}

.checkbox-label input[type="checkbox"] {
    width: 18px;
    height: 18px;
    accent-color: var(--primary-color);
}

/* Current Report Container */
.current-report-container {
    background: var(--bg-secondary);
    border-radius: var(--radius-xl);
    padding: var(--spacing-xl);
    box-shadow:
        8px 8px 16px var(--shadow-dark),
        -8px -8px 16px var(--shadow-light);
    margin-top: var(--spacing-xl);
}

.report-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-xl);
    padding-bottom: var(--spacing-lg);
    border-bottom: 2px solid var(--border-color);
}

.report-header h4 {
    font-size: 1.6rem;
    font-weight: 600;
    color: var(--text-primary);
}

.report-actions {
    display: flex;
    gap: var(--spacing-sm);
}

/* Report Content Styles */
.report-summary {
    text-align: center;
    margin-bottom: var(--spacing-2xl);
}

.report-summary h3 {
    font-size: 2rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

.period-info {
    color: var(--text-secondary);
    font-size: 1rem;
    background: var(--bg-primary);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-md);
    display: inline-block;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-lg);
    margin: var(--spacing-xl) 0;
}

.stat-item {
    background: var(--bg-primary);
    padding: var(--spacing-lg);
    border-radius: var(--radius-lg);
    text-align: center;
    box-shadow:
        2px 2px 4px var(--shadow-dark),
        -2px -2px 4px var(--shadow-light);
}

.stat-item.success {
    border-left: 4px solid var(--success-color);
}

.stat-item.danger {
    border-left: 4px solid var(--danger-color);
}

.stat-item.warning {
    border-left: 4px solid var(--warning-color);
}

.stat-item.primary {
    border-left: 4px solid var(--primary-color);
}

.stat-value {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.stat-label {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.financial-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-md);
    margin: var(--spacing-lg) 0;
}

.financial-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md);
    background: var(--bg-primary);
    border-radius: var(--radius-md);
    box-shadow:
        1px 1px 2px var(--shadow-dark),
        -1px -1px 2px var(--shadow-light);
}

.financial-item .label {
    color: var(--text-secondary);
    font-weight: 500;
}

.financial-item .value {
    font-weight: 600;
    color: var(--text-primary);
}

.financial-item .value.success {
    color: var(--success-color);
}

.financial-item .value.danger {
    color: var(--danger-color);
}

.financial-item .value.warning {
    color: var(--warning-color);
}

/* Report Table */
.report-table {
    width: 100%;
    border-collapse: collapse;
    margin: var(--spacing-lg) 0;
    background: var(--bg-secondary);
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow:
        4px 4px 8px var(--shadow-dark),
        -4px -4px 8px var(--shadow-light);
}

.report-table th,
.report-table td {
    padding: var(--spacing-md);
    text-align: right;
    border-bottom: 1px solid var(--border-color);
}

.report-table th {
    background: var(--bg-primary);
    font-weight: 600;
    color: var(--text-primary);
}

.report-table tr:hover {
    background: var(--bg-primary);
}

.report-table tr:last-child td {
    border-bottom: none;
}

/* Performance Badges */
.performance-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.8rem;
    font-weight: 500;
    text-align: center;
    display: inline-block;
}

.performance-badge.excellent {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.performance-badge.good {
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.performance-badge.needs-improvement {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* Stats Sections */
.stats-section {
    margin-bottom: var(--spacing-2xl);
}

.stats-section h4 {
    font-size: 1.4rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-lg);
}

.general-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-md);
}

.general-stats .stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md);
    background: var(--bg-primary);
    border-radius: var(--radius-md);
}

.general-stats .label {
    color: var(--text-secondary);
}

.general-stats .value {
    font-weight: 600;
    color: var(--text-primary);
}

/* Mobile Responsive for Reports */
@media (max-width: 768px) {
    .report-config-modal {
        width: 95vw;
        max-width: none;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .quick-periods {
        justify-content: center;
    }

    .report-types-grid {
        grid-template-columns: 1fr;
    }

    .report-header {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-md);
    }

    .report-actions {
        width: 100%;
        justify-content: space-between;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .financial-grid {
        grid-template-columns: 1fr;
    }

    .report-table {
        font-size: 0.8rem;
    }

    .report-table th,
    .report-table td {
        padding: var(--spacing-sm);
    }
}

/* User Management Styles */
.user-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    background: var(--bg-primary);
    border-radius: var(--radius-lg);
    box-shadow: var(--elevation-1);
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--primary-gradient);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
}

.user-details {
    flex: 1;
}

.user-name {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.2rem;
}

.user-role {
    font-size: 0.85rem;
    color: var(--text-secondary);
}

/* Notification System Styles */
.notifications-toggle {
    position: relative;
}

.notification-badge {
    position: absolute;
    top: -8px;
    left: -8px;
    background: var(--danger-color);
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.7rem;
    font-weight: 600;
    display: none;
}

.notification-center {
    position: fixed;
    top: 80px;
    left: 20px;
    width: 350px;
    max-height: 500px;
    background: var(--bg-primary);
    border-radius: var(--radius-xl);
    box-shadow: var(--elevation-4);
    z-index: 1000;
    transform: translateX(-100%);
    opacity: 0;
    transition: all var(--transition-normal);
    overflow: hidden;
}

.notification-center.open {
    transform: translateX(0);
    opacity: 1;
}

.notification-center-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
    background: var(--bg-secondary);
}

.notification-center-header h3 {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.notification-actions {
    display: flex;
    gap: var(--spacing-xs);
}

.notification-center-body {
    max-height: 400px;
    overflow-y: auto;
}

.notifications-list {
    padding: var(--spacing-sm);
}

.notification-item {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all var(--transition-fast);
    position: relative;
    margin-bottom: var(--spacing-sm);
}

.notification-item:hover {
    background: var(--bg-secondary);
}

.notification-item.unread {
    background: rgba(102, 126, 234, 0.1);
    border-left: 3px solid var(--primary-color);
}

.notification-item.read {
    opacity: 0.7;
}

.notification-item .notification-icon {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.9rem;
}

.notification-item.info .notification-icon {
    background: var(--info-color);
}

.notification-item.success .notification-icon {
    background: var(--success-color);
}

.notification-item.warning .notification-icon {
    background: var(--warning-color);
}

.notification-item.error .notification-icon {
    background: var(--danger-color);
}

.notification-content {
    flex: 1;
}

.notification-title {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.3rem;
    font-size: 0.9rem;
}

.notification-message {
    color: var(--text-secondary);
    font-size: 0.8rem;
    line-height: 1.4;
    margin-bottom: 0.3rem;
}

.notification-time {
    color: var(--text-muted);
    font-size: 0.7rem;
}

.unread-indicator {
    position: absolute;
    top: 50%;
    right: var(--spacing-md);
    transform: translateY(-50%);
    width: 8px;
    height: 8px;
    background: var(--primary-color);
    border-radius: 50%;
}

.empty-notifications {
    text-align: center;
    padding: var(--spacing-2xl);
    color: var(--text-muted);
}

.empty-notifications i {
    font-size: 3rem;
    margin-bottom: var(--spacing-md);
    opacity: 0.5;
}

/* Performance and PWA Features */
.network-status {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    background: var(--bg-primary);
    padding: var(--spacing-md) var(--spacing-lg);
    border-radius: var(--radius-lg);
    box-shadow: var(--elevation-3);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    z-index: 2000;
    animation: slideDown 0.3s ease-out;
}

.network-status.online {
    border-left: 4px solid var(--success-color);
    color: var(--success-color);
}

.network-status.offline {
    border-left: 4px solid var(--danger-color);
    color: var(--danger-color);
}

.update-banner {
    position: fixed;
    bottom: 20px;
    left: 20px;
    right: 20px;
    background: var(--primary-gradient);
    color: white;
    padding: var(--spacing-lg);
    border-radius: var(--radius-lg);
    box-shadow: var(--elevation-4);
    z-index: 2000;
    animation: slideUp 0.3s ease-out;
}

.update-content {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    flex-wrap: wrap;
}

.update-content i {
    font-size: 1.2rem;
}

.update-content span {
    flex: 1;
    font-weight: 500;
}

.install-app-btn {
    white-space: nowrap;
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

/* Connection Quality Adaptations */
.connection-poor .enhanced-animations,
.connection-poor .complex-transitions {
    animation: none !important;
    transition: none !important;
}

.connection-poor .auto-refresh {
    display: none;
}

.connection-poor .high-quality-images {
    filter: contrast(0.9) brightness(0.95);
}

.reduced-animations * {
    animation-duration: 0.1s !important;
    transition-duration: 0.1s !important;
}

.enhanced-animations {
    animation-duration: 0.5s;
    transition-duration: 0.3s;
}

/* Lazy Loading States */
[data-lazy] {
    opacity: 0;
    transition: opacity var(--transition-normal);
}

[data-lazy].loaded {
    opacity: 1;
}

.lazy-placeholder {
    background: var(--bg-secondary);
    border-radius: var(--radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 200px;
    color: var(--text-muted);
}

.lazy-placeholder::before {
    content: '';
    width: 40px;
    height: 40px;
    border: 3px solid var(--border-color);
    border-top-color: var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Performance Indicators */
.performance-indicator {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background: var(--bg-primary);
    padding: var(--spacing-sm);
    border-radius: var(--radius-md);
    box-shadow: var(--elevation-2);
    font-size: 0.8rem;
    color: var(--text-secondary);
    z-index: 1000;
    opacity: 0.7;
    transition: opacity var(--transition-fast);
}

.performance-indicator:hover {
    opacity: 1;
}

.performance-good {
    border-left: 3px solid var(--success-color);
}

.performance-warning {
    border-left: 3px solid var(--warning-color);
}

.performance-poor {
    border-left: 3px solid var(--danger-color);
}

/* Offline Mode Styles */
.offline-mode {
    filter: grayscale(0.3);
}

.offline-mode .real-time-features {
    display: none;
}

.offline-indicator {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: var(--warning-color);
    color: white;
    text-align: center;
    padding: var(--spacing-sm);
    font-weight: 500;
    z-index: 2000;
}

/* Loading States */
.loading-skeleton {
    background: linear-gradient(90deg, var(--bg-secondary) 25%, var(--bg-primary) 50%, var(--bg-secondary) 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
    border-radius: var(--radius-md);
}

.loading-skeleton.text {
    height: 1rem;
    margin-bottom: 0.5rem;
}

.loading-skeleton.title {
    height: 1.5rem;
    width: 60%;
    margin-bottom: 1rem;
}

.loading-skeleton.card {
    height: 200px;
    margin-bottom: 1rem;
}

/* PWA Specific Styles */
@media (display-mode: standalone) {
    .header {
        padding-top: env(safe-area-inset-top);
    }

    .install-app-btn {
        display: none;
    }

    .pwa-only {
        display: block;
    }
}

.pwa-only {
    display: none;
}

/* Animations */
@keyframes slideDown {
    from {
        transform: translateX(-50%) translateY(-100%);
        opacity: 0;
    }
    to {
        transform: translateX(-50%) translateY(0);
        opacity: 1;
    }
}

@keyframes slideUp {
    from {
        transform: translateY(100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

@keyframes shimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

/* Mobile Responsive for Performance Features */
@media (max-width: 768px) {
    .network-status {
        left: 10px;
        right: 10px;
        transform: none;
    }

    .update-banner {
        left: 10px;
        right: 10px;
    }

    .update-content {
        flex-direction: column;
        text-align: center;
    }

    .performance-indicator {
        bottom: 10px;
        right: 10px;
        font-size: 0.7rem;
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

.fade-out {
    animation: fadeOut 0.3s ease-in-out;
}

.slide-in-right {
    animation: slideInRight 0.4s ease-out;
}

.slide-in-left {
    animation: slideInLeft 0.4s ease-out;
}

.scale-in {
    animation: scaleIn 0.3s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeOut {
    from {
        opacity: 1;
        transform: translateY(0);
    }
    to {
        opacity: 0;
        transform: translateY(-20px);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* Loading Animation */
.loading {
    position: relative;
    overflow: hidden;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    animation: loading 1.5s infinite;
}
