// Service Worker for Delivery Management System PWA
const CACHE_NAME = 'delivery-management-v1.0.0';
const STATIC_CACHE = 'static-v1.0.0';
const DYNAMIC_CACHE = 'dynamic-v1.0.0';

// Files to cache for offline functionality
const STATIC_FILES = [
    '/',
    '/index.html',
    '/styles/main.css',
    '/styles/neumorphic.css',
    '/js/main.js',
    '/js/storage.js',
    '/js/orders.js',
    '/js/drivers.js',
    '/js/customers.js',
    '/js/assignment.js',
    '/js/reports.js',
    '/js/accounting.js',
    '/js/order-classification.js',
    '/js/advanced-search.js',
    '/js/advanced-reports.js',
    '/js/user-management.js',
    '/js/notifications.js',
    '/js/sample-data.js',
    'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css'
];

// Install event - cache static files
self.addEventListener('install', (event) => {
    console.log('Service Worker: Installing...');
    
    event.waitUntil(
        caches.open(STATIC_CACHE)
            .then((cache) => {
                console.log('Service Worker: Caching static files');
                return cache.addAll(STATIC_FILES);
            })
            .then(() => {
                console.log('Service Worker: Static files cached successfully');
                return self.skipWaiting();
            })
            .catch((error) => {
                console.error('Service Worker: Error caching static files:', error);
            })
    );
});

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
    console.log('Service Worker: Activating...');
    
    event.waitUntil(
        caches.keys()
            .then((cacheNames) => {
                return Promise.all(
                    cacheNames.map((cacheName) => {
                        if (cacheName !== STATIC_CACHE && cacheName !== DYNAMIC_CACHE) {
                            console.log('Service Worker: Deleting old cache:', cacheName);
                            return caches.delete(cacheName);
                        }
                    })
                );
            })
            .then(() => {
                console.log('Service Worker: Activated successfully');
                return self.clients.claim();
            })
    );
});

// Fetch event - serve cached files or fetch from network
self.addEventListener('fetch', (event) => {
    const { request } = event;
    const url = new URL(request.url);

    // Skip non-GET requests
    if (request.method !== 'GET') {
        return;
    }

    // Skip chrome-extension and other non-http(s) requests
    if (!url.protocol.startsWith('http')) {
        return;
    }

    event.respondWith(
        caches.match(request)
            .then((cachedResponse) => {
                // Return cached version if available
                if (cachedResponse) {
                    console.log('Service Worker: Serving from cache:', request.url);
                    return cachedResponse;
                }

                // Fetch from network and cache dynamic content
                return fetch(request)
                    .then((networkResponse) => {
                        // Check if response is valid
                        if (!networkResponse || networkResponse.status !== 200 || networkResponse.type !== 'basic') {
                            return networkResponse;
                        }

                        // Clone response for caching
                        const responseToCache = networkResponse.clone();

                        // Cache dynamic content
                        caches.open(DYNAMIC_CACHE)
                            .then((cache) => {
                                console.log('Service Worker: Caching dynamic content:', request.url);
                                cache.put(request, responseToCache);
                            });

                        return networkResponse;
                    })
                    .catch((error) => {
                        console.error('Service Worker: Fetch failed:', error);
                        
                        // Return offline page for navigation requests
                        if (request.destination === 'document') {
                            return caches.match('/index.html');
                        }
                        
                        // Return empty response for other requests
                        return new Response('', {
                            status: 408,
                            statusText: 'Request Timeout'
                        });
                    });
            })
    );
});

// Background sync for offline data
self.addEventListener('sync', (event) => {
    console.log('Service Worker: Background sync triggered:', event.tag);
    
    if (event.tag === 'sync-orders') {
        event.waitUntil(syncOrders());
    }
    
    if (event.tag === 'sync-notifications') {
        event.waitUntil(syncNotifications());
    }
});

// Push notifications
self.addEventListener('push', (event) => {
    console.log('Service Worker: Push notification received');
    
    const options = {
        body: event.data ? event.data.text() : 'إشعار جديد من نظام إدارة التوصيل',
        icon: '/icon-192x192.png',
        badge: '/icon-72x72.png',
        vibrate: [200, 100, 200],
        data: {
            dateOfArrival: Date.now(),
            primaryKey: 1
        },
        actions: [
            {
                action: 'explore',
                title: 'عرض التفاصيل',
                icon: '/icon-192x192.png'
            },
            {
                action: 'close',
                title: 'إغلاق',
                icon: '/icon-192x192.png'
            }
        ]
    };
    
    event.waitUntil(
        self.registration.showNotification('نظام إدارة التوصيل', options)
    );
});

// Notification click handling
self.addEventListener('notificationclick', (event) => {
    console.log('Service Worker: Notification clicked:', event.action);
    
    event.notification.close();
    
    if (event.action === 'explore') {
        event.waitUntil(
            clients.openWindow('/')
        );
    }
});

// Message handling from main thread
self.addEventListener('message', (event) => {
    console.log('Service Worker: Message received:', event.data);
    
    if (event.data && event.data.type === 'SKIP_WAITING') {
        self.skipWaiting();
    }
    
    if (event.data && event.data.type === 'CACHE_URLS') {
        event.waitUntil(
            caches.open(DYNAMIC_CACHE)
                .then((cache) => {
                    return cache.addAll(event.data.payload);
                })
        );
    }
});

// Sync functions
async function syncOrders() {
    try {
        console.log('Service Worker: Syncing orders...');
        
        // Get pending orders from IndexedDB
        const pendingOrders = await getPendingOrders();
        
        if (pendingOrders.length > 0) {
            // Send to server when online
            for (const order of pendingOrders) {
                try {
                    await syncOrderToServer(order);
                    await markOrderAsSynced(order.id);
                } catch (error) {
                    console.error('Service Worker: Failed to sync order:', error);
                }
            }
        }
        
        console.log('Service Worker: Orders sync completed');
    } catch (error) {
        console.error('Service Worker: Orders sync failed:', error);
    }
}

async function syncNotifications() {
    try {
        console.log('Service Worker: Syncing notifications...');
        
        // Sync notification preferences and unread status
        const notifications = await getPendingNotifications();
        
        for (const notification of notifications) {
            try {
                await syncNotificationToServer(notification);
            } catch (error) {
                console.error('Service Worker: Failed to sync notification:', error);
            }
        }
        
        console.log('Service Worker: Notifications sync completed');
    } catch (error) {
        console.error('Service Worker: Notifications sync failed:', error);
    }
}

// Helper functions for IndexedDB operations
async function getPendingOrders() {
    // This would interface with IndexedDB to get pending orders
    // For now, return empty array
    return [];
}

async function syncOrderToServer(order) {
    // This would send the order to the server
    // For now, just log
    console.log('Service Worker: Syncing order to server:', order);
}

async function markOrderAsSynced(orderId) {
    // This would mark the order as synced in IndexedDB
    console.log('Service Worker: Marking order as synced:', orderId);
}

async function getPendingNotifications() {
    // This would get pending notifications from IndexedDB
    return [];
}

async function syncNotificationToServer(notification) {
    // This would sync notification to server
    console.log('Service Worker: Syncing notification to server:', notification);
}

// Periodic background tasks
self.addEventListener('periodicsync', (event) => {
    console.log('Service Worker: Periodic sync triggered:', event.tag);
    
    if (event.tag === 'daily-cleanup') {
        event.waitUntil(performDailyCleanup());
    }
});

async function performDailyCleanup() {
    try {
        console.log('Service Worker: Performing daily cleanup...');
        
        // Clean old cache entries
        const cacheNames = await caches.keys();
        for (const cacheName of cacheNames) {
            if (cacheName.startsWith('dynamic-')) {
                const cache = await caches.open(cacheName);
                const requests = await cache.keys();
                
                // Remove entries older than 7 days
                const oneWeekAgo = Date.now() - (7 * 24 * 60 * 60 * 1000);
                
                for (const request of requests) {
                    const response = await cache.match(request);
                    const dateHeader = response.headers.get('date');
                    
                    if (dateHeader && new Date(dateHeader).getTime() < oneWeekAgo) {
                        await cache.delete(request);
                        console.log('Service Worker: Removed old cache entry:', request.url);
                    }
                }
            }
        }
        
        console.log('Service Worker: Daily cleanup completed');
    } catch (error) {
        console.error('Service Worker: Daily cleanup failed:', error);
    }
}

// Error handling
self.addEventListener('error', (event) => {
    console.error('Service Worker: Error occurred:', event.error);
});

self.addEventListener('unhandledrejection', (event) => {
    console.error('Service Worker: Unhandled promise rejection:', event.reason);
});

console.log('Service Worker: Script loaded successfully');
