<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار content-section - نظام إدارة التوصيل</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            direction: rtl;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.2);
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e0e5ec;
        }

        .nav-buttons {
            display: flex;
            gap: 10px;
            margin-bottom: 30px;
            flex-wrap: wrap;
            justify-content: center;
        }

        .nav-btn {
            padding: 10px 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .nav-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        .nav-btn.active {
            background: #28a745;
        }

        .debug-info {
            background: #000;
            color: #0f0;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            margin-bottom: 20px;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }

        .content-section {
            display: none !important;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
            margin-bottom: 20px;
            border: 2px solid #dee2e6;
        }

        .content-section.active {
            display: block !important;
            border-color: #28a745;
            background: #f8fff9;
        }

        .section-content {
            text-align: center;
            padding: 40px;
        }

        .section-title {
            font-size: 2rem;
            color: #2d3748;
            margin-bottom: 10px;
        }

        .section-description {
            color: #4a5568;
            font-size: 1.1rem;
        }

        .test-buttons {
            display: flex;
            gap: 10px;
            margin: 20px 0;
            flex-wrap: wrap;
            justify-content: center;
        }

        .test-btn {
            padding: 8px 16px;
            background: #6c757d;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
        }

        .test-btn:hover {
            background: #5a6268;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 8px;
        }

        .status-active {
            background: #28a745;
        }

        .status-hidden {
            background: #dc3545;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 اختبار content-section</h1>
            <p>فحص وتشخيص مشكلة عرض الأقسام</p>
        </div>

        <div class="nav-buttons" id="navButtons">
            <button class="nav-btn active" onclick="showSection('dashboard')">لوحة التحكم</button>
            <button class="nav-btn" onclick="showSection('orders')">الطلبات</button>
            <button class="nav-btn" onclick="showSection('drivers')">المندوبين</button>
            <button class="nav-btn" onclick="showSection('customers')">العملاء</button>
            <button class="nav-btn" onclick="showSection('reports')">التقارير</button>
        </div>

        <div class="test-buttons">
            <button class="test-btn" onclick="debugSections()">🔍 فحص الأقسام</button>
            <button class="test-btn" onclick="fixSections()">🔧 إصلاح الأقسام</button>
            <button class="test-btn" onclick="testAllSections()">🧪 اختبار جميع الأقسام</button>
            <button class="test-btn" onclick="clearLog()">🗑️ مسح السجل</button>
        </div>

        <div class="debug-info" id="debugLog"></div>

        <!-- Test Sections -->
        <section id="dashboard" class="content-section active">
            <div class="section-content">
                <h2 class="section-title">📊 لوحة التحكم</h2>
                <p class="section-description">هذا هو قسم لوحة التحكم الرئيسية</p>
                <div>حالة القسم: <span class="status-indicator status-active"></span> نشط</div>
            </div>
        </section>

        <section id="orders" class="content-section">
            <div class="section-content">
                <h2 class="section-title">📦 إدارة الطلبات</h2>
                <p class="section-description">قسم إدارة وتتبع الطلبات</p>
                <div>حالة القسم: <span class="status-indicator status-hidden"></span> مخفي</div>
            </div>
        </section>

        <section id="drivers" class="content-section">
            <div class="section-content">
                <h2 class="section-title">🚗 إدارة المندوبين</h2>
                <p class="section-description">قسم إدارة المندوبين والعمولات</p>
                <div>حالة القسم: <span class="status-indicator status-hidden"></span> مخفي</div>
            </div>
        </section>

        <section id="customers" class="content-section">
            <div class="section-content">
                <h2 class="section-title">👥 إدارة العملاء</h2>
                <p class="section-description">قسم إدارة بيانات العملاء</p>
                <div>حالة القسم: <span class="status-indicator status-hidden"></span> مخفي</div>
            </div>
        </section>

        <section id="reports" class="content-section">
            <div class="section-content">
                <h2 class="section-title">📈 التقارير</h2>
                <p class="section-description">قسم التقارير والإحصائيات</p>
                <div>حالة القسم: <span class="status-indicator status-hidden"></span> مخفي</div>
            </div>
        </section>
    </div>

    <script>
        let debugLog = document.getElementById('debugLog');
        let currentSection = 'dashboard';

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const colors = {
                'info': '#0f0',
                'error': '#f00',
                'warning': '#ff0',
                'success': '#0f0'
            };
            
            const color = colors[type] || '#0f0';
            debugLog.innerHTML += `<span style="color: ${color}">[${timestamp}] ${message}</span>\n`;
            debugLog.scrollTop = debugLog.scrollHeight;
            console.log(message);
        }

        function showSection(sectionName) {
            log(`🔄 محاولة عرض القسم: ${sectionName}`, 'info');
            
            try {
                // إخفاء جميع الأقسام
                const allSections = document.querySelectorAll('.content-section');
                log(`📄 تم العثور على ${allSections.length} أقسام`, 'info');
                
                allSections.forEach(section => {
                    section.classList.remove('active');
                    const indicator = section.querySelector('.status-indicator');
                    if (indicator) {
                        indicator.className = 'status-indicator status-hidden';
                    }
                });

                // إظهار القسم المطلوب
                const targetSection = document.getElementById(sectionName);
                if (targetSection) {
                    targetSection.classList.add('active');
                    const indicator = targetSection.querySelector('.status-indicator');
                    if (indicator) {
                        indicator.className = 'status-indicator status-active';
                    }
                    
                    // تحديث أزرار التنقل
                    document.querySelectorAll('.nav-btn').forEach(btn => {
                        btn.classList.remove('active');
                    });
                    
                    const activeBtn = document.querySelector(`[onclick="showSection('${sectionName}')"]`);
                    if (activeBtn) {
                        activeBtn.classList.add('active');
                    }
                    
                    currentSection = sectionName;
                    log(`✅ تم عرض القسم ${sectionName} بنجاح`, 'success');
                } else {
                    log(`❌ لم يتم العثور على القسم ${sectionName}`, 'error');
                }
                
            } catch (error) {
                log(`❌ خطأ في عرض القسم: ${error.message}`, 'error');
            }
        }

        function debugSections() {
            log('🔍 بدء فحص الأقسام...', 'info');
            
            const sections = document.querySelectorAll('.content-section');
            sections.forEach(section => {
                const styles = window.getComputedStyle(section);
                const hasActive = section.classList.contains('active');
                
                log(`📄 ${section.id}:`, 'info');
                log(`  - Active Class: ${hasActive}`, 'info');
                log(`  - Display: ${styles.display}`, 'info');
                log(`  - Opacity: ${styles.opacity}`, 'info');
                log(`  - Visibility: ${styles.visibility}`, 'info');
                log(`  - Transform: ${styles.transform}`, 'info');
            });
            
            log('✅ انتهى فحص الأقسام', 'success');
        }

        function fixSections() {
            log('🔧 بدء إصلاح الأقسام...', 'warning');
            
            try {
                // إعادة تعيين جميع الأقسام
                document.querySelectorAll('.content-section').forEach(section => {
                    section.classList.remove('active');
                    section.style.display = '';
                    section.style.opacity = '';
                    section.style.visibility = '';
                    section.style.transform = '';
                });
                
                // إظهار القسم الحالي
                showSection(currentSection);
                
                log('✅ تم إصلاح الأقسام بنجاح', 'success');
            } catch (error) {
                log(`❌ خطأ في إصلاح الأقسام: ${error.message}`, 'error');
            }
        }

        function testAllSections() {
            log('🧪 بدء اختبار جميع الأقسام...', 'info');
            
            const sections = ['dashboard', 'orders', 'drivers', 'customers', 'reports'];
            let index = 0;
            
            function testNext() {
                if (index < sections.length) {
                    const section = sections[index];
                    log(`🎯 اختبار القسم: ${section}`, 'info');
                    showSection(section);
                    index++;
                    setTimeout(testNext, 1000);
                } else {
                    log('✅ انتهى اختبار جميع الأقسام', 'success');
                    showSection('dashboard');
                }
            }
            
            testNext();
        }

        function clearLog() {
            debugLog.innerHTML = '';
            log('🗑️ تم مسح السجل', 'info');
        }

        // تهيئة أولية
        window.addEventListener('load', () => {
            log('🚀 تم تحميل صفحة الاختبار', 'success');
            log('📋 الأوامر المتاحة:', 'info');
            log('  - showSection(name): عرض قسم معين', 'info');
            log('  - debugSections(): فحص حالة الأقسام', 'info');
            log('  - fixSections(): إصلاح الأقسام', 'info');
            log('  - testAllSections(): اختبار جميع الأقسام', 'info');
            
            debugSections();
        });

        // جعل الدوال متاحة عالمياً
        window.showSection = showSection;
        window.debugSections = debugSections;
        window.fixSections = fixSections;
        window.testAllSections = testAllSections;
    </script>
</body>
</html>
