<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار تسجيل الدخول</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f0f0f3;
            direction: rtl;
        }
        .test-container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
        }
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            opacity: 0.9;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 5px;
            background: #e9ecef;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>اختبار نظام تسجيل الدخول</h1>
        
        <div class="test-section">
            <h3>1. اختبار Storage</h3>
            <button class="btn" onclick="testStorage()">اختبار Storage</button>
            <div id="storageResult" class="result"></div>
        </div>
        
        <div class="test-section">
            <h3>2. إنشاء المستخدمين</h3>
            <button class="btn" onclick="createUsers()">إنشاء المستخدمين</button>
            <div id="usersResult" class="result"></div>
        </div>
        
        <div class="test-section">
            <h3>3. عرض المستخدمين</h3>
            <button class="btn" onclick="showUsers()">عرض المستخدمين</button>
            <div id="showUsersResult" class="result"></div>
        </div>
        
        <div class="test-section">
            <h3>4. اختبار تسجيل الدخول</h3>
            <button class="btn" onclick="testLogin('admin', 'admin123')">تسجيل دخول Admin</button>
            <button class="btn" onclick="testLogin('manager', 'manager123')">تسجيل دخول Manager</button>
            <button class="btn" onclick="testLogin('wrong', 'wrong')">تسجيل دخول خاطئ</button>
            <div id="loginResult" class="result"></div>
        </div>
        
        <div class="test-section">
            <h3>5. مسح البيانات</h3>
            <button class="btn" onclick="clearData()">مسح جميع البيانات</button>
            <div id="clearResult" class="result"></div>
        </div>
        
        <div class="test-section">
            <h3>6. الذهاب لصفحة تسجيل الدخول</h3>
            <button class="btn" onclick="window.location.href='login.html'">صفحة تسجيل الدخول</button>
        </div>
    </div>

    <script src="js/storage.js"></script>
    <script>
        let loginSystem;
        
        // Wait for storage to be ready
        async function waitForStorage() {
            return new Promise((resolve) => {
                const check = () => {
                    if (window.storage && window.storage.isReady) {
                        resolve();
                    } else {
                        setTimeout(check, 100);
                    }
                };
                check();
            });
        }
        
        async function testStorage() {
            const result = document.getElementById('storageResult');
            try {
                await waitForStorage();
                result.innerHTML = '<span class="success">✅ Storage جاهز للاستخدام</span>';
            } catch (error) {
                result.innerHTML = `<span class="error">❌ خطأ في Storage: ${error.message}</span>`;
            }
        }
        
        async function createUsers() {
            const result = document.getElementById('usersResult');
            try {
                await waitForStorage();
                
                const users = [
                    {
                        id: 1,
                        username: 'admin',
                        password: 'admin123',
                        name: 'مدير النظام',
                        role: 'admin',
                        active: true
                    },
                    {
                        id: 2,
                        username: 'manager',
                        password: 'manager123',
                        name: 'مدير العمليات',
                        role: 'manager',
                        active: true
                    },
                    {
                        id: 3,
                        username: 'accountant',
                        password: 'accountant123',
                        name: 'المحاسب',
                        role: 'accountant',
                        active: true
                    }
                ];
                
                for (const user of users) {
                    await storage.add('users', user);
                }
                
                result.innerHTML = '<span class="success">✅ تم إنشاء المستخدمين بنجاح</span>';
            } catch (error) {
                result.innerHTML = `<span class="error">❌ خطأ في إنشاء المستخدمين: ${error.message}</span>`;
            }
        }
        
        async function showUsers() {
            const result = document.getElementById('showUsersResult');
            try {
                await waitForStorage();
                const users = await storage.getAll('users') || [];
                
                if (users.length === 0) {
                    result.innerHTML = '<span class="info">ℹ️ لا توجد مستخدمين. قم بإنشائهم أولاً.</span>';
                } else {
                    let html = '<span class="success">✅ المستخدمين الموجودين:</span><br>';
                    users.forEach(user => {
                        html += `<br>• ${user.name} (${user.username} / ${user.password}) - ${user.role}`;
                    });
                    result.innerHTML = html;
                }
            } catch (error) {
                result.innerHTML = `<span class="error">❌ خطأ في عرض المستخدمين: ${error.message}</span>`;
            }
        }
        
        async function testLogin(username, password) {
            const result = document.getElementById('loginResult');
            try {
                await waitForStorage();
                const users = await storage.getAll('users') || [];
                
                const user = users.find(u => 
                    u.username === username && 
                    u.password === password && 
                    u.active
                );
                
                if (user) {
                    result.innerHTML = `<span class="success">✅ تسجيل دخول ناجح: ${user.name}</span>`;
                } else {
                    result.innerHTML = `<span class="error">❌ فشل تسجيل الدخول: ${username}</span>`;
                }
            } catch (error) {
                result.innerHTML = `<span class="error">❌ خطأ في اختبار تسجيل الدخول: ${error.message}</span>`;
            }
        }
        
        async function clearData() {
            const result = document.getElementById('clearResult');
            try {
                localStorage.clear();
                sessionStorage.clear();
                
                // Clear IndexedDB
                if (window.storage && window.storage.db) {
                    const stores = ['users', 'orders', 'drivers', 'customers'];
                    for (const store of stores) {
                        try {
                            const items = await storage.getAll(store);
                            for (const item of items) {
                                await storage.delete(store, item.id);
                            }
                        } catch (e) {
                            // Store might not exist
                        }
                    }
                }
                
                result.innerHTML = '<span class="success">✅ تم مسح جميع البيانات</span>';
            } catch (error) {
                result.innerHTML = `<span class="error">❌ خطأ في مسح البيانات: ${error.message}</span>`;
            }
        }
        
        // Auto-test on load
        window.addEventListener('load', async () => {
            console.log('Test page loaded');
            await testStorage();
        });
    </script>
</body>
</html>
