# 🚀 دليل التشغيل - نظام إدارة شركة التوصيل

## 📋 **متطلبات التشغيل**

### الحد الأدنى:
- متصفح حديث يدعم ES6+ (Chrome 60+, Firefox 55+, Safari 12+)
- خ<PERSON><PERSON> ويب محلي (Apache, Nginx, أو خادم تطوير)
- ذاكرة: 2GB RAM
- مساحة تخزين: 100MB

### الموصى به:
- متصفح Chrome أو Edge الحديث
- ذاكرة: 4GB RAM
- اتصال إنترنت مستقر

## 🔧 **خطوات التشغيل**

### 1. تحضير الملفات:
```bash
# تأكد من وجود جميع الملفات في المجلد:
delivery-management-system/
├── index.html              # الصفحة الرئيسية
├── login.html              # صفحة تسجيل الدخول
├── manifest.json           # ملف PWA
├── sw.js                   # Service Worker
├── styles/                 # ملفات CSS
├── js/                     # ملفات JavaScript
└── icons/                  # أيقونات PWA (اختيارية)
```

### 2. تشغيل خادم محلي:

#### باستخدام Python:
```bash
# Python 3
python -m http.server 8000

# Python 2
python -m SimpleHTTPServer 8000
```

#### باستخدام Node.js:
```bash
# تثبيت serve عالمياً
npm install -g serve

# تشغيل الخادم
serve . -p 8000
```

#### باستخدام PHP:
```bash
php -S localhost:8000
```

#### باستخدام Live Server (VS Code):
1. تثبيت إضافة Live Server
2. النقر بالزر الأيمن على index.html
3. اختيار "Open with Live Server"

### 3. الوصول للنظام:
1. افتح المتصفح
2. اذهب إلى `http://localhost:8000`
3. سيتم توجيهك تلقائياً لصفحة تسجيل الدخول

## 🔐 **بيانات تسجيل الدخول**

### الحسابات الافتراضية:

#### مدير النظام:
- **اسم المستخدم**: admin
- **كلمة المرور**: admin123
- **الصلاحيات**: جميع الصلاحيات

#### مدير العمليات:
- **اسم المستخدم**: manager
- **كلمة المرور**: manager123
- **الصلاحيات**: إدارة الطلبات والمندوبين والتقارير

#### المحاسب:
- **اسم المستخدم**: accountant
- **كلمة المرور**: accountant123
- **الصلاحيات**: التقارير المالية والمحاسبة

## 🎯 **البدء السريع**

### 1. تسجيل الدخول:
- استخدم أحد الحسابات المذكورة أعلاه
- أو انقر على أحد الحسابات التجريبية في صفحة تسجيل الدخول

### 2. استكشاف النظام:
- **لوحة التحكم**: عرض الإحصائيات العامة
- **الطلبات**: إدارة طلبات التوصيل
- **المندوبين**: إدارة المندوبين والعمولات
- **العملاء**: قاعدة بيانات العملاء
- **الإسناد**: إسناد الطلبات للمندوبين
- **التقارير**: تقارير مفصلة وإحصائيات
- **المحاسبة**: النظام المحاسبي المتكامل

### 3. إضافة بيانات جديدة:
- ابدأ بإضافة مندوبين جدد
- أضف عملاء أو شركات
- أنشئ طلبات توصيل جديدة
- قم بإسناد الطلبات للمندوبين

## 🔧 **إعدادات متقدمة**

### تفعيل HTTPS (للـ PWA):
```bash
# باستخدام mkcert لشهادة محلية
mkcert -install
mkcert localhost 127.0.0.1 ::1

# تشغيل خادم HTTPS
python -m http.server 8000 --bind 127.0.0.1 --directory . --cgi
```

### تخصيص الإعدادات:
1. **العملة**: تعديل متغير `currency` في ملفات JS
2. **اللغة**: تعديل النصوص في الملفات
3. **الألوان**: تعديل متغيرات CSS في `styles/neumorphic.css`

## 📱 **تثبيت كتطبيق PWA**

### على الكمبيوتر:
1. افتح النظام في Chrome
2. انقر على أيقونة التثبيت في شريط العنوان
3. اتبع التعليمات

### على الهاتف:
1. افتح النظام في المتصفح
2. اختر "إضافة إلى الشاشة الرئيسية"
3. أكد التثبيت

## 🐛 **حل المشاكل الشائعة**

### المشكلة: البيانات لا تظهر
**الحل**: 
- تأكد من تفعيل JavaScript
- امسح cache المتصفح
- تحقق من console للأخطاء

### المشكلة: صفحة تسجيل الدخول لا تعمل
**الحل**:
- تأكد من وجود ملف `login.html`
- تحقق من مسار الملفات
- تأكد من تشغيل خادم محلي

### المشكلة: الإشعارات لا تعمل
**الحل**:
- اسمح بالإشعارات في المتصفح
- تأكد من تشغيل النظام عبر HTTPS أو localhost

### المشكلة: بطء في التحميل
**الحل**:
- استخدم Chrome للأداء الأفضل
- امسح cache المتصفح
- تأكد من سرعة الاتصال

## 📊 **البيانات التجريبية**

النظام يأتي مع بيانات تجريبية تشمل:
- 3 مندوبين
- 15 طلب توصيل
- 5 عملاء
- بيانات محاسبية أساسية

يمكنك حذف هذه البيانات من قسم الإعدادات أو إعادة تعيين النظام.

## 🔄 **النسخ الاحتياطية**

### تصدير البيانات:
1. اذهب إلى قسم الإعدادات
2. انقر على "تصدير البيانات"
3. احفظ الملف في مكان آمن

### استيراد البيانات:
1. اذهب إلى قسم الإعدادات
2. انقر على "استيراد البيانات"
3. اختر ملف النسخة الاحتياطية

## 📞 **الدعم التقني**

في حالة مواجهة مشاكل:
1. تحقق من console المتصفح للأخطاء
2. تأكد من تحديث المتصفح
3. جرب متصفح مختلف
4. امسح البيانات المحلية وأعد التشغيل

## 🎉 **مبروك!**

النظام الآن جاهز للاستخدام. استمتع بإدارة شركة التوصيل بكفاءة وسهولة!

---

**ملاحظة**: هذا النظام مصمم للاستخدام المحلي. للاستخدام الإنتاجي، يُنصح بإعداد خادم مخصص وقاعدة بيانات خارجية.
