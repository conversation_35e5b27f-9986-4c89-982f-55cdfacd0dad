// Orders Management System
class OrdersManager {
    constructor() {
        this.orders = [];
        this.filteredOrders = [];
        this.currentEditingOrder = null;
        this.init();
    }

    async init() {
        this.setupEventListeners();
        await this.loadOrders();
        this.setupFilters();
    }

    setupEventListeners() {
        // Add order button
        const addOrderBtn = document.getElementById('addOrderBtn');
        if (addOrderBtn) {
            addOrderBtn.addEventListener('click', () => this.showAddOrderModal());
        }

        // Search functionality
        const searchInput = document.getElementById('searchOrders');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => this.handleSearch(e.target.value));
        }

        // Filter functionality
        const statusFilter = document.getElementById('statusFilter');
        const cityFilter = document.getElementById('cityFilter');
        const dateFilter = document.getElementById('dateFilter');

        if (statusFilter) {
            statusFilter.addEventListener('change', () => this.applyFilters());
        }
        if (cityFilter) {
            cityFilter.addEventListener('change', () => this.applyFilters());
        }
        if (dateFilter) {
            dateFilter.addEventListener('change', () => this.applyFilters());
        }
    }

    async loadOrders() {
        try {
            this.orders = await storage.getAll('orders');
            this.filteredOrders = [...this.orders];
            this.renderOrders();
            this.updateCityFilter();
        } catch (error) {
            console.error('Error loading orders:', error);
            window.app.showNotification('خطأ في تحميل الطلبات', 'error');
        }
    }

    renderOrders() {
        const tbody = document.getElementById('ordersTableBody');
        if (!tbody) return;

        if (this.filteredOrders.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="8" class="text-center">لا توجد طلبات</td>
                </tr>
            `;
            return;
        }

        tbody.innerHTML = this.filteredOrders.map(order => `
            <tr>
                <td>#${order.id}</td>
                <td>${order.clientName}</td>
                <td>${order.customerName}</td>
                <td>${order.city}</td>
                <td>${order.deliveryPrice} د.ع</td>
                <td>
                    <span class="status-badge status-${order.status}">
                        ${this.getStatusText(order.status)}
                    </span>
                </td>
                <td>${this.formatDate(order.date)}</td>
                <td>
                    <div class="action-buttons">
                        <button class="btn-icon" onclick="ordersManager.editOrder(${order.id})" title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn-icon" onclick="ordersManager.changeStatus(${order.id})" title="تغيير الحالة">
                            <i class="fas fa-exchange-alt"></i>
                        </button>
                        <button class="btn-icon" onclick="ordersManager.deleteOrder(${order.id})" title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');
    }

    showAddOrderModal() {
        const modalContent = `
            <div class="modal">
                <div class="modal-header">
                    <h3 class="modal-title">إضافة طلب جديد</h3>
                    <button class="modal-close">&times;</button>
                </div>
                <div class="modal-body">
                    <form id="orderForm">
                        <div class="form-group">
                            <label class="form-label">اسم العميل/الشركة *</label>
                            <input type="text" class="form-input" name="clientName" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">اسم المرسل *</label>
                            <input type="text" class="form-input" name="senderName" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">رقم هاتف المرسل *</label>
                            <input type="tel" class="form-input" name="senderPhone" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">اسم الزبون *</label>
                            <input type="text" class="form-input" name="customerName" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">رقم هاتف الزبون *</label>
                            <input type="tel" class="form-input" name="customerPhone" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">المحافظة/المدينة *</label>
                            <select class="form-select" name="city" required>
                                <option value="">اختر المحافظة</option>
                                <option value="بغداد">بغداد</option>
                                <option value="البصرة">البصرة</option>
                                <option value="أربيل">أربيل</option>
                                <option value="النجف">النجف</option>
                                <option value="كربلاء">كربلاء</option>
                                <option value="الموصل">الموصل</option>
                                <option value="السليمانية">السليمانية</option>
                                <option value="دهوك">دهوك</option>
                                <option value="الأنبار">الأنبار</option>
                                <option value="بابل">بابل</option>
                                <option value="ديالى">ديالى</option>
                                <option value="ذي قار">ذي قار</option>
                                <option value="المثنى">المثنى</option>
                                <option value="القادسية">القادسية</option>
                                <option value="واسط">واسط</option>
                                <option value="ميسان">ميسان</option>
                                <option value="كركوك">كركوك</option>
                                <option value="صلاح الدين">صلاح الدين</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">العنوان التفصيلي *</label>
                            <textarea class="form-textarea" name="address" required></textarea>
                        </div>
                        <div class="form-group">
                            <label class="form-label">سعر التوصيل (د.ع) *</label>
                            <input type="number" class="form-input" name="deliveryPrice" min="0" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">المندوب</label>
                            <select class="form-select" name="driverId" id="driverSelect">
                                <option value="">اختر المندوب</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">ملاحظات</label>
                            <textarea class="form-textarea" name="notes"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn-secondary modal-close">إلغاء</button>
                    <button type="button" class="btn-primary" onclick="ordersManager.saveOrder()">حفظ الطلب</button>
                </div>
            </div>
        `;

        window.app.showModal(modalContent);
        this.loadDriversForSelect();
    }

    async loadDriversForSelect() {
        try {
            const drivers = await storage.getAll('drivers');
            const select = document.getElementById('driverSelect');
            if (select) {
                select.innerHTML = '<option value="">اختر المندوب</option>' +
                    drivers.map(driver => `<option value="${driver.id}">${driver.name}</option>`).join('');
            }
        } catch (error) {
            console.error('Error loading drivers:', error);
        }
    }

    async saveOrder() {
        const form = document.getElementById('orderForm');
        const formData = new FormData(form);
        
        if (!form.checkValidity()) {
            form.reportValidity();
            return;
        }

        const orderData = {
            clientName: formData.get('clientName'),
            senderName: formData.get('senderName'),
            senderPhone: formData.get('senderPhone'),
            customerName: formData.get('customerName'),
            customerPhone: formData.get('customerPhone'),
            city: formData.get('city'),
            address: formData.get('address'),
            deliveryPrice: parseFloat(formData.get('deliveryPrice')),
            driverId: formData.get('driverId') || null,
            notes: formData.get('notes') || '',
            status: 'pending',
            date: new Date().toISOString(),
            createdAt: new Date().toISOString()
        };

        try {
            if (this.currentEditingOrder) {
                orderData.id = this.currentEditingOrder.id;
                await storage.update('orders', orderData);
                window.app.showNotification('تم تحديث الطلب بنجاح', 'success');
            } else {
                await storage.add('orders', orderData);
                window.app.showNotification('تم إضافة الطلب بنجاح', 'success');
            }

            this.currentEditingOrder = null;
            window.app.closeModal();
            await this.loadOrders();
        } catch (error) {
            console.error('Error saving order:', error);
            window.app.showNotification('خطأ في حفظ الطلب', 'error');
        }
    }

    async editOrder(orderId) {
        try {
            const order = await storage.getById('orders', orderId);
            if (!order) {
                window.app.showNotification('الطلب غير موجود', 'error');
                return;
            }

            this.currentEditingOrder = order;
            this.showAddOrderModal();

            // Fill form with order data
            setTimeout(() => {
                const form = document.getElementById('orderForm');
                if (form) {
                    form.clientName.value = order.clientName;
                    form.senderName.value = order.senderName;
                    form.senderPhone.value = order.senderPhone;
                    form.customerName.value = order.customerName;
                    form.customerPhone.value = order.customerPhone;
                    form.city.value = order.city;
                    form.address.value = order.address;
                    form.deliveryPrice.value = order.deliveryPrice;
                    form.driverId.value = order.driverId || '';
                    form.notes.value = order.notes || '';
                }

                // Update modal title
                const modalTitle = document.querySelector('.modal-title');
                if (modalTitle) {
                    modalTitle.textContent = 'تعديل الطلب';
                }
            }, 100);

        } catch (error) {
            console.error('Error loading order for edit:', error);
            window.app.showNotification('خطأ في تحميل الطلب', 'error');
        }
    }

    changeStatus(orderId) {
        const modalContent = `
            <div class="modal">
                <div class="modal-header">
                    <h3 class="modal-title">تغيير حالة الطلب</h3>
                    <button class="modal-close">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="form-group">
                        <label class="form-label">الحالة الجديدة:</label>
                        <select class="form-select" id="newStatus">
                            <option value="pending">معلق</option>
                            <option value="delivered">مسلم</option>
                            <option value="returned">راجع</option>
                            <option value="partial_return">راجع جزئي</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">ملاحظات:</label>
                        <textarea class="form-textarea" id="statusNotes" placeholder="ملاحظات حول تغيير الحالة..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn-secondary modal-close">إلغاء</button>
                    <button type="button" class="btn-primary" onclick="ordersManager.updateOrderStatus(${orderId})">تحديث الحالة</button>
                </div>
            </div>
        `;

        window.app.showModal(modalContent);
    }

    async updateOrderStatus(orderId) {
        const newStatus = document.getElementById('newStatus').value;
        const statusNotes = document.getElementById('statusNotes').value;

        try {
            const order = await storage.getById('orders', orderId);
            if (!order) {
                window.app.showNotification('الطلب غير موجود', 'error');
                return;
            }

            order.status = newStatus;
            order.statusNotes = statusNotes;
            order.statusUpdatedAt = new Date().toISOString();

            await storage.update('orders', order);
            window.app.showNotification('تم تحديث حالة الطلب بنجاح', 'success');
            window.app.closeModal();
            await this.loadOrders();

        } catch (error) {
            console.error('Error updating order status:', error);
            window.app.showNotification('خطأ في تحديث حالة الطلب', 'error');
        }
    }

    async deleteOrder(orderId) {
        if (!confirm('هل أنت متأكد من حذف هذا الطلب؟')) {
            return;
        }

        try {
            await storage.delete('orders', orderId);
            window.app.showNotification('تم حذف الطلب بنجاح', 'success');
            await this.loadOrders();
        } catch (error) {
            console.error('Error deleting order:', error);
            window.app.showNotification('خطأ في حذف الطلب', 'error');
        }
    }

    handleSearch(searchTerm) {
        if (!searchTerm.trim()) {
            this.filteredOrders = [...this.orders];
        } else {
            const term = searchTerm.toLowerCase();
            this.filteredOrders = this.orders.filter(order =>
                order.clientName.toLowerCase().includes(term) ||
                order.customerName.toLowerCase().includes(term) ||
                order.customerPhone.includes(term) ||
                order.senderPhone.includes(term) ||
                order.city.toLowerCase().includes(term)
            );
        }
        this.renderOrders();
    }

    applyFilters() {
        const statusFilter = document.getElementById('statusFilter').value;
        const cityFilter = document.getElementById('cityFilter').value;
        const dateFilter = document.getElementById('dateFilter').value;

        this.filteredOrders = this.orders.filter(order => {
            let matches = true;

            if (statusFilter && order.status !== statusFilter) {
                matches = false;
            }

            if (cityFilter && order.city !== cityFilter) {
                matches = false;
            }

            if (dateFilter) {
                const orderDate = new Date(order.date).toDateString();
                const filterDate = new Date(dateFilter).toDateString();
                if (orderDate !== filterDate) {
                    matches = false;
                }
            }

            return matches;
        });

        this.renderOrders();
    }

    updateCityFilter() {
        const cityFilter = document.getElementById('cityFilter');
        if (!cityFilter) return;

        const cities = [...new Set(this.orders.map(order => order.city))].sort();
        cityFilter.innerHTML = '<option value="">جميع المحافظات</option>' +
            cities.map(city => `<option value="${city}">${city}</option>`).join('');
    }

    getStatusText(status) {
        const statusMap = {
            'delivered': 'مسلم',
            'pending': 'معلق',
            'returned': 'راجع',
            'partial_return': 'راجع جزئي'
        };
        return statusMap[status] || status;
    }

    formatDate(dateString) {
        return new Date(dateString).toLocaleDateString('ar-IQ');
    }
}

// Initialize orders manager
document.addEventListener('DOMContentLoaded', () => {
    window.ordersManager = new OrdersManager();
});
