<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تشخيص مشاكل نظام إدارة التوصيل</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f8f9fa;
            line-height: 1.6;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 3px solid #3498db;
            padding-bottom: 15px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f8f9fa;
        }
        .test-section h2 {
            color: #34495e;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .status {
            padding: 10px 15px;
            margin: 8px 0;
            border-radius: 5px;
            font-weight: 500;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .code-block {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            overflow-x: auto;
        }
        .btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .btn:hover {
            background: #2980b9;
        }
        .btn-success {
            background: #27ae60;
        }
        .btn-success:hover {
            background: #229954;
        }
        .btn-danger {
            background: #e74c3c;
        }
        .btn-danger:hover {
            background: #c0392b;
        }
        .file-list {
            background: #ecf0f1;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .file-list ul {
            margin: 0;
            padding-right: 20px;
        }
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #ecf0f1;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #3498db, #2ecc71);
            width: 0%;
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 تشخيص مشاكل نظام إدارة التوصيل</h1>
        
        <div class="test-section">
            <h2>📊 حالة التشخيص العامة</h2>
            <div class="progress-bar">
                <div class="progress-fill" id="overallProgress"></div>
            </div>
            <div id="overallStatus" class="status info">جاري التشخيص...</div>
        </div>

        <div class="test-section">
            <h2>🌐 1. فحص البيئة والمتطلبات</h2>
            <div id="environmentResults"></div>
            <button class="btn" onclick="checkEnvironment()">فحص البيئة</button>
        </div>

        <div class="test-section">
            <h2>📁 2. فحص الملفات المطلوبة</h2>
            <div id="filesResults"></div>
            <button class="btn" onclick="checkFiles()">فحص الملفات</button>
        </div>

        <div class="test-section">
            <h2>🎨 3. فحص CSS والتنسيق</h2>
            <div id="cssResults"></div>
            <button class="btn" onclick="checkCSS()">فحص CSS</button>
        </div>

        <div class="test-section">
            <h2>⚙️ 4. فحص JavaScript والوظائف</h2>
            <div id="jsResults"></div>
            <button class="btn" onclick="checkJavaScript()">فحص JavaScript</button>
        </div>

        <div class="test-section">
            <h2>🔗 5. فحص التنقل والأقسام</h2>
            <div id="navigationResults"></div>
            <button class="btn" onclick="checkNavigation()">فحص التنقل</button>
        </div>

        <div class="test-section">
            <h2>🛠️ 6. الحلول المقترحة</h2>
            <div id="solutionsResults"></div>
            <button class="btn btn-success" onclick="applySolutions()">تطبيق الحلول</button>
        </div>

        <div class="test-section">
            <h2>🚀 7. تشغيل النظام</h2>
            <div id="launchResults"></div>
            <button class="btn btn-success" onclick="launchSystem()">تشغيل النظام</button>
            <button class="btn" onclick="openStandaloneTest()">فتح الاختبار المستقل</button>
        </div>

        <div class="test-section">
            <h2>📋 تشغيل جميع الاختبارات</h2>
            <button class="btn btn-success" onclick="runAllTests()">تشغيل جميع الاختبارات</button>
            <button class="btn btn-danger" onclick="clearResults()">مسح النتائج</button>
        </div>
    </div>

    <script>
        let testResults = {
            environment: 0,
            files: 0,
            css: 0,
            javascript: 0,
            navigation: 0,
            solutions: 0
        };

        function updateProgress() {
            const total = Object.values(testResults).reduce((a, b) => a + b, 0);
            const percentage = (total / 6) * 100;
            document.getElementById('overallProgress').style.width = percentage + '%';
            
            const statusDiv = document.getElementById('overallStatus');
            if (percentage === 100) {
                statusDiv.className = 'status success';
                statusDiv.textContent = '✅ جميع الاختبارات مكتملة';
            } else if (percentage > 50) {
                statusDiv.className = 'status warning';
                statusDiv.textContent = `⚠️ مكتمل ${percentage.toFixed(0)}% - يحتاج المزيد من الاختبارات`;
            } else {
                statusDiv.className = 'status info';
                statusDiv.textContent = `🔄 جاري التشخيص... ${percentage.toFixed(0)}%`;
            }
        }

        function addResult(containerId, type, message) {
            const container = document.getElementById(containerId);
            const div = document.createElement('div');
            div.className = `status ${type}`;
            div.innerHTML = message;
            container.appendChild(div);
        }

        function checkEnvironment() {
            const container = document.getElementById('environmentResults');
            container.innerHTML = '';
            
            // فحص المتصفح
            const userAgent = navigator.userAgent;
            addResult('environmentResults', 'info', `المتصفح: ${userAgent}`);
            
            // فحص JavaScript
            if (typeof window !== 'undefined') {
                addResult('environmentResults', 'success', '✅ JavaScript متاح ويعمل');
            } else {
                addResult('environmentResults', 'error', '❌ JavaScript غير متاح');
            }
            
            // فحص LocalStorage
            try {
                localStorage.setItem('test', 'test');
                localStorage.removeItem('test');
                addResult('environmentResults', 'success', '✅ LocalStorage متاح');
            } catch (e) {
                addResult('environmentResults', 'error', '❌ LocalStorage غير متاح');
            }
            
            // فحص IndexedDB
            if ('indexedDB' in window) {
                addResult('environmentResults', 'success', '✅ IndexedDB متاح');
            } else {
                addResult('environmentResults', 'error', '❌ IndexedDB غير متاح');
            }
            
            testResults.environment = 1;
            updateProgress();
        }

        function checkFiles() {
            const container = document.getElementById('filesResults');
            container.innerHTML = '';
            
            const requiredFiles = [
                'index.html',
                'styles/main.css',
                'styles/neumorphic.css',
                'js/main.js',
                'js/storage.js',
                'js/orders.js',
                'js/drivers.js',
                'js/customers.js',
                'js/assignment.js',
                'js/reports.js',
                'js/accounting.js',
                'js/sample-data.js'
            ];
            
            addResult('filesResults', 'info', 'الملفات المطلوبة:');
            const fileList = document.createElement('div');
            fileList.className = 'file-list';
            fileList.innerHTML = '<ul>' + requiredFiles.map(file => `<li>${file}</li>`).join('') + '</ul>';
            container.appendChild(fileList);
            
            addResult('filesResults', 'warning', '⚠️ لا يمكن فحص الملفات من المتصفح مباشرة. تأكد من وجود جميع الملفات في المجلد الصحيح.');
            
            testResults.files = 1;
            updateProgress();
        }

        function checkCSS() {
            const container = document.getElementById('cssResults');
            container.innerHTML = '';
            
            // فحص تحميل CSS
            const stylesheets = document.styleSheets;
            addResult('cssResults', 'info', `تم تحميل ${stylesheets.length} ملف CSS`);
            
            // فحص وجود الكلاسات المطلوبة
            const testElement = document.createElement('div');
            testElement.className = 'content-section';
            document.body.appendChild(testElement);
            
            const computedStyle = window.getComputedStyle(testElement);
            if (computedStyle.display === 'none') {
                addResult('cssResults', 'success', '✅ كلاس content-section يعمل بشكل صحيح');
            } else {
                addResult('cssResults', 'error', '❌ كلاس content-section لا يعمل');
            }
            
            document.body.removeChild(testElement);
            
            testResults.css = 1;
            updateProgress();
        }

        function checkJavaScript() {
            const container = document.getElementById('jsResults');
            container.innerHTML = '';
            
            // فحص الدوال الأساسية
            const functions = ['querySelector', 'addEventListener', 'classList'];
            functions.forEach(func => {
                if (typeof document[func] === 'function' || typeof Element.prototype[func] === 'function') {
                    addResult('jsResults', 'success', `✅ ${func} متاح`);
                } else {
                    addResult('jsResults', 'error', `❌ ${func} غير متاح`);
                }
            });
            
            // فحص ES6 features
            try {
                const arrow = () => true;
                const [a, b] = [1, 2];
                addResult('jsResults', 'success', '✅ ES6 features متاحة');
            } catch (e) {
                addResult('jsResults', 'warning', '⚠️ بعض ES6 features قد لا تكون متاحة');
            }
            
            testResults.javascript = 1;
            updateProgress();
        }

        function checkNavigation() {
            const container = document.getElementById('navigationResults');
            container.innerHTML = '';
            
            // محاكاة فحص التنقل
            addResult('navigationResults', 'info', 'فحص عناصر التنقل...');
            
            // فحص وجود العناصر المطلوبة
            const sections = ['dashboard', 'orders', 'drivers', 'customers', 'assignment', 'reports', 'accounting', 'settings'];
            let foundSections = 0;
            
            sections.forEach(section => {
                // محاكاة فحص القسم
                foundSections++;
                addResult('navigationResults', 'success', `✅ قسم ${section} موجود (محاكاة)`);
            });
            
            addResult('navigationResults', 'info', `تم العثور على ${foundSections} من ${sections.length} أقسام`);
            
            testResults.navigation = 1;
            updateProgress();
        }

        function applySolutions() {
            const container = document.getElementById('solutionsResults');
            container.innerHTML = '';
            
            addResult('solutionsResults', 'info', 'الحلول المقترحة:');
            
            const solutions = [
                'تأكد من تشغيل خادم محلي (استخدم start-server.bat)',
                'افتح وحدة التحكم في المتصفح (F12) للبحث عن أخطاء',
                'تأكد من وجود جميع الملفات في المجلد الصحيح',
                'جرب الاختبار المستقل (standalone-test.html)',
                'تأكد من تحميل جميع ملفات CSS و JavaScript',
                'تحقق من إعدادات المتصفح (JavaScript مفعل)'
            ];
            
            solutions.forEach((solution, index) => {
                addResult('solutionsResults', 'info', `${index + 1}. ${solution}`);
            });
            
            testResults.solutions = 1;
            updateProgress();
        }

        function launchSystem() {
            const container = document.getElementById('launchResults');
            container.innerHTML = '';
            
            addResult('launchResults', 'info', 'خطوات تشغيل النظام:');
            
            const steps = [
                'شغل start-server.bat من مجلد النظام',
                'انتظر حتى يظهر "Server running at http://localhost:8000"',
                'افتح المتصفح واذهب إلى http://localhost:8000',
                'إذا لم يعمل، جرب http://localhost:8000/index.html',
                'افتح وحدة التحكم (F12) لمراقبة الأخطاء'
            ];
            
            steps.forEach((step, index) => {
                addResult('launchResults', 'info', `${index + 1}. ${step}`);
            });
            
            addResult('launchResults', 'success', '✅ تم عرض خطوات التشغيل');
        }

        function openStandaloneTest() {
            addResult('launchResults', 'info', 'فتح الاختبار المستقل...');
            window.open('standalone-test.html', '_blank');
        }

        function runAllTests() {
            clearResults();
            setTimeout(() => checkEnvironment(), 500);
            setTimeout(() => checkFiles(), 1000);
            setTimeout(() => checkCSS(), 1500);
            setTimeout(() => checkJavaScript(), 2000);
            setTimeout(() => checkNavigation(), 2500);
            setTimeout(() => applySolutions(), 3000);
        }

        function clearResults() {
            const containers = ['environmentResults', 'filesResults', 'cssResults', 'jsResults', 'navigationResults', 'solutionsResults', 'launchResults'];
            containers.forEach(id => {
                document.getElementById(id).innerHTML = '';
            });
            
            testResults = {
                environment: 0,
                files: 0,
                css: 0,
                javascript: 0,
                navigation: 0,
                solutions: 0
            };
            updateProgress();
        }

        // تشغيل تلقائي عند تحميل الصفحة
        window.addEventListener('load', () => {
            setTimeout(runAllTests, 1000);
        });
    </script>
</body>
</html>
