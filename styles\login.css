/* Login Page Styles */
.login-page {
    margin: 0;
    padding: 0;
    min-height: 100vh;
    background: var(--bg-primary);
    font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
    overflow-x: hidden;
    position: relative;
}

/* Background Animation */
.login-background {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
}

.floating-shapes {
    position: absolute;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

.shape {
    position: absolute;
    border-radius: 50%;
    background: var(--primary-gradient);
    opacity: 0.1;
    animation: float 20s infinite linear;
}

.shape-1 {
    width: 80px;
    height: 80px;
    top: 10%;
    left: 10%;
    animation-delay: 0s;
}

.shape-2 {
    width: 120px;
    height: 120px;
    top: 20%;
    right: 15%;
    animation-delay: 5s;
}

.shape-3 {
    width: 60px;
    height: 60px;
    bottom: 30%;
    left: 20%;
    animation-delay: 10s;
}

.shape-4 {
    width: 100px;
    height: 100px;
    bottom: 20%;
    right: 25%;
    animation-delay: 15s;
}

.shape-5 {
    width: 140px;
    height: 140px;
    top: 50%;
    left: 50%;
    animation-delay: 8s;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
    }
    25% {
        transform: translateY(-20px) rotate(90deg);
    }
    50% {
        transform: translateY(-40px) rotate(180deg);
    }
    75% {
        transform: translateY(-20px) rotate(270deg);
    }
}

/* Login Container */
.login-container {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    padding: 2rem;
    position: relative;
    z-index: 1;
}

/* Logo Section */
.login-header {
    text-align: center;
    margin-bottom: 2rem;
}

.logo-container {
    display: inline-block;
}

.logo-icon {
    width: 100px;
    height: 100px;
    margin: 0 auto 1rem;
    background: var(--primary-gradient);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--elevation-4);
    animation: logoFloat 3s ease-in-out infinite;
    position: relative;
    overflow: hidden;
}

.logo-icon i {
    font-size: 2.5rem;
    color: white;
    z-index: 2;
    position: relative;
}

.logo-pulse {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 100%;
    height: 100%;
    background: rgba(255,255,255,0.3);
    border-radius: 50%;
    transform: translate(-50%, -50%) scale(0);
    animation: pulse 2s infinite;
}

.version-badge {
    background: var(--primary-gradient);
    color: white;
    padding: 0.3rem 0.8rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    margin-top: 0.5rem;
    display: inline-block;
    box-shadow: var(--elevation-2);
}

.logo-text {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0 0 0.5rem 0;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
}

.logo-subtitle {
    font-size: 1.1rem;
    color: var(--text-secondary);
    margin: 0;
}

@keyframes logoFloat {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
    }
    50% {
        transform: translateY(-10px) rotate(5deg);
    }
}

@keyframes pulse {
    0% {
        transform: translate(-50%, -50%) scale(0);
        opacity: 1;
    }
    100% {
        transform: translate(-50%, -50%) scale(1.5);
        opacity: 0;
    }
}

/* Login Form Container */
.login-form-container {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem 0;
}

.login-card {
    width: 100%;
    max-width: 450px;
    background: var(--bg-primary);
    border-radius: var(--radius-2xl);
    padding: 3rem;
    box-shadow: var(--elevation-4);
    position: relative;
    overflow: hidden;
}

.login-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--primary-gradient);
}

.login-card-header {
    text-align: center;
    margin-bottom: 2rem;
    position: relative;
}

.header-icon {
    width: 60px;
    height: 60px;
    background: var(--primary-gradient);
    border-radius: 50%;
    margin: 0 auto 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--elevation-3);
}

.header-icon i {
    font-size: 1.5rem;
    color: white;
}

.login-card-header h2 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0 0 0.5rem 0;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.login-card-header p {
    color: var(--text-secondary);
    margin: 0 0 1rem 0;
    font-size: 1.1rem;
}

.security-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: rgba(40, 167, 69, 0.1);
    color: #28a745;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
    border: 1px solid rgba(40, 167, 69, 0.2);
}

/* Form Styles */
.login-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.form-group {
    position: relative;
}

.input-container {
    position: relative;
    display: flex;
    align-items: center;
}

.input-icon {
    position: absolute;
    right: 1rem;
    z-index: 2;
    color: var(--text-muted);
    font-size: 1.1rem;
}

.form-input {
    width: 100%;
    padding: 1rem 1rem 1rem 3rem;
    border: none;
    border-radius: var(--radius-lg);
    background: var(--bg-secondary);
    color: var(--text-primary);
    font-size: 1rem;
    transition: all var(--transition-normal);
    box-shadow: var(--inset-1);
}

.form-input:focus {
    outline: none;
    box-shadow: var(--inset-2);
    background: var(--bg-primary);
}

.form-input:focus + .floating-label,
.form-input:not(:placeholder-shown) + .floating-label {
    top: -0.5rem;
    right: 0.8rem;
    font-size: 0.8rem;
    color: var(--primary-color);
    background: var(--bg-primary);
    padding: 0 0.5rem;
}

.floating-label {
    position: absolute;
    top: 1rem;
    right: 3rem;
    color: var(--text-muted);
    transition: all var(--transition-normal);
    pointer-events: none;
    background: transparent;
    z-index: 1;
}

.password-toggle {
    position: absolute;
    left: 1rem;
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 50%;
    transition: all var(--transition-fast);
}

.password-toggle:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

.input-error {
    margin-top: 0.5rem;
    color: var(--danger-color);
    font-size: 0.85rem;
    display: none;
    opacity: 0;
    transform: translateY(-10px);
    transition: all 0.3s ease;
}

.input-error.show {
    display: block;
    opacity: 1;
    transform: translateY(0);
    animation: errorSlideIn 0.3s ease-out;
}

@keyframes errorSlideIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

/* Form Options */
.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 1rem 0;
}

.checkbox-container {
    display: flex;
    align-items: center;
    cursor: pointer;
    user-select: none;
}

.checkbox-container input {
    display: none;
}

.checkmark {
    width: 20px;
    height: 20px;
    border-radius: var(--radius-sm);
    background: var(--bg-secondary);
    box-shadow: var(--inset-1);
    margin-left: 0.5rem;
    position: relative;
    transition: all var(--transition-normal);
}

.checkbox-container input:checked + .checkmark {
    background: var(--primary-gradient);
    box-shadow: var(--elevation-1);
}

.checkbox-container input:checked + .checkmark::after {
    content: '\f00c';
    font-family: 'Font Awesome 6 Free';
    font-weight: 900;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 0.8rem;
}

.checkbox-text {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.forgot-password {
    color: var(--primary-color);
    text-decoration: none;
    font-size: 0.9rem;
    transition: all var(--transition-fast);
}

.forgot-password:hover {
    color: var(--secondary-color);
    text-decoration: underline;
}

/* Login Button */
.login-btn {
    width: 100%;
    padding: 1.2rem;
    border: none;
    border-radius: var(--radius-lg);
    background: var(--primary-gradient);
    color: white;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all var(--transition-normal);
    box-shadow: var(--elevation-3);
    position: relative;
    overflow: hidden;
}

.login-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.login-btn:hover::before {
    left: 100%;
}

.login-btn:hover {
    transform: translateY(-3px);
    box-shadow: var(--elevation-4);
}

.login-btn:active {
    transform: translateY(-1px);
    box-shadow: var(--elevation-2);
}

.login-btn:disabled {
    opacity: 0.8;
    cursor: not-allowed;
    transform: none;
}

.btn-content {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.btn-icon {
    transition: transform 0.3s ease;
}

.login-btn:hover .btn-icon {
    transform: translateX(-5px);
}

.btn-loader {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-success {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: white;
}

.spinner {
    width: 20px;
    height: 20px;
    border: 2px solid rgba(255,255,255,0.3);
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Error Message */
.login-error {
    background: var(--danger-color);
    color: white;
    padding: 1rem;
    border-radius: var(--radius-md);
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-top: 1rem;
    animation: shake 0.5s ease-in-out;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

/* Demo Accounts */
.demo-accounts {
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid var(--border-color);
}

.demo-header {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
    justify-content: center;
}

.demo-header i {
    color: var(--primary-color);
    font-size: 1.1rem;
}

.demo-header h4 {
    color: var(--text-primary);
    margin: 0;
    font-size: 1rem;
    font-weight: 600;
}

.demo-account-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.demo-account {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: var(--bg-secondary);
    border-radius: var(--radius-lg);
    cursor: pointer;
    transition: all var(--transition-normal);
    box-shadow: var(--elevation-1);
    border: 2px solid transparent;
    position: relative;
    overflow: hidden;
}

.demo-account::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.1), transparent);
    transition: left 0.5s;
}

.demo-account:hover::before {
    left: 100%;
}

.demo-account:hover {
    background: var(--bg-primary);
    transform: translateY(-2px);
    box-shadow: var(--elevation-3);
    border-color: var(--primary-color);
}

.account-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
    flex-shrink: 0;
}

.demo-account.admin .account-icon {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
}

.demo-account.manager .account-icon {
    background: linear-gradient(135deg, #3498db, #2980b9);
}

.demo-account.accountant .account-icon {
    background: linear-gradient(135deg, #f39c12, #e67e22);
}

.account-info {
    flex: 1;
    text-align: right;
}

.account-title {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 1rem;
    margin-bottom: 0.3rem;
}

.account-credentials {
    color: var(--text-secondary);
    font-size: 0.9rem;
    font-family: 'Courier New', monospace;
    background: rgba(102, 126, 234, 0.1);
    padding: 0.2rem 0.5rem;
    border-radius: 4px;
    display: inline-block;
    margin-bottom: 0.3rem;
}

.account-permissions {
    color: var(--text-muted);
    font-size: 0.8rem;
}

.account-action {
    color: var(--primary-color);
    font-size: 1.2rem;
    opacity: 0.7;
    transition: all var(--transition-fast);
}

.demo-account:hover .account-action {
    opacity: 1;
    transform: scale(1.1);
}

/* Debug Tools */
.debug-tools {
    margin-top: 1.5rem;
    padding-top: 1.5rem;
    border-top: 1px solid var(--border-color);
    display: flex;
    gap: 0.5rem;
    justify-content: center;
    flex-wrap: wrap;
}

.debug-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.6rem 1rem;
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    color: var(--text-secondary);
    font-size: 0.8rem;
    cursor: pointer;
    transition: all var(--transition-fast);
}

.debug-btn:hover {
    background: var(--bg-primary);
    color: var(--text-primary);
    border-color: var(--primary-color);
    transform: translateY(-1px);
}

.debug-btn i {
    font-size: 0.9rem;
}

/* Footer */
.login-footer {
    text-align: center;
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid var(--border-color);
}

.login-footer p {
    color: var(--text-muted);
    margin: 0 0 1rem 0;
    font-size: 0.9rem;
}

.footer-links {
    display: flex;
    justify-content: center;
    gap: 2rem;
    flex-wrap: wrap;
}

.footer-links a {
    color: var(--text-secondary);
    text-decoration: none;
    font-size: 0.9rem;
    transition: color var(--transition-fast);
}

.footer-links a:hover {
    color: var(--primary-color);
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.loading-content {
    background: var(--bg-primary);
    padding: 2rem;
    border-radius: var(--radius-lg);
    text-align: center;
    box-shadow: var(--elevation-4);
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--border-color);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .login-container {
        padding: 1rem;
    }

    .login-card {
        padding: 2rem;
        margin: 1rem 0;
        max-width: 100%;
    }

    .logo-text {
        font-size: 2rem;
    }

    .logo-icon {
        width: 80px;
        height: 80px;
    }

    .logo-icon i {
        font-size: 2rem;
    }

    .demo-account {
        flex-direction: column;
        text-align: center;
        gap: 0.5rem;
    }

    .account-info {
        text-align: center;
    }

    .debug-tools {
        flex-direction: column;
    }

    .footer-links {
        gap: 1rem;
    }
}

@media (max-width: 480px) {
    .login-card {
        padding: 1.5rem;
        margin: 0.5rem;
    }

    .logo-text {
        font-size: 1.8rem;
    }

    .logo-icon {
        width: 70px;
        height: 70px;
    }

    .logo-icon i {
        font-size: 1.8rem;
    }

    .login-card-header h2 {
        font-size: 1.6rem;
    }

    .form-input {
        padding: 0.9rem 0.9rem 0.9rem 2.5rem;
    }

    .floating-label {
        right: 2.5rem;
    }

    .demo-account {
        padding: 0.8rem;
    }

    .account-icon {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }

    .footer-links {
        flex-direction: column;
        gap: 0.5rem;
    }

    .floating-shapes .shape {
        display: none;
    }
}
