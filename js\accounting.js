// Enhanced Accounting Management System
class AccountingManager {
    constructor() {
        this.accountingData = {
            totalReceived: 0,
            totalCommissions: 0,
            netProfit: 0,
            companyBalances: {},
            driverBalances: {},
            monthlyData: {},
            transactions: [],
            expenses: [],
            payments: []
        };
        this.currentView = 'overview';
        this.init();
    }

    async init() {
        this.setupEventListeners();
        await this.loadAccountingData();
    }

    setupEventListeners() {
        // Navigation between accounting views
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('accounting-nav-btn')) {
                const view = e.target.dataset.view;
                this.switchView(view);
            }

            if (e.target.classList.contains('add-payment-btn')) {
                this.showAddPaymentModal();
            }

            if (e.target.classList.contains('add-expense-btn')) {
                this.showAddExpenseModal();
            }

            if (e.target.classList.contains('calculate-commissions-btn')) {
                this.calculateCommissions();
            }

            if (e.target.classList.contains('export-accounting-btn')) {
                this.exportAccountingData();
            }
        });

        // Listen for order updates to refresh accounting
        document.addEventListener('ordersUpdated', () => {
            this.loadAccountingData();
        });
    }

    async loadAccountingData() {
        try {
            const orders = await storage.getAll('orders');
            const drivers = await storage.getAll('drivers');
            
            await this.calculateAccountingData(orders, drivers);
            this.renderAccountingData();
        } catch (error) {
            console.error('Error loading accounting data:', error);
            window.app.showNotification('خطأ في تحميل البيانات المحاسبية', 'error');
        }
    }

    async calculateAccountingData(orders, drivers) {
        // Reset data
        this.accountingData = {
            totalReceived: 0,
            totalCommissions: 0,
            netProfit: 0,
            companyBalances: {},
            driverBalances: {},
            monthlyData: {},
            transactions: [],
            expenses: await this.loadExpenses(),
            payments: await this.loadPayments()
        };

        // Calculate totals from delivered orders
        const deliveredOrders = orders.filter(order => order.status === 'delivered');
        
        // Total received amount
        this.accountingData.totalReceived = deliveredOrders.reduce((sum, order) =>
            sum + (order.deliveryPrice || 0), 0);

        // Calculate company balances
        await this.calculateCompanyBalances(orders);

        // Calculate driver balances and commissions
        await this.calculateDriverBalances(orders, drivers);

        // Calculate monthly data
        this.calculateMonthlyData(orders);

        // Generate transactions log
        this.generateTransactionsLog(orders, drivers);

        // Calculate net profit
        const totalExpenses = this.accountingData.expenses.reduce((sum, expense) => sum + expense.amount, 0);
        this.accountingData.netProfit = this.accountingData.totalReceived - this.accountingData.totalCommissions - totalExpenses;

        // Calculate company balances
        orders.forEach(order => {
            if (!this.accountingData.companyBalances[order.clientName]) {
                this.accountingData.companyBalances[order.clientName] = {
                    totalOrders: 0,
                    deliveredOrders: 0,
                    totalAmount: 0,
                    receivedAmount: 0,
                    pendingAmount: 0,
                    returnedOrders: 0
                };
            }

            const company = this.accountingData.companyBalances[order.clientName];
            company.totalOrders++;
            company.totalAmount += order.deliveryPrice;

            switch (order.status) {
                case 'delivered':
                    company.deliveredOrders++;
                    company.receivedAmount += order.deliveryPrice;
                    break;
                case 'pending':
                    company.pendingAmount += order.deliveryPrice;
                    break;
                case 'returned':
                case 'partial_return':
                    company.returnedOrders++;
                    break;
            }
        });

        // Calculate driver balances and commissions
        drivers.forEach(driver => {
            const driverOrders = orders.filter(order => order.driverId == driver.id);
            const driverDeliveredOrders = driverOrders.filter(order => order.status === 'delivered');
            
            const totalCommission = driverDeliveredOrders.length * (driver.commissionAmount || driver.fixedCommission || 1000);

            this.accountingData.driverBalances[driver.id] = {
                driverName: driver.name,
                totalOrders: driverOrders.length,
                deliveredOrders: driverDeliveredOrders.length,
                totalCommission: totalCommission,
                fixedCommission: driver.commissionAmount || driver.fixedCommission || 1000,
                successRate: driverOrders.length > 0 ? 
                    Math.round((driverDeliveredOrders.length / driverOrders.length) * 100) : 0
            };

            this.accountingData.totalCommissions += totalCommission;
        });

        // Calculate net profit
        this.accountingData.netProfit = this.accountingData.totalReceived - this.accountingData.totalCommissions;
    }

    renderAccountingData() {
        this.updateSummaryCards();
        this.renderCompanyBalances();
        this.renderDriverBalances();
    }

    updateSummaryCards() {
        const totalReceivedEl = document.getElementById('totalReceived');
        const totalCommissionsEl = document.getElementById('totalCommissions');
        const netProfitEl = document.getElementById('netProfit');

        if (totalReceivedEl) {
            totalReceivedEl.textContent = `${this.accountingData.totalReceived.toLocaleString()} د.ع`;
        }
        if (totalCommissionsEl) {
            totalCommissionsEl.textContent = `${Math.round(this.accountingData.totalCommissions).toLocaleString()} د.ع`;
        }
        if (netProfitEl) {
            netProfitEl.textContent = `${Math.round(this.accountingData.netProfit).toLocaleString()} د.ع`;
        }
    }

    renderCompanyBalances() {
        const container = document.getElementById('companyBalances');
        if (!container) return;

        const companies = Object.entries(this.accountingData.companyBalances)
            .sort((a, b) => b[1].receivedAmount - a[1].receivedAmount);

        if (companies.length === 0) {
            container.innerHTML = '<p class="text-center">لا توجد بيانات شركات</p>';
            return;
        }

        container.innerHTML = `
            <div class="balances-header">
                <h4>أرصدة الشركات</h4>
                <button class="btn-secondary" onclick="accountingManager.exportCompanyBalances()">
                    <i class="fas fa-download"></i>
                    تصدير
                </button>
            </div>
            <div class="table-container">
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>اسم الشركة</th>
                            <th>إجمالي الطلبات</th>
                            <th>طلبات مسلمة</th>
                            <th>طلبات راجعة</th>
                            <th>المبلغ المستلم</th>
                            <th>المبلغ المعلق</th>
                            <th>إجمالي المبلغ</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${companies.map(([companyName, data]) => `
                            <tr>
                                <td><strong>${companyName}</strong></td>
                                <td>${data.totalOrders}</td>
                                <td><span class="status-badge status-delivered">${data.deliveredOrders}</span></td>
                                <td><span class="status-badge status-returned">${data.returnedOrders}</span></td>
                                <td><strong class="text-success">${data.receivedAmount.toLocaleString()} د.ع</strong></td>
                                <td><strong class="text-warning">${data.pendingAmount.toLocaleString()} د.ع</strong></td>
                                <td><strong>${data.totalAmount.toLocaleString()} د.ع</strong></td>
                                <td>
                                    <button class="btn-icon" onclick="accountingManager.viewCompanyDetails('${companyName}')" title="التفاصيل">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn-icon" onclick="accountingManager.addPayment('${companyName}')" title="إضافة دفعة">
                                        <i class="fas fa-plus"></i>
                                    </button>
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>
        `;
    }

    renderDriverBalances() {
        // This will be rendered in a separate section or modal
        // For now, we'll add it to the company balances section
        const container = document.getElementById('companyBalances');
        if (!container) return;

        const drivers = Object.entries(this.accountingData.driverBalances)
            .sort((a, b) => b[1].totalCommission - a[1].totalCommission);

        if (drivers.length === 0) return;

        const driverBalancesHTML = `
            <div class="balances-section" style="margin-top: 2rem;">
                <div class="balances-header">
                    <h4>أرصدة المندوبين</h4>
                    <button class="btn-secondary" onclick="accountingManager.exportDriverBalances()">
                        <i class="fas fa-download"></i>
                        تصدير
                    </button>
                </div>
                <div class="table-container">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>اسم المندوب</th>
                                <th>إجمالي الطلبات</th>
                                <th>طلبات مسلمة</th>
                                <th>معدل النجاح</th>
                                <th>العمولة الثابتة</th>
                                <th>العمولة المستحقة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${drivers.map(([driverId, data]) => `
                                <tr>
                                    <td><strong>${data.driverName}</strong></td>
                                    <td>${data.totalOrders}</td>
                                    <td><span class="status-badge status-delivered">${data.deliveredOrders}</span></td>
                                    <td><strong class="${data.successRate >= 80 ? 'text-success' : data.successRate >= 60 ? 'text-warning' : 'text-danger'}">${data.successRate}%</strong></td>
                                    <td>${data.fixedCommission || 1000} د.ع</td>
                                    <td><strong class="text-primary">${Math.round(data.totalCommission).toLocaleString()} د.ع</strong></td>
                                    <td>
                                        <button class="btn-icon" onclick="accountingManager.viewDriverDetails(${driverId})" title="التفاصيل">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn-icon" onclick="accountingManager.payCommission(${driverId})" title="دفع العمولة">
                                            <i class="fas fa-money-bill"></i>
                                        </button>
                                    </td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
            </div>
        `;

        container.insertAdjacentHTML('beforeend', driverBalancesHTML);
    }

    async viewCompanyDetails(companyName) {
        try {
            const orders = await storage.getAll('orders');
            const companyOrders = orders.filter(order => order.clientName === companyName);
            const companyData = this.accountingData.companyBalances[companyName];

            const modalContent = `
                <div class="modal" style="max-width: 900px;">
                    <div class="modal-header">
                        <h3 class="modal-title">تفاصيل الشركة: ${companyName}</h3>
                        <button class="modal-close">&times;</button>
                    </div>
                    <div class="modal-body">
                        <div class="company-summary">
                            <div class="summary-stats">
                                <div class="summary-item">
                                    <span>إجمالي الطلبات:</span>
                                    <strong>${companyData.totalOrders}</strong>
                                </div>
                                <div class="summary-item">
                                    <span>طلبات مسلمة:</span>
                                    <strong>${companyData.deliveredOrders}</strong>
                                </div>
                                <div class="summary-item">
                                    <span>طلبات راجعة:</span>
                                    <strong>${companyData.returnedOrders}</strong>
                                </div>
                                <div class="summary-item">
                                    <span>المبلغ المستلم:</span>
                                    <strong>${companyData.receivedAmount.toLocaleString()} د.ع</strong>
                                </div>
                                <div class="summary-item">
                                    <span>المبلغ المعلق:</span>
                                    <strong>${companyData.pendingAmount.toLocaleString()} د.ع</strong>
                                </div>
                            </div>
                        </div>

                        <h4>طلبات الشركة:</h4>
                        <div class="table-container">
                            <table class="data-table">
                                <thead>
                                    <tr>
                                        <th>رقم الطلب</th>
                                        <th>اسم الزبون</th>
                                        <th>المحافظة</th>
                                        <th>السعر</th>
                                        <th>الحالة</th>
                                        <th>التاريخ</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${companyOrders.map(order => `
                                        <tr>
                                            <td>#${order.id}</td>
                                            <td>${order.recipientName || order.customerName || 'غير محدد'}</td>
                                            <td>${order.city}</td>
                                            <td>${order.deliveryPrice} د.ع</td>
                                            <td>
                                                <span class="status-badge status-${order.status}">
                                                    ${this.getStatusText(order.status)}
                                                </span>
                                            </td>
                                            <td>${this.formatDate(order.date)}</td>
                                        </tr>
                                    `).join('')}
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn-secondary modal-close">إغلاق</button>
                        <button type="button" class="btn-primary" onclick="accountingManager.exportCompanyReport('${companyName}')">
                            <i class="fas fa-download"></i>
                            تصدير تقرير الشركة
                        </button>
                    </div>
                </div>
            `;

            window.app.showModal(modalContent);
        } catch (error) {
            console.error('Error loading company details:', error);
            window.app.showNotification('خطأ في تحميل تفاصيل الشركة', 'error');
        }
    }

    async viewDriverDetails(driverId) {
        try {
            const driver = await storage.getById('drivers', driverId);
            const orders = await storage.getAll('orders');
            const driverOrders = orders.filter(order => order.driverId == driverId);
            const driverData = this.accountingData.driverBalances[driverId];

            const modalContent = `
                <div class="modal" style="max-width: 900px;">
                    <div class="modal-header">
                        <h3 class="modal-title">تفاصيل المندوب: ${driverData.driverName}</h3>
                        <button class="modal-close">&times;</button>
                    </div>
                    <div class="modal-body">
                        <div class="driver-summary">
                            <div class="summary-stats">
                                <div class="summary-item">
                                    <span>إجمالي الطلبات:</span>
                                    <strong>${driverData.totalOrders}</strong>
                                </div>
                                <div class="summary-item">
                                    <span>طلبات مسلمة:</span>
                                    <strong>${driverData.deliveredOrders}</strong>
                                </div>
                                <div class="summary-item">
                                    <span>معدل النجاح:</span>
                                    <strong>${driverData.successRate}%</strong>
                                </div>
                                <div class="summary-item">
                                    <span>العمولة الثابتة:</span>
                                    <strong>${driverData.fixedCommission || 1000} د.ع</strong>
                                </div>
                                <div class="summary-item">
                                    <span>العمولة المستحقة:</span>
                                    <strong>${Math.round(driverData.totalCommission).toLocaleString()} د.ع</strong>
                                </div>
                            </div>
                        </div>

                        <h4>طلبات المندوب:</h4>
                        <div class="table-container">
                            <table class="data-table">
                                <thead>
                                    <tr>
                                        <th>رقم الطلب</th>
                                        <th>العميل</th>
                                        <th>الزبون</th>
                                        <th>المحافظة</th>
                                        <th>السعر</th>
                                        <th>العمولة</th>
                                        <th>الحالة</th>
                                        <th>التاريخ</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${driverOrders.map(order => {
                                        const commission = order.status === 'delivered' ?
                                            (driverData.fixedCommission || 1000) : 0;
                                        return `
                                            <tr>
                                                <td>#${order.id}</td>
                                                <td>${order.clientName}</td>
                                                <td>${order.recipientName || order.customerName || 'غير محدد'}</td>
                                                <td>${order.city}</td>
                                                <td>${order.deliveryPrice} د.ع</td>
                                                <td>${Math.round(commission)} د.ع</td>
                                                <td>
                                                    <span class="status-badge status-${order.status}">
                                                        ${this.getStatusText(order.status)}
                                                    </span>
                                                </td>
                                                <td>${this.formatDate(order.date)}</td>
                                            </tr>
                                        `;
                                    }).join('')}
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn-secondary modal-close">إغلاق</button>
                        <button type="button" class="btn-primary" onclick="accountingManager.exportDriverReport(${driverId})">
                            <i class="fas fa-download"></i>
                            تصدير تقرير المندوب
                        </button>
                    </div>
                </div>
            `;

            window.app.showModal(modalContent);
        } catch (error) {
            console.error('Error loading driver details:', error);
            window.app.showNotification('خطأ في تحميل تفاصيل المندوب', 'error');
        }
    }

    addPayment(companyName) {
        const modalContent = `
            <div class="modal">
                <div class="modal-header">
                    <h3 class="modal-title">إضافة دفعة للشركة: ${companyName}</h3>
                    <button class="modal-close">&times;</button>
                </div>
                <div class="modal-body">
                    <form id="paymentForm">
                        <div class="form-group">
                            <label class="form-label">مبلغ الدفعة (د.ع) *</label>
                            <input type="number" class="form-input" name="amount" min="0" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">تاريخ الدفعة *</label>
                            <input type="date" class="form-input" name="paymentDate" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">طريقة الدفع</label>
                            <select class="form-select" name="paymentMethod">
                                <option value="cash">نقداً</option>
                                <option value="bank_transfer">تحويل بنكي</option>
                                <option value="check">شيك</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">ملاحظات</label>
                            <textarea class="form-textarea" name="notes"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn-secondary modal-close">إلغاء</button>
                    <button type="button" class="btn-primary" onclick="accountingManager.savePayment('${companyName}')">حفظ الدفعة</button>
                </div>
            </div>
        `;

        window.app.showModal(modalContent);
        
        // Set today's date as default
        setTimeout(() => {
            const dateInput = document.querySelector('input[name="paymentDate"]');
            if (dateInput) {
                dateInput.value = new Date().toISOString().split('T')[0];
            }
        }, 100);
    }

    async savePayment(companyName) {
        const form = document.getElementById('paymentForm');
        const formData = new FormData(form);
        
        if (!form.checkValidity()) {
            form.reportValidity();
            return;
        }

        const paymentData = {
            companyName: companyName,
            amount: parseFloat(formData.get('amount')),
            paymentDate: formData.get('paymentDate'),
            paymentMethod: formData.get('paymentMethod'),
            notes: formData.get('notes') || '',
            createdAt: new Date().toISOString()
        };

        try {
            // Save payment record (you might want to create a payments store)
            await storage.add('payments', paymentData);
            window.app.showNotification('تم حفظ الدفعة بنجاح', 'success');
            window.app.closeModal();
            await this.loadAccountingData();
        } catch (error) {
            console.error('Error saving payment:', error);
            window.app.showNotification('خطأ في حفظ الدفعة', 'error');
        }
    }

    exportCompanyBalances() {
        const companies = Object.entries(this.accountingData.companyBalances);
        let csvContent = 'اسم الشركة,إجمالي الطلبات,طلبات مسلمة,طلبات راجعة,المبلغ المستلم,المبلغ المعلق,إجمالي المبلغ\n';
        
        companies.forEach(([companyName, data]) => {
            csvContent += `"${companyName}",${data.totalOrders},${data.deliveredOrders},${data.returnedOrders},${data.receivedAmount},${data.pendingAmount},${data.totalAmount}\n`;
        });

        this.downloadCSV(csvContent, 'أرصدة-الشركات');
    }

    exportDriverBalances() {
        const drivers = Object.entries(this.accountingData.driverBalances);
        let csvContent = 'اسم المندوب,إجمالي الطلبات,طلبات مسلمة,معدل النجاح,العمولة الثابتة,العمولة المستحقة\n';
        
        drivers.forEach(([driverId, data]) => {
            csvContent += `"${data.driverName}",${data.totalOrders},${data.deliveredOrders},${data.successRate}%,${data.fixedCommission || 1000},${Math.round(data.totalCommission)}\n`;
        });

        this.downloadCSV(csvContent, 'أرصدة-المندوبين');
    }

    downloadCSV(content, filename) {
        const blob = new Blob([content], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', `${filename}-${new Date().toISOString().split('T')[0]}.csv`);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }

    getStatusText(status) {
        const statusMap = {
            'delivered': 'مسلم',
            'pending': 'معلق',
            'returned': 'راجع',
            'partial_return': 'راجع جزئي'
        };
        return statusMap[status] || status;
    }

    formatDate(dateString) {
        return new Date(dateString).toLocaleDateString('ar-IQ');
    }

    // Enhanced accounting methods
    async loadExpenses() {
        try {
            return await storage.getAll('expenses') || [];
        } catch (error) {
            console.error('Error loading expenses:', error);
            return [];
        }
    }

    async loadPayments() {
        try {
            return await storage.getAll('payments') || [];
        } catch (error) {
            console.error('Error loading payments:', error);
            return [];
        }
    }

    async calculateCompanyBalances(orders) {
        const companies = {};

        orders.forEach(order => {
            const companyName = order.clientName;
            if (!companies[companyName]) {
                companies[companyName] = {
                    totalOrders: 0,
                    deliveredOrders: 0,
                    returnedOrders: 0,
                    pendingOrders: 0,
                    totalAmount: 0,
                    receivedAmount: 0,
                    pendingAmount: 0,
                    lastOrderDate: null
                };
            }

            const company = companies[companyName];
            company.totalOrders++;
            company.totalAmount += order.deliveryPrice || 0;

            if (order.status === 'delivered') {
                company.deliveredOrders++;
                company.receivedAmount += order.deliveryPrice || 0;
            } else if (order.status === 'returned') {
                company.returnedOrders++;
            } else if (order.status === 'pending') {
                company.pendingOrders++;
                company.pendingAmount += order.deliveryPrice || 0;
            }

            const orderDate = new Date(order.createdAt || order.date);
            if (!company.lastOrderDate || orderDate > company.lastOrderDate) {
                company.lastOrderDate = orderDate;
            }
        });

        this.accountingData.companyBalances = companies;
    }

    async calculateDriverBalances(orders, drivers) {
        const driverBalances = {};

        drivers.forEach(driver => {
            driverBalances[driver.id] = {
                name: driver.name,
                phone: driver.phone,
                totalOrders: 0,
                deliveredOrders: 0,
                returnedOrders: 0,
                totalCommission: 0,
                paidCommission: 0,
                pendingCommission: 0,
                commissionRate: driver.commissionAmount || 0,
                lastDeliveryDate: null
            };
        });

        orders.forEach(order => {
            if (order.driverId && driverBalances[order.driverId]) {
                const balance = driverBalances[order.driverId];
                balance.totalOrders++;

                if (order.status === 'delivered') {
                    balance.deliveredOrders++;
                    balance.totalCommission += balance.commissionRate;
                    balance.pendingCommission += balance.commissionRate;

                    const deliveryDate = new Date(order.createdAt || order.date);
                    if (!balance.lastDeliveryDate || deliveryDate > balance.lastDeliveryDate) {
                        balance.lastDeliveryDate = deliveryDate;
                    }
                } else if (order.status === 'returned') {
                    balance.returnedOrders++;
                }
            }
        });

        this.accountingData.driverBalances = driverBalances;
        this.accountingData.totalCommissions = Object.values(driverBalances)
            .reduce((sum, balance) => sum + balance.totalCommission, 0);
    }

    calculateMonthlyData(orders) {
        const monthlyData = {};

        orders.forEach(order => {
            const date = new Date(order.createdAt || order.date);
            const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;

            if (!monthlyData[monthKey]) {
                monthlyData[monthKey] = {
                    totalOrders: 0,
                    deliveredOrders: 0,
                    returnedOrders: 0,
                    revenue: 0,
                    commissions: 0
                };
            }

            const month = monthlyData[monthKey];
            month.totalOrders++;

            if (order.status === 'delivered') {
                month.deliveredOrders++;
                month.revenue += order.deliveryPrice || 0;

                // Calculate commission for this order
                if (order.driverId && this.accountingData.driverBalances[order.driverId]) {
                    month.commissions += this.accountingData.driverBalances[order.driverId].commissionRate;
                }
            } else if (order.status === 'returned') {
                month.returnedOrders++;
            }
        });

        this.accountingData.monthlyData = monthlyData;
    }

    generateTransactionsLog(orders, drivers) {
        const transactions = [];

        // Add order transactions
        orders.forEach(order => {
            if (order.status === 'delivered') {
                transactions.push({
                    id: `order-${order.id}`,
                    type: 'revenue',
                    description: `إيراد من الطلب #${order.id} - ${order.clientName}`,
                    amount: order.deliveryPrice || 0,
                    date: order.createdAt || order.date,
                    reference: order.trackingNumber
                });

                // Add commission transaction
                if (order.driverId) {
                    const driver = drivers.find(d => d.id === order.driverId);
                    if (driver) {
                        transactions.push({
                            id: `commission-${order.id}`,
                            type: 'commission',
                            description: `عمولة المندوب ${driver.name} - الطلب #${order.id}`,
                            amount: -(driver.commissionAmount || 0),
                            date: order.createdAt || order.date,
                            reference: order.trackingNumber
                        });
                    }
                }
            }
        });

        // Sort by date (newest first)
        transactions.sort((a, b) => new Date(b.date) - new Date(a.date));

        this.accountingData.transactions = transactions;
    }

    switchView(view) {
        this.currentView = view;
        this.renderAccountingData();

        // Update navigation
        document.querySelectorAll('.accounting-nav-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-view="${view}"]`)?.classList.add('active');
    }

    exportAccountingData() {
        const data = {
            summary: {
                totalReceived: this.accountingData.totalReceived,
                totalCommissions: this.accountingData.totalCommissions,
                netProfit: this.accountingData.netProfit,
                exportDate: new Date().toISOString()
            },
            companyBalances: this.accountingData.companyBalances,
            driverBalances: this.accountingData.driverBalances,
            monthlyData: this.accountingData.monthlyData,
            transactions: this.accountingData.transactions
        };

        const jsonContent = JSON.stringify(data, null, 2);
        const blob = new Blob([jsonContent], { type: 'application/json' });
        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = `accounting_data_${new Date().toISOString().split('T')[0]}.json`;
        link.click();

        window.app.showNotification('تم تصدير البيانات المحاسبية', 'success');
    }

    formatCurrency(amount) {
        return new Intl.NumberFormat('ar-IQ', {
            style: 'currency',
            currency: 'IQD',
            minimumFractionDigits: 0
        }).format(amount || 0);
    }

    // Missing methods that are referenced but not implemented
    showAddPaymentModal() {
        const modalContent = `
            <div class="modal">
                <div class="modal-header">
                    <h3 class="modal-title">إضافة دفعة</h3>
                    <button class="modal-close">&times;</button>
                </div>
                <div class="modal-body">
                    <form id="addPaymentForm">
                        <div class="form-group">
                            <label class="form-label">نوع الدفعة</label>
                            <select class="form-select" name="type" required>
                                <option value="">اختر النوع</option>
                                <option value="received">دفعة مستلمة</option>
                                <option value="paid">دفعة مدفوعة</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">المبلغ (د.ع)</label>
                            <input type="number" class="form-input" name="amount" required min="0">
                        </div>
                        <div class="form-group">
                            <label class="form-label">الوصف</label>
                            <input type="text" class="form-input" name="description" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">المرجع</label>
                            <input type="text" class="form-input" name="reference">
                        </div>
                        <div class="form-group">
                            <label class="form-label">التاريخ</label>
                            <input type="date" class="form-input" name="date" required value="${new Date().toISOString().split('T')[0]}">
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn-secondary modal-close">إلغاء</button>
                    <button type="button" class="btn-primary" onclick="accountingManager.savePaymentFromModal()">حفظ</button>
                </div>
            </div>
        `;

        window.app.showModal(modalContent);
    }

    showAddExpenseModal() {
        const modalContent = `
            <div class="modal">
                <div class="modal-header">
                    <h3 class="modal-title">إضافة مصروف</h3>
                    <button class="modal-close">&times;</button>
                </div>
                <div class="modal-body">
                    <form id="addExpenseForm">
                        <div class="form-group">
                            <label class="form-label">نوع المصروف</label>
                            <select class="form-select" name="category" required>
                                <option value="">اختر النوع</option>
                                <option value="fuel">وقود</option>
                                <option value="maintenance">صيانة</option>
                                <option value="office">مصاريف مكتبية</option>
                                <option value="marketing">تسويق</option>
                                <option value="other">أخرى</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">المبلغ (د.ع)</label>
                            <input type="number" class="form-input" name="amount" required min="0">
                        </div>
                        <div class="form-group">
                            <label class="form-label">الوصف</label>
                            <input type="text" class="form-input" name="description" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">المرجع</label>
                            <input type="text" class="form-input" name="reference">
                        </div>
                        <div class="form-group">
                            <label class="form-label">التاريخ</label>
                            <input type="date" class="form-input" name="date" required value="${new Date().toISOString().split('T')[0]}">
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn-secondary modal-close">إلغاء</button>
                    <button type="button" class="btn-primary" onclick="accountingManager.saveExpenseFromModal()">حفظ</button>
                </div>
            </div>
        `;

        window.app.showModal(modalContent);
    }

    async savePaymentFromModal() {
        const form = document.getElementById('addPaymentForm');
        if (!form || !form.checkValidity()) {
            form?.reportValidity();
            return;
        }

        const formData = new FormData(form);
        const payment = {
            id: Date.now(),
            type: formData.get('type'),
            amount: parseFloat(formData.get('amount')),
            description: formData.get('description'),
            reference: formData.get('reference'),
            date: formData.get('date'),
            createdAt: new Date().toISOString()
        };

        try {
            await storage.add('payments', payment);
            window.app.closeModal();
            await this.loadAccountingData();
            window.app.showNotification('تم حفظ الدفعة بنجاح', 'success');
        } catch (error) {
            console.error('Error saving payment:', error);
            window.app.showNotification('خطأ في حفظ الدفعة', 'error');
        }
    }

    async saveExpenseFromModal() {
        const form = document.getElementById('addExpenseForm');
        if (!form || !form.checkValidity()) {
            form?.reportValidity();
            return;
        }

        const formData = new FormData(form);
        const expense = {
            id: Date.now(),
            category: formData.get('category'),
            amount: parseFloat(formData.get('amount')),
            description: formData.get('description'),
            reference: formData.get('reference'),
            date: formData.get('date'),
            createdAt: new Date().toISOString()
        };

        try {
            await storage.add('expenses', expense);
            window.app.closeModal();
            await this.loadAccountingData();
            window.app.showNotification('تم حفظ المصروف بنجاح', 'success');
        } catch (error) {
            console.error('Error saving expense:', error);
            window.app.showNotification('خطأ في حفظ المصروف', 'error');
        }
    }

    async calculateCommissions() {
        try {
            // This would typically update commission payments in the database
            window.app.showNotification('تم حساب العمولات بنجاح', 'success');
            await this.loadAccountingData();
        } catch (error) {
            console.error('Error calculating commissions:', error);
            window.app.showNotification('خطأ في حساب العمولات', 'error');
        }
    }
}

// Initialize accounting manager
document.addEventListener('DOMContentLoaded', () => {
    window.accountingManager = new AccountingManager();
});
