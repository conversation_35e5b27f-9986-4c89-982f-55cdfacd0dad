<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار التنقل - نظام إدارة التوصيل</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f0f0f3;
            direction: rtl;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
        }
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .btn:hover {
            opacity: 0.9;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 5px;
            background: #e9ecef;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        .warning { background: #fff3cd; color: #856404; }
        
        .nav-test {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin: 10px 0;
        }
        
        .section-test {
            border: 2px solid #ddd;
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
        }
        
        .section-test.active {
            border-color: #28a745;
            background: #f8fff9;
        }
        
        .log-area {
            background: #000;
            color: #0f0;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            height: 300px;
            overflow-y: auto;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 اختبار التنقل - نظام إدارة التوصيل</h1>
        
        <div class="test-section">
            <h3>1. اختبار وجود العناصر</h3>
            <button class="btn" onclick="testElements()">فحص العناصر</button>
            <div id="elementsResult" class="result"></div>
        </div>
        
        <div class="test-section">
            <h3>2. اختبار التنقل المباشر</h3>
            <div class="nav-test">
                <button class="btn" onclick="testNavigation('dashboard')">لوحة التحكم</button>
                <button class="btn" onclick="testNavigation('orders')">الطلبات</button>
                <button class="btn" onclick="testNavigation('drivers')">المندوبين</button>
                <button class="btn" onclick="testNavigation('customers')">العملاء</button>
                <button class="btn" onclick="testNavigation('assignment')">الإسناد</button>
                <button class="btn" onclick="testNavigation('reports')">التقارير</button>
                <button class="btn" onclick="testNavigation('accounting')">المحاسبة</button>
                <button class="btn" onclick="testNavigation('settings')">الإعدادات</button>
            </div>
            <div id="navigationResult" class="result"></div>
        </div>
        
        <div class="test-section">
            <h3>3. محاكاة النقر على التنقل</h3>
            <button class="btn" onclick="simulateClicks()">محاكاة النقرات</button>
            <div id="clickResult" class="result"></div>
        </div>
        
        <div class="test-section">
            <h3>4. اختبار CSS</h3>
            <button class="btn" onclick="testCSS()">فحص CSS</button>
            <div id="cssResult" class="result"></div>
        </div>
        
        <div class="test-section">
            <h3>5. سجل الأحداث المباشر</h3>
            <button class="btn" onclick="clearLog()">مسح السجل</button>
            <button class="btn" onclick="startLogging()">بدء التسجيل</button>
            <div id="logArea" class="log-area"></div>
        </div>
        
        <div class="test-section">
            <h3>6. الذهاب للنظام الرئيسي</h3>
            <button class="btn" onclick="window.location.href='index.html'">النظام الرئيسي</button>
            <button class="btn" onclick="window.location.href='index.html?debug=1'">النظام مع التشخيص</button>
        </div>
    </div>

    <script>
        let logArea;
        let isLogging = false;
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logMessage = `[${timestamp}] ${message}`;
            console.log(logMessage);
            
            if (logArea && isLogging) {
                const color = {
                    'info': '#0f0',
                    'error': '#f00',
                    'warning': '#ff0',
                    'success': '#0f0'
                }[type] || '#0f0';
                
                logArea.innerHTML += `<div style="color: ${color}">${logMessage}</div>`;
                logArea.scrollTop = logArea.scrollHeight;
            }
        }
        
        function testElements() {
            const result = document.getElementById('elementsResult');
            let html = '<h4>نتائج فحص العناصر:</h4>';
            
            // Check nav links
            const navLinks = document.querySelectorAll('.nav-link');
            html += `<p>🔗 روابط التنقل: ${navLinks.length}</p>`;
            
            // Check sections
            const sections = document.querySelectorAll('.content-section');
            html += `<p>📄 الأقسام: ${sections.length}</p>`;
            
            // Check if main.js is loaded
            html += `<p>📜 main.js محمل: ${typeof window.app !== 'undefined' ? 'نعم' : 'لا'}</p>`;
            
            // Check CSS
            const testSection = document.querySelector('.content-section');
            if (testSection) {
                const styles = window.getComputedStyle(testSection);
                html += `<p>🎨 CSS display: ${styles.display}</p>`;
            }
            
            result.innerHTML = html;
            result.className = 'result info';
        }
        
        function testNavigation(sectionName) {
            const result = document.getElementById('navigationResult');
            log(`Testing navigation to: ${sectionName}`, 'info');
            
            try {
                // Hide all sections
                document.querySelectorAll('.content-section').forEach(section => {
                    section.classList.remove('active');
                });
                
                // Show target section
                const targetSection = document.getElementById(sectionName);
                if (targetSection) {
                    targetSection.classList.add('active');
                    log(`✅ Section ${sectionName} activated`, 'success');
                    result.innerHTML = `<span class="success">✅ تم التنقل إلى ${sectionName} بنجاح</span>`;
                } else {
                    log(`❌ Section ${sectionName} not found`, 'error');
                    result.innerHTML = `<span class="error">❌ القسم ${sectionName} غير موجود</span>`;
                }
            } catch (error) {
                log(`❌ Navigation error: ${error.message}`, 'error');
                result.innerHTML = `<span class="error">❌ خطأ: ${error.message}</span>`;
            }
        }
        
        function simulateClicks() {
            const result = document.getElementById('clickResult');
            const navLinks = document.querySelectorAll('.nav-link');
            
            if (navLinks.length === 0) {
                result.innerHTML = '<span class="error">❌ لا توجد روابط تنقل</span>';
                return;
            }
            
            let html = '<h4>محاكاة النقرات:</h4>';
            
            navLinks.forEach((link, index) => {
                const section = link.dataset.section;
                html += `<p>${index + 1}. ${section}: `;
                
                try {
                    // Simulate click
                    const event = new MouseEvent('click', {
                        bubbles: true,
                        cancelable: true,
                        view: window
                    });
                    
                    link.dispatchEvent(event);
                    html += '<span class="success">✅ تم</span>';
                    log(`Simulated click on ${section}`, 'info');
                } catch (error) {
                    html += `<span class="error">❌ ${error.message}</span>`;
                    log(`Click simulation failed for ${section}: ${error.message}`, 'error');
                }
                
                html += '</p>';
            });
            
            result.innerHTML = html;
        }
        
        function testCSS() {
            const result = document.getElementById('cssResult');
            let html = '<h4>فحص CSS:</h4>';
            
            const sections = document.querySelectorAll('.content-section');
            sections.forEach(section => {
                const styles = window.getComputedStyle(section);
                const isActive = section.classList.contains('active');
                
                html += `<p><strong>${section.id}:</strong><br>`;
                html += `- active class: ${isActive ? 'نعم' : 'لا'}<br>`;
                html += `- display: ${styles.display}<br>`;
                html += `- opacity: ${styles.opacity}<br>`;
                html += `- visibility: ${styles.visibility}</p>`;
            });
            
            result.innerHTML = html;
            result.className = 'result info';
        }
        
        function startLogging() {
            logArea = document.getElementById('logArea');
            isLogging = true;
            log('🚀 بدء تسجيل الأحداث...', 'success');
            
            // Override console methods
            const originalLog = console.log;
            const originalError = console.error;
            const originalWarn = console.warn;
            
            console.log = function(...args) {
                originalLog.apply(console, args);
                log(args.join(' '), 'info');
            };
            
            console.error = function(...args) {
                originalError.apply(console, args);
                log(args.join(' '), 'error');
            };
            
            console.warn = function(...args) {
                originalWarn.apply(console, args);
                log(args.join(' '), 'warning');
            };
        }
        
        function clearLog() {
            const logArea = document.getElementById('logArea');
            logArea.innerHTML = '';
        }
        
        // Auto-run basic tests on load
        window.addEventListener('load', () => {
            setTimeout(() => {
                testElements();
                log('🔍 صفحة اختبار التنقل جاهزة', 'success');
            }, 500);
        });
    </script>
</body>
</html>
