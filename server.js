// Simple HTTP Server for Delivery Management System
const http = require('http');
const fs = require('fs');
const path = require('path');
const url = require('url');

const PORT = process.env.PORT || 8000;

// MIME types
const mimeTypes = {
    '.html': 'text/html',
    '.css': 'text/css',
    '.js': 'application/javascript',
    '.json': 'application/json',
    '.png': 'image/png',
    '.jpg': 'image/jpeg',
    '.gif': 'image/gif',
    '.svg': 'image/svg+xml',
    '.ico': 'image/x-icon',
    '.woff': 'font/woff',
    '.woff2': 'font/woff2',
    '.ttf': 'font/ttf',
    '.eot': 'application/vnd.ms-fontobject'
};

function getContentType(filePath) {
    const ext = path.extname(filePath).toLowerCase();
    return mimeTypes[ext] || 'application/octet-stream';
}

function serveFile(res, filePath) {
    fs.readFile(filePath, (err, data) => {
        if (err) {
            if (err.code === 'ENOENT') {
                res.writeHead(404, { 'Content-Type': 'text/html; charset=utf-8' });
                res.end(`
                    <!DOCTYPE html>
                    <html lang="ar" dir="rtl">
                    <head>
                        <meta charset="UTF-8">
                        <title>404 - الملف غير موجود</title>
                        <style>
                            body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
                            h1 { color: #e74c3c; }
                            p { color: #666; }
                            a { color: #3498db; text-decoration: none; }
                        </style>
                    </head>
                    <body>
                        <h1>404 - الملف غير موجود</h1>
                        <p>الملف المطلوب غير موجود: ${filePath}</p>
                        <a href="/">العودة للصفحة الرئيسية</a>
                    </body>
                    </html>
                `);
            } else {
                res.writeHead(500, { 'Content-Type': 'text/html; charset=utf-8' });
                res.end(`
                    <!DOCTYPE html>
                    <html lang="ar" dir="rtl">
                    <head>
                        <meta charset="UTF-8">
                        <title>خطأ في الخادم</title>
                        <style>
                            body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
                            h1 { color: #e74c3c; }
                            p { color: #666; }
                        </style>
                    </head>
                    <body>
                        <h1>خطأ في الخادم</h1>
                        <p>حدث خطأ أثناء قراءة الملف</p>
                    </body>
                    </html>
                `);
            }
        } else {
            const contentType = getContentType(filePath);
            res.writeHead(200, { 
                'Content-Type': contentType,
                'Cache-Control': 'no-cache'
            });
            res.end(data);
        }
    });
}

const server = http.createServer((req, res) => {
    // Enable CORS
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

    const parsedUrl = url.parse(req.url, true);
    let pathname = parsedUrl.pathname;

    // Remove leading slash
    if (pathname.startsWith('/')) {
        pathname = pathname.substring(1);
    }

    // Default to index.html
    if (pathname === '' || pathname === '/') {
        pathname = 'index.html';
    }

    const filePath = path.join(__dirname, pathname);

    // Security check - prevent directory traversal
    if (!filePath.startsWith(__dirname)) {
        res.writeHead(403, { 'Content-Type': 'text/html; charset=utf-8' });
        res.end(`
            <!DOCTYPE html>
            <html lang="ar" dir="rtl">
            <head>
                <meta charset="UTF-8">
                <title>403 - ممنوع</title>
                <style>
                    body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
                    h1 { color: #e74c3c; }
                </style>
            </head>
            <body>
                <h1>403 - الوصول ممنوع</h1>
            </body>
            </html>
        `);
        return;
    }

    console.log(`${new Date().toISOString()} - ${req.method} ${req.url} -> ${filePath}`);

    // Check if file exists
    fs.stat(filePath, (err, stats) => {
        if (err) {
            serveFile(res, filePath);
        } else if (stats.isDirectory()) {
            // Try to serve index.html from directory
            const indexPath = path.join(filePath, 'index.html');
            serveFile(res, indexPath);
        } else {
            serveFile(res, filePath);
        }
    });
});

server.listen(PORT, () => {
    console.log('🚀 نظام إدارة التوصيل');
    console.log('='.repeat(50));
    console.log(`✅ الخادم يعمل على المنفذ: ${PORT}`);
    console.log(`🌐 الرابط: http://localhost:${PORT}`);
    console.log(`📁 المجلد: ${__dirname}`);
    console.log('='.repeat(50));
    console.log('اضغط Ctrl+C لإيقاف الخادم');
    console.log('');
});

server.on('error', (err) => {
    if (err.code === 'EADDRINUSE') {
        console.error(`❌ المنفذ ${PORT} مستخدم بالفعل`);
        console.log('جرب منفذ آخر:');
        console.log(`node server.js`);
        console.log('أو');
        console.log(`PORT=8080 node server.js`);
    } else {
        console.error('❌ خطأ في الخادم:', err);
    }
});

// Graceful shutdown
process.on('SIGINT', () => {
    console.log('\n🛑 إيقاف الخادم...');
    server.close(() => {
        console.log('✅ تم إيقاف الخادم بنجاح');
        process.exit(0);
    });
});

process.on('SIGTERM', () => {
    console.log('\n🛑 إيقاف الخادم...');
    server.close(() => {
        console.log('✅ تم إيقاف الخادم بنجاح');
        process.exit(0);
    });
});
