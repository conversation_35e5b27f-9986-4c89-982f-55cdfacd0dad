<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - نظام إدارة التوصيل</title>
    
    <!-- Styles -->
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="styles/neumorphic.css">
    <link rel="stylesheet" href="styles/login.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- PWA Manifest -->
    <link rel="manifest" href="manifest.json">
    
    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#667eea">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="إدارة التوصيل">
    
    <!-- PWA Icons -->
    <link rel="apple-touch-icon" href="icon-192x192.png">
    <link rel="icon" type="image/png" sizes="192x192" href="icon-192x192.png">
    <link rel="icon" type="image/png" sizes="512x512" href="icon-512x512.png">
</head>
<body class="login-page">
    <!-- Background Animation -->
    <div class="login-background">
        <div class="floating-shapes">
            <div class="shape shape-1"></div>
            <div class="shape shape-2"></div>
            <div class="shape shape-3"></div>
            <div class="shape shape-4"></div>
            <div class="shape shape-5"></div>
        </div>
    </div>

    <!-- Login Container -->
    <div class="login-container">
        <!-- Logo Section -->
        <div class="login-header">
            <div class="logo-container">
                <div class="logo-icon">
                    <i class="fas fa-shipping-fast"></i>
                </div>
                <h1 class="logo-text">نظام إدارة التوصيل</h1>
                <p class="logo-subtitle">نظام شامل لإدارة شركات التوصيل</p>
            </div>
        </div>

        <!-- Login Form -->
        <div class="login-form-container">
            <div class="login-card">
                <div class="login-card-header">
                    <h2>تسجيل الدخول</h2>
                    <p>أدخل بياناتك للوصول إلى النظام</p>
                </div>

                <form id="loginForm" class="login-form">
                    <!-- Username Field -->
                    <div class="form-group">
                        <div class="input-container">
                            <div class="input-icon">
                                <i class="fas fa-user"></i>
                            </div>
                            <input 
                                type="text" 
                                id="username" 
                                name="username" 
                                class="form-input" 
                                placeholder="اسم المستخدم"
                                required
                                autocomplete="username"
                            >
                            <label for="username" class="floating-label">اسم المستخدم</label>
                        </div>
                        <div class="input-error" id="usernameError"></div>
                    </div>

                    <!-- Password Field -->
                    <div class="form-group">
                        <div class="input-container">
                            <div class="input-icon">
                                <i class="fas fa-lock"></i>
                            </div>
                            <input 
                                type="password" 
                                id="password" 
                                name="password" 
                                class="form-input" 
                                placeholder="كلمة المرور"
                                required
                                autocomplete="current-password"
                            >
                            <label for="password" class="floating-label">كلمة المرور</label>
                            <button type="button" class="password-toggle" id="passwordToggle">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                        <div class="input-error" id="passwordError"></div>
                    </div>

                    <!-- Remember Me & Forgot Password -->
                    <div class="form-options">
                        <label class="checkbox-container">
                            <input type="checkbox" id="rememberMe" name="rememberMe">
                            <span class="checkmark"></span>
                            <span class="checkbox-text">تذكرني</span>
                        </label>
                        <a href="#" class="forgot-password" id="forgotPasswordLink">نسيت كلمة المرور؟</a>
                    </div>

                    <!-- Login Button -->
                    <button type="submit" class="login-btn" id="loginBtn">
                        <span class="btn-text">تسجيل الدخول</span>
                        <div class="btn-loader" style="display: none;">
                            <div class="spinner"></div>
                        </div>
                    </button>

                    <!-- Error Message -->
                    <div class="login-error" id="loginError" style="display: none;">
                        <i class="fas fa-exclamation-triangle"></i>
                        <span id="loginErrorText"></span>
                    </div>
                </form>

                <!-- Demo Accounts -->
                <div class="demo-accounts">
                    <h4>حسابات تجريبية:</h4>
                    <div class="demo-account-list">
                        <div class="demo-account" data-username="admin" data-password="admin123">
                            <strong>مدير النظام:</strong> admin / admin123
                        </div>
                        <div class="demo-account" data-username="manager" data-password="manager123">
                            <strong>مدير العمليات:</strong> manager / manager123
                        </div>
                        <div class="demo-account" data-username="accountant" data-password="accountant123">
                            <strong>محاسب:</strong> accountant / accountant123
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <div class="login-footer">
            <p>&copy; 2024 نظام إدارة التوصيل. جميع الحقوق محفوظة.</p>
            <div class="footer-links">
                <a href="#" id="aboutLink">حول النظام</a>
                <a href="#" id="supportLink">الدعم التقني</a>
                <a href="#" id="privacyLink">سياسة الخصوصية</a>
            </div>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loadingOverlay" style="display: none;">
        <div class="loading-content">
            <div class="loading-spinner"></div>
            <p>جاري تسجيل الدخول...</p>
        </div>
    </div>

    <!-- Forgot Password Modal -->
    <div class="modal-overlay" id="forgotPasswordModal" style="display: none;">
        <div class="modal forgot-password-modal">
            <div class="modal-header">
                <h3>استعادة كلمة المرور</h3>
                <button class="modal-close" id="closeForgotModal">&times;</button>
            </div>
            <div class="modal-body">
                <p>أدخل اسم المستخدم أو البريد الإلكتروني لاستعادة كلمة المرور:</p>
                <form id="forgotPasswordForm">
                    <div class="form-group">
                        <input type="text" class="form-input" placeholder="اسم المستخدم أو البريد الإلكتروني" required>
                    </div>
                    <button type="submit" class="btn-primary">إرسال رابط الاستعادة</button>
                </form>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="js/storage.js"></script>
    <script src="js/login.js"></script>
</body>
</html>
