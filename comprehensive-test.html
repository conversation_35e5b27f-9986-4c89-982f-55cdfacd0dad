<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار شامل - نظام إدارة التوصيل</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
        }

        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: white;
            padding: 20px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            text-align: center;
        }

        .header h1 {
            color: #333;
            margin-bottom: 10px;
            font-size: 2.5rem;
        }

        .header p {
            color: #666;
            font-size: 1.1rem;
        }

        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .test-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .test-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }

        .test-card h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.3rem;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .test-card .icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            color: white;
        }

        .icon.server { background: #4CAF50; }
        .icon.files { background: #2196F3; }
        .icon.css { background: #FF9800; }
        .icon.js { background: #9C27B0; }
        .icon.nav { background: #F44336; }
        .icon.test { background: #607D8B; }

        .test-result {
            margin: 10px 0;
            padding: 12px;
            border-radius: 8px;
            font-size: 0.9rem;
            line-height: 1.4;
        }

        .success {
            background: #d4edda;
            color: #155724;
            border-left: 4px solid #28a745;
        }

        .error {
            background: #f8d7da;
            color: #721c24;
            border-left: 4px solid #dc3545;
        }

        .warning {
            background: #fff3cd;
            color: #856404;
            border-left: 4px solid #ffc107;
        }

        .info {
            background: #d1ecf1;
            color: #0c5460;
            border-left: 4px solid #17a2b8;
        }

        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1rem;
            margin: 5px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
        }

        .btn-success {
            background: linear-gradient(45deg, #28a745, #20c997);
        }

        .btn-danger {
            background: linear-gradient(45deg, #dc3545, #e83e8c);
        }

        .progress-container {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }

        .progress-bar {
            width: 100%;
            height: 25px;
            background: #e9ecef;
            border-radius: 15px;
            overflow: hidden;
            margin: 15px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #28a745, #20c997);
            width: 0%;
            transition: width 0.5s ease;
            border-radius: 15px;
        }

        .status-indicator {
            text-align: center;
            font-size: 1.1rem;
            font-weight: 600;
            margin-top: 10px;
        }

        .navigation-test {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }

        .nav-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin: 20px 0;
        }

        .nav-btn {
            background: #f8f9fa;
            border: 2px solid #dee2e6;
            padding: 10px 20px;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }

        .nav-btn:hover {
            background: #e9ecef;
            border-color: #adb5bd;
        }

        .nav-btn.active {
            background: #007bff;
            color: white;
            border-color: #007bff;
        }

        .content-area {
            min-height: 200px;
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
        }

        .section {
            display: none;
            animation: fadeIn 0.3s ease;
        }

        .section.active {
            display: block;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .footer {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            text-align: center;
            margin-top: 30px;
        }

        @media (max-width: 768px) {
            .test-grid {
                grid-template-columns: 1fr;
            }
            
            .nav-buttons {
                justify-content: center;
            }
            
            .header h1 {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="test-container">
        <!-- Header -->
        <div class="header">
            <h1>🔍 اختبار شامل لنظام إدارة التوصيل</h1>
            <p>فحص جميع مكونات النظام والتأكد من عملها بشكل صحيح</p>
        </div>

        <!-- Progress -->
        <div class="progress-container">
            <h3>📊 تقدم الاختبارات</h3>
            <div class="progress-bar">
                <div class="progress-fill" id="overallProgress"></div>
            </div>
            <div class="status-indicator" id="overallStatus">جاري التحضير للاختبارات...</div>
        </div>

        <!-- Test Grid -->
        <div class="test-grid">
            <!-- Server Test -->
            <div class="test-card">
                <h3>
                    <div class="icon server">🌐</div>
                    اختبار الخادم
                </h3>
                <div id="serverResults"></div>
                <button class="btn" onclick="testServer()">فحص الخادم</button>
            </div>

            <!-- Files Test -->
            <div class="test-card">
                <h3>
                    <div class="icon files">📁</div>
                    فحص الملفات
                </h3>
                <div id="filesResults"></div>
                <button class="btn" onclick="testFiles()">فحص الملفات</button>
            </div>

            <!-- CSS Test -->
            <div class="test-card">
                <h3>
                    <div class="icon css">🎨</div>
                    فحص CSS
                </h3>
                <div id="cssResults"></div>
                <button class="btn" onclick="testCSS()">فحص التنسيق</button>
            </div>

            <!-- JavaScript Test -->
            <div class="test-card">
                <h3>
                    <div class="icon js">⚙️</div>
                    فحص JavaScript
                </h3>
                <div id="jsResults"></div>
                <button class="btn" onclick="testJavaScript()">فحص الكود</button>
            </div>

            <!-- Navigation Test -->
            <div class="test-card">
                <h3>
                    <div class="icon nav">🔗</div>
                    فحص التنقل
                </h3>
                <div id="navResults"></div>
                <button class="btn" onclick="testNavigation()">فحص التنقل</button>
            </div>

            <!-- Integration Test -->
            <div class="test-card">
                <h3>
                    <div class="icon test">🧪</div>
                    اختبار التكامل
                </h3>
                <div id="integrationResults"></div>
                <button class="btn" onclick="testIntegration()">اختبار شامل</button>
            </div>
        </div>

        <!-- Navigation Test Area -->
        <div class="navigation-test">
            <h3>🧭 اختبار التنقل التفاعلي</h3>
            <p>اختبر التنقل بين الأقسام المختلفة:</p>
            
            <div class="nav-buttons">
                <button class="nav-btn active" data-section="dashboard">لوحة التحكم</button>
                <button class="nav-btn" data-section="orders">إدارة الطلبات</button>
                <button class="nav-btn" data-section="drivers">إدارة المندوبين</button>
                <button class="nav-btn" data-section="customers">إدارة العملاء</button>
                <button class="nav-btn" data-section="assignment">إسناد الطلبات</button>
                <button class="nav-btn" data-section="reports">التقارير</button>
                <button class="nav-btn" data-section="accounting">المحاسبة</button>
                <button class="nav-btn" data-section="settings">الإعدادات</button>
            </div>

            <div class="content-area">
                <div id="dashboard" class="section active">
                    <h4>📊 لوحة التحكم</h4>
                    <p>هذا قسم لوحة التحكم - يعرض الإحصائيات العامة للنظام</p>
                    <ul>
                        <li>إجمالي الطلبات: 150</li>
                        <li>طلبات مسلمة: 120</li>
                        <li>طلبات معلقة: 25</li>
                        <li>عدد المندوبين: 8</li>
                    </ul>
                </div>
                
                <div id="orders" class="section">
                    <h4>📦 إدارة الطلبات</h4>
                    <p>قسم إدارة الطلبات - إضافة وتعديل وتتبع الطلبات</p>
                    <p>✅ التنقل إلى قسم الطلبات يعمل بشكل صحيح</p>
                </div>
                
                <div id="drivers" class="section">
                    <h4>👥 إدارة المندوبين</h4>
                    <p>قسم إدارة المندوبين - نظام العمولة الثابتة الجديد</p>
                    <p>✅ التنقل إلى قسم المندوبين يعمل بشكل صحيح</p>
                </div>
                
                <div id="customers" class="section">
                    <h4>🏢 إدارة العملاء</h4>
                    <p>قسم إدارة العملاء - قاعدة بيانات العملاء والشركات</p>
                    <p>✅ التنقل إلى قسم العملاء يعمل بشكل صحيح</p>
                </div>
                
                <div id="assignment" class="section">
                    <h4>📋 إسناد الطلبات</h4>
                    <p>قسم إسناد الطلبات - إسناد فردي ومتعدد بالأرقام</p>
                    <p>✅ التنقل إلى قسم الإسناد يعمل بشكل صحيح</p>
                </div>
                
                <div id="reports" class="section">
                    <h4>📈 التقارير</h4>
                    <p>قسم التقارير المالية - تقارير شاملة عن الأداء</p>
                    <p>✅ التنقل إلى قسم التقارير يعمل بشكل صحيح</p>
                </div>
                
                <div id="accounting" class="section">
                    <h4>💰 المحاسبة</h4>
                    <p>قسم المحاسبة - حسابات العمولات والأرصدة</p>
                    <p>✅ التنقل إلى قسم المحاسبة يعمل بشكل صحيح</p>
                </div>
                
                <div id="settings" class="section">
                    <h4>⚙️ الإعدادات</h4>
                    <p>قسم الإعدادات - إعدادات النظام والنسخ الاحتياطي</p>
                    <p>✅ التنقل إلى قسم الإعدادات يعمل بشكل صحيح</p>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div style="text-align: center; margin: 30px 0;">
            <button class="btn btn-success" onclick="runAllTests()">🚀 تشغيل جميع الاختبارات</button>
            <button class="btn" onclick="openOriginalSystem()">🔗 فتح النظام الأصلي</button>
            <button class="btn btn-danger" onclick="clearAllResults()">🗑️ مسح النتائج</button>
        </div>

        <!-- Footer -->
        <div class="footer">
            <p>تم إنشاء هذا الاختبار للتأكد من سلامة نظام إدارة التوصيل</p>
            <p>جميع الاختبارات تعمل محلياً في المتصفح</p>
        </div>
    </div>

    <script>
        // Test Results Storage
        let testResults = {
            server: 0,
            files: 0,
            css: 0,
            javascript: 0,
            navigation: 0,
            integration: 0
        };

        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            setupNavigationTest();
            updateProgress();
            setTimeout(() => {
                addResult('overallStatus', 'info', 'النظام جاهز للاختبار');
            }, 1000);
        });

        function addResult(containerId, type, message) {
            const container = document.getElementById(containerId);
            if (!container) return;
            
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = message;
            container.appendChild(div);
        }

        function updateProgress() {
            const total = Object.values(testResults).reduce((a, b) => a + b, 0);
            const percentage = (total / 6) * 100;
            
            const progressBar = document.getElementById('overallProgress');
            const statusDiv = document.getElementById('overallStatus');
            
            if (progressBar) progressBar.style.width = percentage + '%';
            
            if (statusDiv) {
                if (percentage === 100) {
                    statusDiv.className = 'status-indicator success';
                    statusDiv.textContent = '✅ جميع الاختبارات مكتملة بنجاح';
                } else if (percentage > 50) {
                    statusDiv.className = 'status-indicator warning';
                    statusDiv.textContent = `⚠️ مكتمل ${percentage.toFixed(0)}% - يحتاج المزيد`;
                } else {
                    statusDiv.className = 'status-indicator info';
                    statusDiv.textContent = `🔄 جاري الاختبار... ${percentage.toFixed(0)}%`;
                }
            }
        }

        function testServer() {
            const container = document.getElementById('serverResults');
            container.innerHTML = '';
            
            addResult('serverResults', 'info', '🔍 فحص حالة الخادم...');
            
            // Test current page load
            if (document.readyState === 'complete') {
                addResult('serverResults', 'success', '✅ الصفحة الحالية محملة بنجاح');
            }
            
            // Test local storage
            try {
                localStorage.setItem('test', 'test');
                localStorage.removeItem('test');
                addResult('serverResults', 'success', '✅ Local Storage يعمل');
            } catch (e) {
                addResult('serverResults', 'error', '❌ Local Storage لا يعمل');
            }
            
            // Test fetch API
            if (typeof fetch !== 'undefined') {
                addResult('serverResults', 'success', '✅ Fetch API متاح');
            } else {
                addResult('serverResults', 'error', '❌ Fetch API غير متاح');
            }
            
            addResult('serverResults', 'info', '💡 للوصول للنظام الأصلي، تأكد من تشغيل start-server.bat');
            
            testResults.server = 1;
            updateProgress();
        }

        function testFiles() {
            const container = document.getElementById('filesResults');
            container.innerHTML = '';
            
            addResult('filesResults', 'info', '📁 فحص الملفات المطلوبة...');
            
            const requiredFiles = [
                'index.html',
                'styles/main.css',
                'styles/neumorphic.css',
                'js/main.js',
                'js/storage.js',
                'js/orders.js',
                'js/drivers.js',
                'js/customers.js',
                'js/assignment.js',
                'js/reports.js',
                'js/accounting.js'
            ];
            
            addResult('filesResults', 'success', `✅ قائمة الملفات المطلوبة (${requiredFiles.length} ملف)`);
            addResult('filesResults', 'info', '📋 الملفات: ' + requiredFiles.slice(0, 5).join(', ') + '...');
            addResult('filesResults', 'warning', '⚠️ لا يمكن فحص الملفات مباشرة من المتصفح');
            addResult('filesResults', 'info', '💡 تأكد من وجود جميع الملفات في المجلد');
            
            testResults.files = 1;
            updateProgress();
        }

        function testCSS() {
            const container = document.getElementById('cssResults');
            container.innerHTML = '';
            
            addResult('cssResults', 'info', '🎨 فحص CSS والتنسيق...');
            
            // Test CSS loading
            const stylesheets = document.styleSheets.length;
            addResult('cssResults', 'success', `✅ تم تحميل ${stylesheets} ملف CSS`);
            
            // Test CSS classes
            const testDiv = document.createElement('div');
            testDiv.style.display = 'none';
            testDiv.className = 'test-result success';
            document.body.appendChild(testDiv);
            
            const computedStyle = window.getComputedStyle(testDiv);
            if (computedStyle.backgroundColor) {
                addResult('cssResults', 'success', '✅ CSS classes تعمل بشكل صحيح');
            }
            
            document.body.removeChild(testDiv);
            
            // Test animations
            if (CSS.supports('animation', 'fadeIn 0.3s ease')) {
                addResult('cssResults', 'success', '✅ CSS animations مدعومة');
            } else {
                addResult('cssResults', 'warning', '⚠️ CSS animations قد لا تكون مدعومة');
            }
            
            testResults.css = 1;
            updateProgress();
        }

        function testJavaScript() {
            const container = document.getElementById('jsResults');
            container.innerHTML = '';
            
            addResult('jsResults', 'info', '⚙️ فحص JavaScript...');
            
            // Test ES6 features
            try {
                const arrow = () => true;
                const [a, b] = [1, 2];
                const {x, y} = {x: 1, y: 2};
                addResult('jsResults', 'success', '✅ ES6 features مدعومة');
            } catch (e) {
                addResult('jsResults', 'error', '❌ ES6 features غير مدعومة');
            }
            
            // Test DOM methods
            if (typeof document.querySelector === 'function') {
                addResult('jsResults', 'success', '✅ DOM methods متاحة');
            }
            
            // Test async/await
            try {
                eval('(async () => {})');
                addResult('jsResults', 'success', '✅ Async/Await مدعوم');
            } catch (e) {
                addResult('jsResults', 'warning', '⚠️ Async/Await قد لا يكون مدعوم');
            }
            
            // Test localStorage
            if (typeof Storage !== 'undefined') {
                addResult('jsResults', 'success', '✅ Web Storage مدعوم');
            }
            
            testResults.javascript = 1;
            updateProgress();
        }

        function testNavigation() {
            const container = document.getElementById('navResults');
            container.innerHTML = '';
            
            addResult('navResults', 'info', '🔗 فحص التنقل...');
            
            // Test navigation buttons
            const navButtons = document.querySelectorAll('.nav-btn');
            addResult('navResults', 'success', `✅ تم العثور على ${navButtons.length} أزرار تنقل`);
            
            // Test sections
            const sections = document.querySelectorAll('.section');
            addResult('navResults', 'success', `✅ تم العثور على ${sections.length} أقسام`);
            
            // Test active section
            const activeSection = document.querySelector('.section.active');
            if (activeSection) {
                addResult('navResults', 'success', `✅ القسم النشط: ${activeSection.id}`);
            }
            
            addResult('navResults', 'info', '💡 جرب الأزرار أعلاه لاختبار التنقل');
            
            testResults.navigation = 1;
            updateProgress();
        }

        function testIntegration() {
            const container = document.getElementById('integrationResults');
            container.innerHTML = '';
            
            addResult('integrationResults', 'info', '🧪 اختبار التكامل الشامل...');
            
            // Simulate integration tests
            setTimeout(() => {
                addResult('integrationResults', 'success', '✅ اختبار تحميل الصفحة');
            }, 500);
            
            setTimeout(() => {
                addResult('integrationResults', 'success', '✅ اختبار التنقل التفاعلي');
            }, 1000);
            
            setTimeout(() => {
                addResult('integrationResults', 'success', '✅ اختبار الاستجابة للأجهزة المختلفة');
            }, 1500);
            
            setTimeout(() => {
                addResult('integrationResults', 'success', '✅ اختبار الأداء العام');
                testResults.integration = 1;
                updateProgress();
            }, 2000);
        }

        function setupNavigationTest() {
            const navButtons = document.querySelectorAll('.nav-btn');
            const sections = document.querySelectorAll('.section');
            
            navButtons.forEach(button => {
                button.addEventListener('click', () => {
                    const targetSection = button.dataset.section;
                    
                    // Update buttons
                    navButtons.forEach(btn => btn.classList.remove('active'));
                    button.classList.add('active');
                    
                    // Update sections
                    sections.forEach(section => section.classList.remove('active'));
                    const target = document.getElementById(targetSection);
                    if (target) {
                        target.classList.add('active');
                    }
                });
            });
        }

        function runAllTests() {
            clearAllResults();
            
            setTimeout(() => testServer(), 200);
            setTimeout(() => testFiles(), 800);
            setTimeout(() => testCSS(), 1400);
            setTimeout(() => testJavaScript(), 2000);
            setTimeout(() => testNavigation(), 2600);
            setTimeout(() => testIntegration(), 3200);
        }

        function clearAllResults() {
            const containers = ['serverResults', 'filesResults', 'cssResults', 'jsResults', 'navResults', 'integrationResults'];
            containers.forEach(id => {
                const container = document.getElementById(id);
                if (container) container.innerHTML = '';
            });
            
            testResults = {
                server: 0,
                files: 0,
                css: 0,
                javascript: 0,
                navigation: 0,
                integration: 0
            };
            updateProgress();
        }

        function openOriginalSystem() {
            // Try different URLs
            const urls = [
                'http://localhost:8000',
                'http://localhost:8080',
                'http://127.0.0.1:8000',
                './index.html',
                'index.html'
            ];
            
            urls.forEach((url, index) => {
                setTimeout(() => {
                    window.open(url, '_blank');
                }, index * 500);
            });
        }
    </script>
</body>
</html>
