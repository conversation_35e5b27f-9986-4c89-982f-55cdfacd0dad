// Login System
class LoginSystem {
    constructor() {
        this.isLoading = false;
        this.maxAttempts = 5;
        this.lockoutTime = 15 * 60 * 1000; // 15 minutes
        this.init();
    }

    async init() {
        await this.initializeDefaultUsers();
        this.setupEventListeners();
        this.checkExistingSession();
        this.setupDemoAccounts();
    }

    async initializeDefaultUsers() {
        try {
            const users = await storage.getAll('users') || [];
            
            if (users.length === 0) {
                const defaultUsers = [
                    {
                        id: 1,
                        username: 'admin',
                        password: this.hashPassword('admin123'),
                        name: 'مدير النظام',
                        email: '<EMAIL>',
                        phone: '***********',
                        role: 'admin',
                        active: true,
                        createdAt: new Date().toISOString(),
                        lastLogin: null,
                        loginAttempts: 0,
                        lockedUntil: null
                    },
                    {
                        id: 2,
                        username: 'manager',
                        password: this.hashPassword('manager123'),
                        name: 'مدير العمليات',
                        email: '<EMAIL>',
                        phone: '***********',
                        role: 'manager',
                        active: true,
                        createdAt: new Date().toISOString(),
                        lastLogin: null,
                        loginAttempts: 0,
                        lockedUntil: null
                    },
                    {
                        id: 3,
                        username: 'accountant',
                        password: this.hashPassword('accountant123'),
                        name: 'المحاسب الرئيسي',
                        email: '<EMAIL>',
                        phone: '***********',
                        role: 'accountant',
                        active: true,
                        createdAt: new Date().toISOString(),
                        lastLogin: null,
                        loginAttempts: 0,
                        lockedUntil: null
                    }
                ];

                for (const user of defaultUsers) {
                    await storage.add('users', user);
                }
                
                console.log('Default users created successfully');
            }
        } catch (error) {
            console.error('Error initializing default users:', error);
        }
    }

    setupEventListeners() {
        // Login form submission
        const loginForm = document.getElementById('loginForm');
        if (loginForm) {
            loginForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleLogin();
            });
        }

        // Password toggle
        const passwordToggle = document.getElementById('passwordToggle');
        const passwordInput = document.getElementById('password');
        if (passwordToggle && passwordInput) {
            passwordToggle.addEventListener('click', () => {
                const type = passwordInput.type === 'password' ? 'text' : 'password';
                passwordInput.type = type;
                
                const icon = passwordToggle.querySelector('i');
                icon.className = type === 'password' ? 'fas fa-eye' : 'fas fa-eye-slash';
            });
        }

        // Input validation
        const usernameInput = document.getElementById('username');
        const passwordInputField = document.getElementById('password');
        
        if (usernameInput) {
            usernameInput.addEventListener('input', () => this.validateUsername());
            usernameInput.addEventListener('blur', () => this.validateUsername());
        }
        
        if (passwordInputField) {
            passwordInputField.addEventListener('input', () => this.validatePassword());
            passwordInputField.addEventListener('blur', () => this.validatePassword());
        }

        // Forgot password
        const forgotPasswordLink = document.getElementById('forgotPasswordLink');
        if (forgotPasswordLink) {
            forgotPasswordLink.addEventListener('click', (e) => {
                e.preventDefault();
                this.showForgotPasswordModal();
            });
        }

        // Close forgot password modal
        const closeForgotModal = document.getElementById('closeForgotModal');
        if (closeForgotModal) {
            closeForgotModal.addEventListener('click', () => {
                this.hideForgotPasswordModal();
            });
        }

        // Forgot password form
        const forgotPasswordForm = document.getElementById('forgotPasswordForm');
        if (forgotPasswordForm) {
            forgotPasswordForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleForgotPassword();
            });
        }

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && e.ctrlKey) {
                this.handleLogin();
            }
        });
    }

    setupDemoAccounts() {
        const demoAccounts = document.querySelectorAll('.demo-account');
        demoAccounts.forEach(account => {
            account.addEventListener('click', () => {
                const username = account.dataset.username;
                const password = account.dataset.password;
                
                document.getElementById('username').value = username;
                document.getElementById('password').value = password;
                
                // Add visual feedback
                account.style.background = 'var(--primary-color)';
                account.style.color = 'white';
                
                setTimeout(() => {
                    account.style.background = '';
                    account.style.color = '';
                }, 300);
            });
        });
    }

    validateUsername() {
        const usernameInput = document.getElementById('username');
        const usernameError = document.getElementById('usernameError');
        const username = usernameInput.value.trim();

        if (!username) {
            this.showError(usernameError, 'اسم المستخدم مطلوب');
            return false;
        }

        if (username.length < 3) {
            this.showError(usernameError, 'اسم المستخدم يجب أن يكون 3 أحرف على الأقل');
            return false;
        }

        this.hideError(usernameError);
        return true;
    }

    validatePassword() {
        const passwordInput = document.getElementById('password');
        const passwordError = document.getElementById('passwordError');
        const password = passwordInput.value;

        if (!password) {
            this.showError(passwordError, 'كلمة المرور مطلوبة');
            return false;
        }

        if (password.length < 6) {
            this.showError(passwordError, 'كلمة المرور يجب أن تكون 6 أحرف على الأقل');
            return false;
        }

        this.hideError(passwordError);
        return true;
    }

    showError(errorElement, message) {
        errorElement.textContent = message;
        errorElement.classList.add('show');
    }

    hideError(errorElement) {
        errorElement.classList.remove('show');
    }

    async handleLogin() {
        if (this.isLoading) return;

        // Validate inputs
        const isUsernameValid = this.validateUsername();
        const isPasswordValid = this.validatePassword();

        if (!isUsernameValid || !isPasswordValid) {
            return;
        }

        const username = document.getElementById('username').value.trim();
        const password = document.getElementById('password').value;
        const rememberMe = document.getElementById('rememberMe').checked;

        this.setLoading(true);

        try {
            // Check for account lockout
            const lockoutCheck = this.checkAccountLockout(username);
            if (lockoutCheck.isLocked) {
                this.showLoginError(`الحساب مقفل لمدة ${Math.ceil(lockoutCheck.remainingTime / 60000)} دقيقة`);
                this.setLoading(false);
                return;
            }

            // Authenticate user
            const user = await this.authenticateUser(username, password);
            
            if (user) {
                // Reset login attempts
                await this.resetLoginAttempts(user.id);
                
                // Update last login
                await this.updateLastLogin(user.id);
                
                // Create session
                await this.createSession(user, rememberMe);
                
                // Redirect to main application
                this.redirectToApp();
            } else {
                // Increment login attempts
                await this.incrementLoginAttempts(username);
                this.showLoginError('اسم المستخدم أو كلمة المرور غير صحيحة');
            }
        } catch (error) {
            console.error('Login error:', error);
            this.showLoginError('حدث خطأ أثناء تسجيل الدخول. يرجى المحاولة مرة أخرى.');
        }

        this.setLoading(false);
    }

    async authenticateUser(username, password) {
        try {
            const users = await storage.getAll('users') || [];
            const user = users.find(u => 
                u.username === username && 
                u.active && 
                this.verifyPassword(password, u.password)
            );
            
            return user || null;
        } catch (error) {
            console.error('Authentication error:', error);
            return null;
        }
    }

    hashPassword(password) {
        // Simple hash function for demo purposes
        // In production, use a proper hashing library like bcrypt
        let hash = 0;
        for (let i = 0; i < password.length; i++) {
            const char = password.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // Convert to 32-bit integer
        }
        return hash.toString();
    }

    verifyPassword(password, hashedPassword) {
        return this.hashPassword(password) === hashedPassword;
    }

    checkAccountLockout(username) {
        const lockoutData = localStorage.getItem(`lockout_${username}`);
        if (!lockoutData) {
            return { isLocked: false, remainingTime: 0 };
        }

        const { lockedUntil, attempts } = JSON.parse(lockoutData);
        const now = Date.now();

        if (now < lockedUntil) {
            return { isLocked: true, remainingTime: lockedUntil - now };
        } else {
            // Lockout expired, clear it
            localStorage.removeItem(`lockout_${username}`);
            return { isLocked: false, remainingTime: 0 };
        }
    }

    async incrementLoginAttempts(username) {
        const attemptKey = `attempts_${username}`;
        const attempts = parseInt(localStorage.getItem(attemptKey) || '0') + 1;
        
        localStorage.setItem(attemptKey, attempts.toString());

        if (attempts >= this.maxAttempts) {
            const lockedUntil = Date.now() + this.lockoutTime;
            localStorage.setItem(`lockout_${username}`, JSON.stringify({
                lockedUntil,
                attempts
            }));
            localStorage.removeItem(attemptKey);
        }
    }

    async resetLoginAttempts(userId) {
        // This would typically update the database
        // For now, we'll just clear localStorage
        const users = await storage.getAll('users') || [];
        const user = users.find(u => u.id === userId);
        if (user) {
            localStorage.removeItem(`attempts_${user.username}`);
            localStorage.removeItem(`lockout_${user.username}`);
        }
    }

    async updateLastLogin(userId) {
        try {
            const user = await storage.getById('users', userId);
            if (user) {
                user.lastLogin = new Date().toISOString();
                await storage.update('users', user);
            }
        } catch (error) {
            console.error('Error updating last login:', error);
        }
    }

    async createSession(user, rememberMe) {
        const sessionData = {
            id: user.id,
            username: user.username,
            name: user.name,
            email: user.email,
            role: user.role,
            loginTime: new Date().toISOString(),
            rememberMe
        };

        if (rememberMe) {
            localStorage.setItem('userSession', JSON.stringify(sessionData));
        } else {
            sessionStorage.setItem('userSession', JSON.stringify(sessionData));
        }
    }

    checkExistingSession() {
        const sessionData = localStorage.getItem('userSession') || sessionStorage.getItem('userSession');
        
        if (sessionData) {
            try {
                const session = JSON.parse(sessionData);
                // Check if session is still valid (e.g., not expired)
                const loginTime = new Date(session.loginTime);
                const now = new Date();
                const hoursDiff = (now - loginTime) / (1000 * 60 * 60);
                
                // Session expires after 24 hours for remember me, 8 hours otherwise
                const maxHours = session.rememberMe ? 24 : 8;
                
                if (hoursDiff < maxHours) {
                    this.redirectToApp();
                } else {
                    // Session expired, clear it
                    this.clearSession();
                }
            } catch (error) {
                console.error('Error checking session:', error);
                this.clearSession();
            }
        }
    }

    clearSession() {
        localStorage.removeItem('userSession');
        sessionStorage.removeItem('userSession');
    }

    redirectToApp() {
        this.showLoadingOverlay('جاري تحميل النظام...');
        
        setTimeout(() => {
            window.location.href = 'index.html';
        }, 1500);
    }

    setLoading(loading) {
        this.isLoading = loading;
        const loginBtn = document.getElementById('loginBtn');
        const btnText = loginBtn.querySelector('.btn-text');
        const btnLoader = loginBtn.querySelector('.btn-loader');

        if (loading) {
            loginBtn.disabled = true;
            btnText.style.display = 'none';
            btnLoader.style.display = 'block';
        } else {
            loginBtn.disabled = false;
            btnText.style.display = 'block';
            btnLoader.style.display = 'none';
        }
    }

    showLoginError(message) {
        const loginError = document.getElementById('loginError');
        const loginErrorText = document.getElementById('loginErrorText');
        
        loginErrorText.textContent = message;
        loginError.style.display = 'flex';
        
        // Auto hide after 5 seconds
        setTimeout(() => {
            loginError.style.display = 'none';
        }, 5000);
    }

    showLoadingOverlay(message) {
        const overlay = document.getElementById('loadingOverlay');
        const messageElement = overlay.querySelector('p');
        
        if (messageElement) {
            messageElement.textContent = message;
        }
        
        overlay.style.display = 'flex';
    }

    showForgotPasswordModal() {
        const modal = document.getElementById('forgotPasswordModal');
        modal.style.display = 'flex';
    }

    hideForgotPasswordModal() {
        const modal = document.getElementById('forgotPasswordModal');
        modal.style.display = 'none';
    }

    handleForgotPassword() {
        // This would typically send a password reset email
        alert('تم إرسال رابط استعادة كلمة المرور إلى بريدك الإلكتروني');
        this.hideForgotPasswordModal();
    }
}

// Initialize login system when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new LoginSystem();
});
