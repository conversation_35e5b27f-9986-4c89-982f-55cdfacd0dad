// Assignment Manager for Order Assignment System
class AssignmentManager {
    constructor() {
        this.currentOrder = null;
        this.drivers = [];
        this.assignmentHistory = [];
        this.init();
    }

    async init() {
        try {
            await this.loadDrivers();
            this.setupEventListeners();
            this.loadAssignmentHistory();
            this.multipleOrders = [];
            this.validationResults = {};
        } catch (error) {
            console.error('Error initializing AssignmentManager:', error);
        }
    }

    setupEventListeners() {
        // Form submission
        const form = document.getElementById('assignmentForm');
        if (form) {
            form.addEventListener('submit', (e) => {
                e.preventDefault();
                this.assignOrder();
            });
        }

        // Multiple assignment form submission
        const multipleForm = document.getElementById('multipleAssignmentForm');
        if (multipleForm) {
            multipleForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.assignMultipleOrders();
            });
        }

        // Real-time tracking search
        const trackingInput = document.getElementById('assignmentTrackingInput');
        if (trackingInput) {
            let debounceTimer;
            trackingInput.addEventListener('input', (e) => {
                clearTimeout(debounceTimer);
                const value = e.target.value.trim();
                
                if (value.length >= 3) {
                    debounceTimer = setTimeout(() => {
                        this.searchByTracking(value);
                    }, 500);
                } else if (value.length === 0) {
                    this.clearOrderDetails();
                }
            });

            // Enter key search
            trackingInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    this.searchByTracking();
                }
            });
        }

        // Multiple tracking input with real-time validation
        const multipleTrackingInput = document.getElementById('multipleTrackingInput');
        if (multipleTrackingInput) {
            let debounceTimer;
            multipleTrackingInput.addEventListener('input', (e) => {
                clearTimeout(debounceTimer);
                const value = e.target.value.trim();

                if (value.length > 10) { // Start validation when there's enough content
                    debounceTimer = setTimeout(() => {
                        this.validateMultipleTracking();
                    }, 1000); // Wait 1 second after user stops typing
                }
            });
        }
    }

    async loadDrivers() {
        try {
            this.drivers = await storage.getAll('drivers');
            this.populateDriverSelect();
        } catch (error) {
            console.error('Error loading drivers:', error);
            window.app.showNotification('خطأ في تحميل المندوبين', 'error');
        }
    }

    populateDriverSelect() {
        const select = document.getElementById('assignmentDriverSelect');
        const multipleSelect = document.getElementById('multipleDriverSelect');

        const populateSelect = (selectElement) => {
            if (!selectElement) return;

            selectElement.innerHTML = '<option value="">اختر المندوب</option>';

            this.drivers.forEach(driver => {
                const option = document.createElement('option');
                option.value = driver.id;
                option.textContent = `${driver.name} - ${driver.city}`;
                selectElement.appendChild(option);
            });
        };

        populateSelect(select);
        populateSelect(multipleSelect);
    }

    async searchByTracking(trackingNumber = null) {
        const input = document.getElementById('assignmentTrackingInput');
        const errorDiv = document.getElementById('trackingSearchError');
        
        if (!trackingNumber) {
            trackingNumber = input?.value?.trim();
        }

        if (!trackingNumber) {
            this.showError('يرجى إدخال رقم الوصل');
            return;
        }

        try {
            // Search for order by tracking number
            const orders = await storage.getAll('orders');
            const foundOrder = orders.find(order => 
                order.trackingNumber && 
                order.trackingNumber.toLowerCase() === trackingNumber.toLowerCase()
            );

            if (foundOrder) {
                this.currentOrder = foundOrder;
                this.displayOrderDetails(foundOrder);
                this.hideError();
                
                // Set current driver if assigned
                const driverSelect = document.getElementById('assignmentDriverSelect');
                if (driverSelect && foundOrder.driverId) {
                    driverSelect.value = foundOrder.driverId;
                }
            } else {
                this.showError('لم يتم العثور على طلب بهذا الرقم');
                this.clearOrderDetails();
            }
        } catch (error) {
            console.error('Error searching for order:', error);
            this.showError('خطأ في البحث عن الطلب');
        }
    }

    displayOrderDetails(order) {
        const detailsCard = document.getElementById('orderDetailsCard');
        if (!detailsCard) return;

        // Update order information
        document.getElementById('orderIdDisplay').textContent = `#${order.id}`;
        document.getElementById('clientNameDisplay').textContent = order.clientName || '-';
        document.getElementById('customerNameDisplay').textContent = order.customerName || '-';
        document.getElementById('cityDisplay').textContent = order.city || '-';
        document.getElementById('priceDisplay').textContent = `${order.deliveryPrice} د.ع`;
        document.getElementById('statusDisplay').textContent = this.getStatusText(order.status);

        // Display current driver
        const currentDriverDisplay = document.getElementById('currentDriverDisplay');
        if (order.driverId) {
            const driver = this.drivers.find(d => d.id == order.driverId);
            currentDriverDisplay.textContent = driver ? driver.name : 'مندوب غير معروف';
            currentDriverDisplay.style.color = 'var(--warning-color)';
        } else {
            currentDriverDisplay.textContent = 'غير مُسند';
            currentDriverDisplay.style.color = 'var(--text-secondary)';
        }

        // Show/hide buttons based on order status
        this.updateActionButtons(order);

        // Show the details card
        detailsCard.style.display = 'block';
    }

    updateActionButtons(order) {
        const assignBtn = document.getElementById('assignBtn');
        const unassignBtn = document.getElementById('unassignBtn');

        // Check if order can be assigned
        const canAssign = order.status === 'pending' || order.status === 'assigned';
        
        if (assignBtn) {
            assignBtn.disabled = !canAssign;
            if (!canAssign) {
                assignBtn.title = 'لا يمكن إسناد الطلبات المسلمة أو المرجعة';
            } else {
                assignBtn.title = '';
            }
        }

        // Show unassign button if order is currently assigned
        if (unassignBtn) {
            if (order.driverId && canAssign) {
                unassignBtn.style.display = 'inline-block';
            } else {
                unassignBtn.style.display = 'none';
            }
        }
    }

    clearOrderDetails() {
        const detailsCard = document.getElementById('orderDetailsCard');
        if (detailsCard) {
            detailsCard.style.display = 'none';
        }
        this.currentOrder = null;
        
        // Reset driver selection
        const driverSelect = document.getElementById('assignmentDriverSelect');
        if (driverSelect) {
            driverSelect.value = '';
        }
    }

    async assignOrder() {
        if (!this.currentOrder) {
            this.showError('يرجى البحث عن طلب أولاً');
            return;
        }

        const driverSelect = document.getElementById('assignmentDriverSelect');
        const driverId = driverSelect?.value;

        if (!driverId) {
            this.showError('يرجى اختيار مندوب');
            return;
        }

        // Comprehensive validation
        const validationResult = await this.validateAssignment(this.currentOrder, driverId);
        if (!validationResult.isValid) {
            this.showError(validationResult.message);
            return;
        }

        // Check if order can be assigned
        if (this.currentOrder.status !== 'pending' && this.currentOrder.status !== 'assigned') {
            this.showError('لا يمكن إسناد الطلبات المسلمة أو المرجعة');
            return;
        }

        try {
            // Update order with new driver
            const updatedOrder = {
                ...this.currentOrder,
                driverId: parseInt(driverId),
                status: 'assigned'
            };

            await storage.update('orders', updatedOrder);
            
            // Add to assignment history and log activity
            await this.addToHistory('assign', this.currentOrder.id, driverId);
            this.logAssignmentActivity('assign', this.currentOrder.id, driverId, {
                trackingNumber: this.currentOrder.trackingNumber,
                customerName: this.currentOrder.customerName,
                city: this.currentOrder.city,
                price: this.currentOrder.deliveryPrice
            });
            
            // Update current order reference
            this.currentOrder = updatedOrder;
            
            // Refresh display
            this.displayOrderDetails(updatedOrder);
            
            // Show success message with details
            const driver = this.drivers.find(d => d.id == driverId);
            const assignmentDetails = {
                orderId: this.currentOrder.id,
                trackingNumber: this.currentOrder.trackingNumber,
                driverName: driver?.name || 'غير معروف',
                customerName: this.currentOrder.customerName,
                city: this.currentOrder.city,
                price: this.currentOrder.deliveryPrice
            };

            this.showAssignmentSuccessNotification(assignmentDetails, 'assign');

            // Refresh other components if they exist
            if (window.ordersManager) {
                window.ordersManager.loadOrders();
            }
            if (window.driversManager) {
                window.driversManager.loadDrivers();
            }

        } catch (error) {
            console.error('Error assigning order:', error);
            this.showError('خطأ في إسناد الطلب');
        }
    }

    async unassignOrder() {
        if (!this.currentOrder) {
            this.showError('يرجى البحث عن طلب أولاً');
            return;
        }

        // Validate unassignment
        const validationResult = await this.validateUnassignment(this.currentOrder);
        if (!validationResult.isValid) {
            this.showError(validationResult.message);
            return;
        }

        try {
            // Update order to remove driver
            const updatedOrder = {
                ...this.currentOrder,
                driverId: null,
                status: 'pending'
            };

            await storage.update('orders', updatedOrder);
            
            // Add to assignment history and log activity
            await this.addToHistory('unassign', this.currentOrder.id, this.currentOrder.driverId);
            this.logAssignmentActivity('unassign', this.currentOrder.id, this.currentOrder.driverId, {
                trackingNumber: this.currentOrder.trackingNumber,
                customerName: this.currentOrder.customerName,
                city: this.currentOrder.city,
                price: this.currentOrder.deliveryPrice
            });
            
            // Update current order reference
            this.currentOrder = updatedOrder;
            
            // Refresh display
            this.displayOrderDetails(updatedOrder);
            
            // Show success message with details
            const unassignDetails = {
                orderId: this.currentOrder.id,
                trackingNumber: this.currentOrder.trackingNumber,
                customerName: this.currentOrder.customerName,
                city: this.currentOrder.city,
                price: this.currentOrder.deliveryPrice
            };

            this.showAssignmentSuccessNotification(unassignDetails, 'unassign');

            // Refresh other components
            if (window.ordersManager) {
                window.ordersManager.loadOrders();
            }
            if (window.driversManager) {
                window.driversManager.loadDrivers();
            }

        } catch (error) {
            console.error('Error unassigning order:', error);
            this.showError('خطأ في إلغاء إسناد الطلب');
        }
    }

    clearForm() {
        const trackingInput = document.getElementById('assignmentTrackingInput');
        const driverSelect = document.getElementById('assignmentDriverSelect');
        
        if (trackingInput) trackingInput.value = '';
        if (driverSelect) driverSelect.value = '';
        
        this.clearOrderDetails();
        this.hideError();
    }

    showError(message) {
        const errorDiv = document.getElementById('trackingSearchError');
        if (errorDiv) {
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
        }
    }

    hideError() {
        const errorDiv = document.getElementById('trackingSearchError');
        if (errorDiv) {
            errorDiv.style.display = 'none';
        }
    }

    async addToHistory(action, orderId, driverId) {
        try {
            const driver = this.drivers.find(d => d.id == driverId);
            const historyItem = {
                id: Date.now(),
                action: action,
                orderId: orderId,
                driverName: driver?.name || 'غير معروف',
                timestamp: new Date().toISOString(),
                date: new Date().toLocaleString('ar-IQ')
            };

            this.assignmentHistory.unshift(historyItem);

            // Keep only last 10 items
            if (this.assignmentHistory.length > 10) {
                this.assignmentHistory = this.assignmentHistory.slice(0, 10);
            }

            // Save to localStorage
            localStorage.setItem('assignmentHistory', JSON.stringify(this.assignmentHistory));

            // Update display
            this.displayAssignmentHistory();
        } catch (error) {
            console.error('Error adding to history:', error);
        }
    }

    loadAssignmentHistory() {
        try {
            const saved = localStorage.getItem('assignmentHistory');
            if (saved) {
                this.assignmentHistory = JSON.parse(saved);
            }
            this.displayAssignmentHistory();
        } catch (error) {
            console.error('Error loading assignment history:', error);
            this.assignmentHistory = [];
        }
    }

    displayAssignmentHistory() {
        const historyContainer = document.getElementById('assignmentHistory');
        if (!historyContainer) return;

        if (this.assignmentHistory.length === 0) {
            historyContainer.innerHTML = '<p class="no-history">لا توجد عمليات إسناد حديثة</p>';
            return;
        }

        historyContainer.innerHTML = this.assignmentHistory.map(item => `
            <div class="history-item">
                <div class="history-header">
                    <span class="history-action">
                        ${item.action === 'assign' ? 'إسناد' : 'إلغاء إسناد'} الطلب #${item.orderId}
                    </span>
                    <span class="history-time">${item.date}</span>
                </div>
                <div class="history-details">
                    ${item.action === 'assign' ? 'للمندوب:' : 'من المندوب:'} ${item.driverName}
                </div>
            </div>
        `).join('');
    }

    async showUnassignedOrders() {
        try {
            const orders = await storage.getAll('orders');
            const unassignedOrders = orders.filter(order =>
                !order.driverId &&
                (order.status === 'pending' || order.status === 'assigned')
            );

            if (unassignedOrders.length === 0) {
                window.app.showNotification('لا توجد طلبات غير مُسندة', 'info');
                return;
            }

            // Create modal content
            const modalContent = `
                <div class="modal-content">
                    <div class="modal-header">
                        <h3 class="modal-title">الطلبات غير المُسندة (${unassignedOrders.length})</h3>
                        <button class="modal-close">&times;</button>
                    </div>
                    <div class="modal-body">
                        <div class="table-container">
                            <table class="data-table">
                                <thead>
                                    <tr>
                                        <th>رقم الطلب</th>
                                        <th>رقم الوصل</th>
                                        <th>العميل</th>
                                        <th>الزبون</th>
                                        <th>المحافظة</th>
                                        <th>السعر</th>
                                        <th>التاريخ</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${unassignedOrders.map(order => `
                                        <tr>
                                            <td>#${order.id}</td>
                                            <td>
                                                ${order.trackingNumber ?
                                                    `<span class="tracking-number">${order.trackingNumber}</span>` :
                                                    '<span class="no-tracking">-</span>'
                                                }
                                            </td>
                                            <td>${order.clientName}</td>
                                            <td>${order.customerName}</td>
                                            <td>${order.city}</td>
                                            <td>${order.deliveryPrice} د.ع</td>
                                            <td>${this.formatDate(order.date)}</td>
                                            <td>
                                                <button class="btn-primary btn-sm" onclick="window.assignmentManager && window.assignmentManager.quickAssign(${order.id})">
                                                    <i class="fas fa-user-plus"></i>
                                                    إسناد سريع
                                                </button>
                                            </td>
                                        </tr>
                                    `).join('')}
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn-secondary modal-close">إغلاق</button>
                        <button type="button" class="btn-primary" onclick="assignmentManager.showBulkAssignmentModal()">
                            <i class="fas fa-list"></i>
                            إسناد متعدد
                        </button>
                    </div>
                </div>
            `;

            window.app.showModal(modalContent);
        } catch (error) {
            console.error('Error showing unassigned orders:', error);
            window.app.showNotification('خطأ في عرض الطلبات غير المُسندة', 'error');
        }
    }

    async quickAssign(orderId) {
        try {
            const order = await storage.getById('orders', orderId);
            if (!order) {
                window.app.showNotification('الطلب غير موجود', 'error');
                return;
            }

            // Fill the tracking input with order's tracking number
            const trackingInput = document.getElementById('assignmentTrackingInput');
            if (trackingInput && order.trackingNumber) {
                trackingInput.value = order.trackingNumber;
            }

            // Close modal and switch to assignment section
            window.app.hideModal();
            window.app.showSection('assignment');

            // Search for the order
            await this.searchByTracking(order.trackingNumber || '');

            window.app.showNotification('تم تحميل الطلب، يرجى اختيار المندوب', 'info');
        } catch (error) {
            console.error('Error in quick assign:', error);
            window.app.showNotification('خطأ في الإسناد السريع', 'error');
        }
    }

    async showBulkAssignmentModal() {
        try {
            const orders = await storage.getAll('orders');
            const unassignedOrders = orders.filter(order =>
                !order.driverId &&
                (order.status === 'pending' || order.status === 'assigned')
            );

            if (unassignedOrders.length === 0) {
                window.app.showNotification('لا توجد طلبات غير مُسندة للإسناد المتعدد', 'info');
                return;
            }

            const modalContent = `
                <div class="modal-content bulk-assignment-modal">
                    <div class="modal-header">
                        <h3 class="modal-title">إسناد متعدد للطلبات</h3>
                        <button class="modal-close">&times;</button>
                    </div>
                    <div class="modal-body">
                        <div class="bulk-assignment-container">
                            <div class="assignment-controls">
                                <div class="form-group">
                                    <label class="form-label">اختيار المندوب للإسناد المتعدد:</label>
                                    <select class="form-select" id="bulkDriverSelect">
                                        <option value="">اختر المندوب</option>
                                        ${this.drivers.map(driver =>
                                            `<option value="${driver.id}">${driver.name} - ${driver.city}</option>`
                                        ).join('')}
                                    </select>
                                </div>
                                <div class="bulk-actions">
                                    <button class="btn-secondary" onclick="window.assignmentManager && window.assignmentManager.selectAllOrders()">
                                        <i class="fas fa-check-square"></i>
                                        تحديد الكل
                                    </button>
                                    <button class="btn-secondary" onclick="window.assignmentManager && window.assignmentManager.deselectAllOrders()">
                                        <i class="fas fa-square"></i>
                                        إلغاء التحديد
                                    </button>
                                    <button class="btn-primary" onclick="window.assignmentManager && window.assignmentManager.bulkAssignSelected()">
                                        <i class="fas fa-user-check"></i>
                                        إسناد المحدد
                                    </button>
                                </div>
                            </div>

                            <div class="orders-selection-container">
                                <h4>الطلبات غير المُسندة (${unassignedOrders.length})</h4>
                                <div class="table-container">
                                    <table class="data-table">
                                        <thead>
                                            <tr>
                                                <th>
                                                    <input type="checkbox" id="selectAllCheckbox" onchange="window.assignmentManager && window.assignmentManager.toggleSelectAll()">
                                                </th>
                                                <th>رقم الطلب</th>
                                                <th>رقم الوصل</th>
                                                <th>العميل</th>
                                                <th>الزبون</th>
                                                <th>المحافظة</th>
                                                <th>السعر</th>
                                                <th>التاريخ</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            ${unassignedOrders.map(order => `
                                                <tr>
                                                    <td>
                                                        <input type="checkbox" class="order-checkbox" value="${order.id}">
                                                    </td>
                                                    <td>#${order.id}</td>
                                                    <td>
                                                        ${order.trackingNumber ?
                                                            `<span class="tracking-number">${order.trackingNumber}</span>` :
                                                            '<span class="no-tracking">-</span>'
                                                        }
                                                    </td>
                                                    <td>${order.clientName}</td>
                                                    <td>${order.customerName}</td>
                                                    <td>${order.city}</td>
                                                    <td>${order.deliveryPrice} د.ع</td>
                                                    <td>${this.formatDate(order.date)}</td>
                                                </tr>
                                            `).join('')}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn-secondary modal-close">إغلاق</button>
                        <span class="selected-count">لم يتم تحديد أي طلب</span>
                    </div>
                </div>
            `;

            window.app.showModal(modalContent);
            this.setupBulkAssignmentListeners();

        } catch (error) {
            console.error('Error showing bulk assignment modal:', error);
            window.app.showNotification('خطأ في عرض واجهة الإسناد المتعدد', 'error');
        }
    }

    setupBulkAssignmentListeners() {
        // Update selected count when checkboxes change
        const checkboxes = document.querySelectorAll('.order-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.addEventListener('change', () => {
                this.updateSelectedCount();
            });
        });

        // Initial count update
        this.updateSelectedCount();
    }

    toggleSelectAll() {
        const selectAllCheckbox = document.getElementById('selectAllCheckbox');
        const orderCheckboxes = document.querySelectorAll('.order-checkbox');

        orderCheckboxes.forEach(checkbox => {
            checkbox.checked = selectAllCheckbox.checked;
        });

        this.updateSelectedCount();
    }

    selectAllOrders() {
        const orderCheckboxes = document.querySelectorAll('.order-checkbox');
        const selectAllCheckbox = document.getElementById('selectAllCheckbox');

        orderCheckboxes.forEach(checkbox => {
            checkbox.checked = true;
        });

        if (selectAllCheckbox) {
            selectAllCheckbox.checked = true;
        }

        this.updateSelectedCount();
    }

    deselectAllOrders() {
        const orderCheckboxes = document.querySelectorAll('.order-checkbox');
        const selectAllCheckbox = document.getElementById('selectAllCheckbox');

        orderCheckboxes.forEach(checkbox => {
            checkbox.checked = false;
        });

        if (selectAllCheckbox) {
            selectAllCheckbox.checked = false;
        }

        this.updateSelectedCount();
    }

    updateSelectedCount() {
        const selectedCheckboxes = document.querySelectorAll('.order-checkbox:checked');
        const countElement = document.querySelector('.selected-count');

        if (countElement) {
            const count = selectedCheckboxes.length;
            if (count === 0) {
                countElement.textContent = 'لم يتم تحديد أي طلب';
                countElement.style.color = 'var(--text-secondary)';
            } else {
                countElement.textContent = `تم تحديد ${count} طلب`;
                countElement.style.color = 'var(--primary-color)';
            }
        }
    }

    async bulkAssignSelected() {
        const selectedCheckboxes = document.querySelectorAll('.order-checkbox:checked');
        const driverSelect = document.getElementById('bulkDriverSelect');

        if (selectedCheckboxes.length === 0) {
            window.app.showNotification('يرجى تحديد طلب واحد على الأقل', 'error');
            return;
        }

        if (!driverSelect?.value) {
            window.app.showNotification('يرجى اختيار مندوب', 'error');
            return;
        }

        const driverId = driverSelect.value;
        const orderIds = Array.from(selectedCheckboxes).map(cb => parseInt(cb.value));

        try {
            let successCount = 0;
            let errorCount = 0;
            const errors = [];

            for (const orderId of orderIds) {
                try {
                    const order = await storage.getById('orders', orderId);
                    if (!order) {
                        errors.push(`الطلب #${orderId} غير موجود`);
                        errorCount++;
                        continue;
                    }

                    // Validate assignment
                    const validationResult = await this.validateAssignment(order, driverId);
                    if (!validationResult.isValid) {
                        errors.push(`الطلب #${orderId}: ${validationResult.message}`);
                        errorCount++;
                        continue;
                    }

                    // Update order
                    const updatedOrder = {
                        ...order,
                        driverId: parseInt(driverId),
                        status: 'assigned'
                    };

                    await storage.update('orders', updatedOrder);
                    await this.addToHistory('assign', orderId, driverId);
                    successCount++;

                } catch (error) {
                    console.error(`Error assigning order ${orderId}:`, error);
                    errors.push(`الطلب #${orderId}: خطأ في الإسناد`);
                    errorCount++;
                }
            }

            // Show results
            if (successCount > 0) {
                const driver = this.drivers.find(d => d.id == driverId);
                window.app.showNotification(
                    `تم إسناد ${successCount} طلب بنجاح للمندوب ${driver?.name || 'غير معروف'}`,
                    'success'
                );
            }

            if (errorCount > 0) {
                console.warn('Bulk assignment errors:', errors);
                window.app.showNotification(
                    `فشل في إسناد ${errorCount} طلب. تحقق من وحدة التحكم للتفاصيل`,
                    'warning'
                );
            }

            // Close modal and refresh
            window.app.hideModal();

            // Refresh other components
            if (window.ordersManager) {
                window.ordersManager.loadOrders();
            }
            if (window.driversManager) {
                window.driversManager.loadDrivers();
            }

        } catch (error) {
            console.error('Error in bulk assignment:', error);
            window.app.showNotification('خطأ في الإسناد المتعدد', 'error');
        }
    }

    async validateAssignment(order, driverId) {
        try {
            // Check if order exists and is valid
            if (!order || !order.id) {
                return { isValid: false, message: 'الطلب غير صحيح' };
            }

            // Check if driver exists
            const driver = this.drivers.find(d => d.id == driverId);
            if (!driver) {
                return { isValid: false, message: 'المندوب غير موجود' };
            }

            // Check order status
            const allowedStatuses = ['pending', 'assigned'];
            if (!allowedStatuses.includes(order.status)) {
                return { isValid: false, message: 'لا يمكن إسناد الطلبات المسلمة أو المرجعة' };
            }

            // Check if driver is active (if driver has active status)
            if (driver.status && driver.status !== 'active') {
                return { isValid: false, message: 'المندوب غير نشط حالياً' };
            }

            // Check if driver is in the same city (optional warning)
            if (driver.city && order.city && driver.city !== order.city) {
                // This is just a warning, not a blocking validation
                console.warn(`Driver ${driver.name} is in ${driver.city} but order is for ${order.city}`);
            }

            // Check if order is already assigned to the same driver
            if (order.driverId == driverId) {
                return { isValid: false, message: 'الطلب مُسند بالفعل لهذا المندوب' };
            }

            // Get current orders for this driver to check workload (optional)
            const allOrders = await storage.getAll('orders');
            const driverActiveOrders = allOrders.filter(o =>
                o.driverId == driverId &&
                (o.status === 'assigned' || o.status === 'pending')
            );

            // Warning if driver has many active orders (not blocking)
            if (driverActiveOrders.length >= 10) {
                console.warn(`Driver ${driver.name} has ${driverActiveOrders.length} active orders`);
            }

            return { isValid: true, message: 'التحقق تم بنجاح' };

        } catch (error) {
            console.error('Error validating assignment:', error);
            return { isValid: false, message: 'خطأ في التحقق من صحة الإسناد' };
        }
    }

    async validateUnassignment(order) {
        try {
            // Check if order exists and is valid
            if (!order || !order.id) {
                return { isValid: false, message: 'الطلب غير صحيح' };
            }

            // Check if order is actually assigned
            if (!order.driverId) {
                return { isValid: false, message: 'الطلب غير مُسند لأي مندوب' };
            }

            // Check order status
            const allowedStatuses = ['pending', 'assigned'];
            if (!allowedStatuses.includes(order.status)) {
                return { isValid: false, message: 'لا يمكن إلغاء إسناد الطلبات المسلمة أو المرجعة' };
            }

            return { isValid: true, message: 'التحقق تم بنجاح' };

        } catch (error) {
            console.error('Error validating unassignment:', error);
            return { isValid: false, message: 'خطأ في التحقق من صحة إلغاء الإسناد' };
        }
    }

    async checkDriverAvailability(driverId) {
        try {
            const driver = this.drivers.find(d => d.id == driverId);
            if (!driver) {
                return { available: false, message: 'المندوب غير موجود' };
            }

            // Check if driver is active
            if (driver.status && driver.status !== 'active') {
                return { available: false, message: 'المندوب غير نشط' };
            }

            // Get driver's current workload
            const allOrders = await storage.getAll('orders');
            const activeOrders = allOrders.filter(o =>
                o.driverId == driverId &&
                (o.status === 'assigned' || o.status === 'pending')
            );

            return {
                available: true,
                activeOrders: activeOrders.length,
                message: `المندوب متاح (${activeOrders.length} طلب نشط)`
            };

        } catch (error) {
            console.error('Error checking driver availability:', error);
            return { available: false, message: 'خطأ في التحقق من توفر المندوب' };
        }
    }

    formatDate(dateString) {
        return new Date(dateString).toLocaleDateString('ar-IQ');
    }

    showAssignmentSuccessNotification(details, action) {
        const actionText = action === 'assign' ? 'إسناد' : 'إلغاء إسناد';
        const message = action === 'assign'
            ? `تم ${actionText} الطلب #${details.orderId} للمندوب ${details.driverName}`
            : `تم ${actionText} الطلب #${details.orderId}`;

        // Create detailed notification
        const notificationContent = `
            <div class="assignment-notification">
                <div class="notification-header">
                    <i class="fas fa-check-circle"></i>
                    <strong>${message}</strong>
                </div>
                <div class="notification-details">
                    ${details.trackingNumber ? `<div>رقم الوصل: ${details.trackingNumber}</div>` : ''}
                    <div>الزبون: ${details.customerName}</div>
                    <div>المحافظة: ${details.city}</div>
                    <div>السعر: ${details.price} د.ع</div>
                </div>
            </div>
        `;

        // Show notification with custom content
        this.showCustomNotification(notificationContent, 'success', 4000);
    }

    showCustomNotification(content, type = 'info', duration = 3000) {
        const notification = document.createElement('div');
        notification.className = `custom-notification notification-${type}`;
        notification.innerHTML = content;

        // Add to page
        document.body.appendChild(notification);

        // Animate in
        setTimeout(() => {
            notification.classList.add('show');
        }, 100);

        // Remove after duration
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => {
                if (notification.parentNode) {
                    document.body.removeChild(notification);
                }
            }, 300);
        }, duration);
    }

    logAssignmentActivity(action, orderId, driverId, details = {}) {
        try {
            const activity = {
                id: Date.now(),
                action: action,
                orderId: orderId,
                driverId: driverId,
                details: details,
                timestamp: new Date().toISOString(),
                userAgent: navigator.userAgent,
                sessionId: this.getSessionId()
            };

            // Get existing logs
            let logs = JSON.parse(localStorage.getItem('assignmentLogs') || '[]');

            // Add new log
            logs.unshift(activity);

            // Keep only last 100 logs
            if (logs.length > 100) {
                logs = logs.slice(0, 100);
            }

            // Save back to localStorage
            localStorage.setItem('assignmentLogs', JSON.stringify(logs));

            console.log('Assignment activity logged:', activity);
        } catch (error) {
            console.error('Error logging assignment activity:', error);
        }
    }

    getSessionId() {
        let sessionId = sessionStorage.getItem('assignmentSessionId');
        if (!sessionId) {
            sessionId = 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
            sessionStorage.setItem('assignmentSessionId', sessionId);
        }
        return sessionId;
    }

    getAssignmentLogs(limit = 50) {
        try {
            const logs = JSON.parse(localStorage.getItem('assignmentLogs') || '[]');
            return logs.slice(0, limit);
        } catch (error) {
            console.error('Error getting assignment logs:', error);
            return [];
        }
    }

    exportAssignmentLogs() {
        try {
            const logs = this.getAssignmentLogs();
            const csvContent = this.convertLogsToCSV(logs);

            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', `assignment_logs_${new Date().toISOString().split('T')[0]}.csv`);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            window.app.showNotification('تم تصدير سجل العمليات بنجاح', 'success');
        } catch (error) {
            console.error('Error exporting logs:', error);
            window.app.showNotification('خطأ في تصدير السجل', 'error');
        }
    }

    convertLogsToCSV(logs) {
        const headers = ['التاريخ والوقت', 'العملية', 'رقم الطلب', 'رقم المندوب', 'التفاصيل'];
        let csv = headers.join(',') + '\n';

        logs.forEach(log => {
            const row = [
                `"${new Date(log.timestamp).toLocaleString('ar-IQ')}"`,
                `"${log.action === 'assign' ? 'إسناد' : 'إلغاء إسناد'}"`,
                log.orderId,
                log.driverId || '-',
                `"${JSON.stringify(log.details)}"`
            ];
            csv += row.join(',') + '\n';
        });

        return csv;
    }

    // Multiple Assignment Functions
    async validateMultipleTracking() {
        const input = document.getElementById('multipleTrackingInput');
        const errorDiv = document.getElementById('multipleTrackingError');
        const displayDiv = document.getElementById('multipleOrdersDisplay');
        const assignBtn = document.getElementById('multipleAssignBtn');

        if (!input?.value?.trim()) {
            this.showMultipleError('يرجى إدخال أرقام الوصل');
            return;
        }

        try {
            // Parse tracking numbers
            const trackingNumbers = this.parseTrackingNumbers(input.value);

            if (trackingNumbers.length === 0) {
                this.showMultipleError('لم يتم العثور على أرقام وصل صحيحة');
                return;
            }

            // Validate each tracking number
            const validationResults = await this.validateTrackingNumbers(trackingNumbers);

            // Display results
            this.displayMultipleOrdersResults(validationResults);

            // Enable/disable assign button
            const validOrders = validationResults.filter(r => r.isValid && !r.isAssigned);
            assignBtn.disabled = validOrders.length === 0;

            this.hideMultipleError();
            displayDiv.style.display = 'block';

        } catch (error) {
            console.error('Error validating multiple tracking:', error);
            this.showMultipleError('خطأ في التحقق من أرقام الوصل');
        }
    }

    parseTrackingNumbers(input) {
        // Split by commas and newlines, then clean up
        return input
            .split(/[,\n\r]+/)
            .map(num => num.trim())
            .filter(num => num.length > 0)
            .filter((num, index, arr) => arr.indexOf(num) === index); // Remove duplicates
    }

    async validateTrackingNumbers(trackingNumbers) {
        const orders = await storage.getAll('orders');
        const results = [];

        for (const trackingNumber of trackingNumbers) {
            const order = orders.find(o =>
                o.trackingNumber &&
                o.trackingNumber.toLowerCase() === trackingNumber.toLowerCase()
            );

            if (!order) {
                results.push({
                    trackingNumber,
                    isValid: false,
                    isAssigned: false,
                    error: 'رقم الوصل غير موجود',
                    order: null
                });
            } else if (order.status === 'delivered' || order.status === 'returned') {
                results.push({
                    trackingNumber,
                    isValid: false,
                    isAssigned: false,
                    error: 'الطلب مُسلم أو مُرجع',
                    order
                });
            } else if (order.driverId) {
                const driver = this.drivers.find(d => d.id == order.driverId);
                results.push({
                    trackingNumber,
                    isValid: true,
                    isAssigned: true,
                    error: `مُسند للمندوب: ${driver?.name || 'غير معروف'}`,
                    order
                });
            } else {
                results.push({
                    trackingNumber,
                    isValid: true,
                    isAssigned: false,
                    error: null,
                    order
                });
            }
        }

        this.validationResults = results;
        return results;
    }

    displayMultipleOrdersResults(results) {
        const listContainer = document.getElementById('multipleOrdersList');
        if (!listContainer) return;

        const validCount = results.filter(r => r.isValid && !r.isAssigned).length;
        const invalidCount = results.filter(r => !r.isValid).length;
        const assignedCount = results.filter(r => r.isAssigned).length;

        listContainer.innerHTML = `
            <div class="multiple-assignment-summary">
                <div class="summary-stat valid">
                    <div class="summary-stat-value">${validCount}</div>
                    <div class="summary-stat-label">جاهز للإسناد</div>
                </div>
                <div class="summary-stat invalid">
                    <div class="summary-stat-value">${invalidCount}</div>
                    <div class="summary-stat-label">غير صحيح</div>
                </div>
                <div class="summary-stat assigned">
                    <div class="summary-stat-value">${assignedCount}</div>
                    <div class="summary-stat-label">مُسند مسبقاً</div>
                </div>
            </div>

            <div class="multiple-orders-items">
                ${results.map(result => `
                    <div class="multiple-order-item">
                        <div class="order-item-info">
                            <div class="order-item-tracking">${result.trackingNumber}</div>
                            ${result.order ? `
                                <div class="order-item-details">
                                    الطلب #${result.order.id} - ${result.order.clientName} - ${result.order.customerName}
                                </div>
                            ` : ''}
                        </div>
                        <div class="order-item-status ${result.isValid ? (result.isAssigned ? 'assigned' : 'valid') : 'invalid'}">
                            ${result.error || 'جاهز للإسناد'}
                        </div>
                    </div>
                `).join('')}
            </div>
        `;
    }

    async assignMultipleOrders() {
        const driverSelect = document.getElementById('multipleDriverSelect');
        const driverId = driverSelect?.value;

        if (!driverId) {
            this.showMultipleError('يرجى اختيار مندوب');
            return;
        }

        if (!this.validationResults || this.validationResults.length === 0) {
            this.showMultipleError('يرجى التحقق من أرقام الوصل أولاً');
            return;
        }

        const validOrders = this.validationResults.filter(r => r.isValid && !r.isAssigned);

        if (validOrders.length === 0) {
            this.showMultipleError('لا توجد طلبات صالحة للإسناد');
            return;
        }

        try {
            let successCount = 0;
            let errorCount = 0;
            const errors = [];

            for (const result of validOrders) {
                try {
                    // Validate assignment
                    const validationResult = await this.validateAssignment(result.order, driverId);
                    if (!validationResult.isValid) {
                        errors.push(`${result.trackingNumber}: ${validationResult.message}`);
                        errorCount++;
                        continue;
                    }

                    // Update order
                    const updatedOrder = {
                        ...result.order,
                        driverId: parseInt(driverId),
                        status: 'assigned'
                    };

                    await storage.update('orders', updatedOrder);
                    await this.addToHistory('assign', result.order.id, driverId);
                    this.logAssignmentActivity('assign', result.order.id, driverId, {
                        trackingNumber: result.trackingNumber,
                        customerName: result.order.customerName,
                        city: result.order.city,
                        price: result.order.deliveryPrice
                    });

                    successCount++;

                } catch (error) {
                    console.error(`Error assigning order ${result.trackingNumber}:`, error);
                    errors.push(`${result.trackingNumber}: خطأ في الإسناد`);
                    errorCount++;
                }
            }

            // Show detailed results
            const driver = this.drivers.find(d => d.id == driverId);
            this.showMultipleAssignmentReport(successCount, errorCount, errors, driver?.name || 'غير معروف');

            // Clear form and refresh
            this.clearMultipleForm();

            // Refresh other components
            if (window.ordersManager) {
                window.ordersManager.loadOrders();
            }
            if (window.driversManager) {
                window.driversManager.loadDrivers();
            }

        } catch (error) {
            console.error('Error in multiple assignment:', error);
            this.showMultipleError('خطأ في الإسناد المتعدد');
        }
    }

    showMultipleAssignmentReport(successCount, errorCount, errors, driverName) {
        const totalCount = successCount + errorCount;

        // Show quick notification first
        if (successCount > 0) {
            this.showMultipleAssignmentSuccess(successCount, driverName);
        }

        // Show detailed modal report
        const modalContent = `
            <div class="modal-content assignment-report-modal">
                <div class="modal-header">
                    <h3 class="modal-title">تقرير الإسناد المتعدد</h3>
                    <button class="modal-close">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="assignment-report-summary">
                        <div class="report-summary-card">
                            <div class="summary-header">
                                <h4>ملخص العملية</h4>
                                <div class="summary-date">${new Date().toLocaleString('ar-IQ')}</div>
                            </div>
                            <div class="summary-stats">
                                <div class="summary-stat success">
                                    <div class="stat-icon"><i class="fas fa-check-circle"></i></div>
                                    <div class="stat-info">
                                        <div class="stat-value">${successCount}</div>
                                        <div class="stat-label">تم الإسناد بنجاح</div>
                                    </div>
                                </div>
                                <div class="summary-stat error">
                                    <div class="stat-icon"><i class="fas fa-times-circle"></i></div>
                                    <div class="stat-info">
                                        <div class="stat-value">${errorCount}</div>
                                        <div class="stat-label">فشل في الإسناد</div>
                                    </div>
                                </div>
                                <div class="summary-stat total">
                                    <div class="stat-icon"><i class="fas fa-list"></i></div>
                                    <div class="stat-info">
                                        <div class="stat-value">${totalCount}</div>
                                        <div class="stat-label">إجمالي الطلبات</div>
                                    </div>
                                </div>
                            </div>
                            <div class="summary-driver">
                                <strong>المندوب المُسند إليه:</strong> ${driverName}
                            </div>
                        </div>
                    </div>

                    ${errorCount > 0 ? `
                        <div class="assignment-errors">
                            <h4>تفاصيل الأخطاء</h4>
                            <div class="errors-list">
                                ${errors.map(error => `
                                    <div class="error-item">
                                        <i class="fas fa-exclamation-triangle"></i>
                                        <span>${error}</span>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    ` : ''}

                    <div class="assignment-recommendations">
                        <h4>التوصيات</h4>
                        <ul>
                            ${successCount > 0 ? '<li>تم إسناد الطلبات الصحيحة بنجاح</li>' : ''}
                            ${errorCount > 0 ? '<li>راجع الأخطاء أعلاه وصحح أرقام الوصل</li>' : ''}
                            <li>تحقق من حالة الطلبات في قسم إدارة الطلبات</li>
                            <li>يمكنك طباعة هذا التقرير للمراجعة</li>
                        </ul>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn-secondary modal-close">إغلاق</button>
                    <button type="button" class="btn-primary" onclick="window.assignmentManager && window.assignmentManager.exportAssignmentReport(${successCount}, ${errorCount}, '${driverName}')">
                        <i class="fas fa-download"></i>
                        تصدير التقرير
                    </button>
                    <button type="button" class="btn-success" onclick="window.print()">
                        <i class="fas fa-print"></i>
                        طباعة
                    </button>
                </div>
            </div>
        `;

        window.app.showModal(modalContent);
    }

    showMultipleAssignmentSuccess(count, driverName) {
        const notificationContent = `
            <div class="assignment-notification">
                <div class="notification-header">
                    <i class="fas fa-check-circle"></i>
                    <strong>تم إسناد ${count} طلب بنجاح</strong>
                </div>
                <div class="notification-details">
                    <div>المندوب: ${driverName}</div>
                    <div>عدد الطلبات: ${count}</div>
                </div>
            </div>
        `;

        this.showCustomNotification(notificationContent, 'success', 4000);
    }

    exportAssignmentReport(successCount, errorCount, driverName) {
        try {
            const reportData = {
                date: new Date().toLocaleString('ar-IQ'),
                driverName: driverName,
                successCount: successCount,
                errorCount: errorCount,
                totalCount: successCount + errorCount,
                successRate: Math.round((successCount / (successCount + errorCount)) * 100)
            };

            const csvContent = `تقرير الإسناد المتعدد
التاريخ والوقت,${reportData.date}
المندوب,${reportData.driverName}
عدد الطلبات المُسندة بنجاح,${reportData.successCount}
عدد الطلبات الفاشلة,${reportData.errorCount}
إجمالي الطلبات,${reportData.totalCount}
معدل النجاح,${reportData.successRate}%

تم إنشاء هذا التقرير بواسطة نظام إدارة شركة التوصيل
`;

            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', `تقرير-الإسناد-المتعدد-${new Date().toISOString().split('T')[0]}.csv`);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            window.app.showNotification('تم تصدير التقرير بنجاح', 'success');
        } catch (error) {
            console.error('Error exporting assignment report:', error);
            window.app.showNotification('خطأ في تصدير التقرير', 'error');
        }
    }

    clearMultipleForm() {
        const input = document.getElementById('multipleTrackingInput');
        const driverSelect = document.getElementById('multipleDriverSelect');
        const displayDiv = document.getElementById('multipleOrdersDisplay');
        const assignBtn = document.getElementById('multipleAssignBtn');

        if (input) input.value = '';
        if (driverSelect) driverSelect.value = '';
        if (displayDiv) displayDiv.style.display = 'none';
        if (assignBtn) assignBtn.disabled = true;

        this.hideMultipleError();
        this.validationResults = {};
    }

    showMultipleError(message) {
        const errorDiv = document.getElementById('multipleTrackingError');
        if (errorDiv) {
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
        }
    }

    hideMultipleError() {
        const errorDiv = document.getElementById('multipleTrackingError');
        if (errorDiv) {
            errorDiv.style.display = 'none';
        }
    }

    getStatusText(status) {
        const statusMap = {
            'pending': 'معلق',
            'assigned': 'مُسند',
            'delivered': 'مسلم',
            'returned': 'راجع',
            'partial_return': 'راجع جزئي'
        };
        return statusMap[status] || status;
    }
}

// Initialize assignment manager when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    // Wait for app to be ready
    const initAssignmentManager = () => {
        if (window.app && window.storage) {
            const assignmentManager = new AssignmentManager();
            window.assignmentManager = assignmentManager;
            console.log('Assignment Manager initialized');
        } else {
            setTimeout(initAssignmentManager, 100);
        }
    };

    initAssignmentManager();
});
