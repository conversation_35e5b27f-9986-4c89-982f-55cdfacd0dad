// Reports Management System
class ReportsManager {
    constructor() {
        this.currentReport = null;
        this.init();
    }

    async init() {
        this.setupEventListeners();
        await this.initializeReports();
    }

    setupEventListeners() {
        // Generate report button
        const generateBtn = document.getElementById('generateReport');
        if (generateBtn) {
            generateBtn.addEventListener('click', () => this.generateReport());
        }

        // Export Excel button
        const exportBtn = document.getElementById('exportExcel');
        if (exportBtn) {
            exportBtn.addEventListener('click', () => this.exportToExcel());
        }

        // Print report button
        const printBtn = document.getElementById('printReport');
        if (printBtn) {
            printBtn.addEventListener('click', () => this.printReport());
        }

        // Report type change
        const reportType = document.getElementById('reportType');
        if (reportType) {
            reportType.addEventListener('change', () => this.onReportTypeChange());
        }
    }

    async initializeReports() {
        // Set default date range (current month)
        const now = new Date();
        const firstDay = new Date(now.getFullYear(), now.getMonth(), 1);
        const lastDay = new Date(now.getFullYear(), now.getMonth() + 1, 0);

        const fromDateInput = document.getElementById('reportFromDate');
        const toDateInput = document.getElementById('reportToDate');

        if (fromDateInput) {
            fromDateInput.value = firstDay.toISOString().split('T')[0];
        }
        if (toDateInput) {
            toDateInput.value = lastDay.toISOString().split('T')[0];
        }

        // Generate initial report
        await this.generateReport();
    }

    onReportTypeChange() {
        const reportType = document.getElementById('reportType').value;
        // Add specific filters based on report type if needed
        // For now, we'll keep it simple
    }

    async generateReport() {
        const reportType = document.getElementById('reportType').value;
        const fromDate = document.getElementById('reportFromDate').value;
        const toDate = document.getElementById('reportToDate').value;

        if (!fromDate || !toDate) {
            window.app.showNotification('يرجى تحديد تاريخ البداية والنهاية', 'error');
            return;
        }

        try {
            const orders = await this.getOrdersInDateRange(fromDate, toDate);
            const drivers = await storage.getAll('drivers');
            const customers = await storage.getAll('customers');

            let reportContent = '';

            switch (reportType) {
                case 'general':
                    reportContent = this.generateGeneralReport(orders, drivers);
                    break;
                case 'company':
                    reportContent = this.generateCompanyReport(orders);
                    break;
                case 'driver':
                    reportContent = this.generateDriverReport(orders, drivers);
                    break;
                case 'customer':
                    reportContent = this.generateCustomerReport(orders, customers);
                    break;
                default:
                    reportContent = this.generateGeneralReport(orders, drivers);
            }

            this.displayReport(reportContent);
            this.currentReport = { type: reportType, orders, drivers, customers, fromDate, toDate };

        } catch (error) {
            console.error('Error generating report:', error);
            window.app.showNotification('خطأ في إنشاء التقرير', 'error');
        }
    }

    async getOrdersInDateRange(fromDate, toDate) {
        const allOrders = await storage.getAll('orders');
        const from = new Date(fromDate);
        const to = new Date(toDate);
        to.setHours(23, 59, 59, 999); // Include the entire end date

        return allOrders.filter(order => {
            const orderDate = new Date(order.date);
            return orderDate >= from && orderDate <= to;
        });
    }

    generateGeneralReport(orders, drivers) {
        const stats = this.calculateGeneralStats(orders, drivers);

        return `
            <div class="report-header">
                <h3>التقرير العام</h3>
                <p>من ${this.formatDate(this.currentReport.fromDate)} إلى ${this.formatDate(this.currentReport.toDate)}</p>
            </div>

            <div class="report-summary">
                <div class="report-item">
                    <div class="report-value">${stats.totalOrders}</div>
                    <div class="report-label">إجمالي الطلبات</div>
                </div>
                <div class="report-item">
                    <div class="report-value">${stats.deliveredOrders}</div>
                    <div class="report-label">طلبات مسلمة</div>
                </div>
                <div class="report-item">
                    <div class="report-value">${stats.returnedOrders}</div>
                    <div class="report-label">طلبات راجعة</div>
                </div>
                <div class="report-item">
                    <div class="report-value">${stats.pendingOrders}</div>
                    <div class="report-label">طلبات معلقة</div>
                </div>
                <div class="report-item">
                    <div class="report-value">${stats.totalRevenue.toLocaleString()} د.ع</div>
                    <div class="report-label">إجمالي الإيرادات</div>
                </div>
                <div class="report-item">
                    <div class="report-value">${stats.totalCommissions.toLocaleString()} د.ع</div>
                    <div class="report-label">إجمالي العمولات</div>
                </div>
                <div class="report-item">
                    <div class="report-value">${stats.netProfit.toLocaleString()} د.ع</div>
                    <div class="report-label">صافي الربح</div>
                </div>
                <div class="report-item">
                    <div class="report-value">${stats.successRate}%</div>
                    <div class="report-label">معدل النجاح</div>
                </div>
            </div>

            <div class="report-section">
                <h4>توزيع الطلبات حسب المحافظة</h4>
                <div class="city-distribution">
                    ${Object.entries(stats.cityDistribution)
                        .sort((a, b) => b[1] - a[1])
                        .map(([city, count]) => `
                            <div class="distribution-item">
                                <span class="city-name">${city}</span>
                                <span class="city-count">${count} طلب</span>
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: ${(count / stats.totalOrders) * 100}%"></div>
                                </div>
                            </div>
                        `).join('')}
                </div>
            </div>

            <div class="report-section">
                <h4>أداء المندوبين</h4>
                <div class="table-container">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>المندوب</th>
                                <th>إجمالي الطلبات</th>
                                <th>طلبات مسلمة</th>
                                <th>معدل النجاح</th>
                                <th>العمولة المستحقة</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${stats.driverPerformance.map(driver => `
                                <tr>
                                    <td>${driver.name}</td>
                                    <td>${driver.totalOrders}</td>
                                    <td>${driver.deliveredOrders}</td>
                                    <td>${driver.successRate}%</td>
                                    <td>${driver.commission.toLocaleString()} د.ع</td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
            </div>
        `;
    }

    generateCompanyReport(orders) {
        const companies = this.groupOrdersByCompany(orders);

        return `
            <div class="report-header">
                <h3>تقرير الشركات</h3>
                <p>من ${this.formatDate(this.currentReport.fromDate)} إلى ${this.formatDate(this.currentReport.toDate)}</p>
            </div>

            <div class="report-section">
                <div class="table-container">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>اسم الشركة</th>
                                <th>عدد الطلبات</th>
                                <th>طلبات مسلمة</th>
                                <th>طلبات راجعة</th>
                                <th>طلبات معلقة</th>
                                <th>إجمالي المبلغ</th>
                                <th>معدل النجاح</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${Object.entries(companies)
                                .sort((a, b) => b[1].totalAmount - a[1].totalAmount)
                                .map(([company, data]) => `
                                    <tr>
                                        <td>${company}</td>
                                        <td>${data.totalOrders}</td>
                                        <td>${data.delivered}</td>
                                        <td>${data.returned}</td>
                                        <td>${data.pending}</td>
                                        <td>${data.totalAmount.toLocaleString()} د.ع</td>
                                        <td>${data.successRate}%</td>
                                    </tr>
                                `).join('')}
                        </tbody>
                    </table>
                </div>
            </div>
        `;
    }

    generateDriverReport(orders, drivers) {
        const driverStats = this.calculateDriverStats(orders, drivers);

        return `
            <div class="report-header">
                <h3>تقرير المندوبين</h3>
                <p>من ${this.formatDate(this.currentReport.fromDate)} إلى ${this.formatDate(this.currentReport.toDate)}</p>
            </div>

            <div class="report-section">
                <div class="table-container">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>اسم المندوب</th>
                                <th>المحافظة</th>
                                <th>إجمالي الطلبات</th>
                                <th>طلبات مسلمة</th>
                                <th>طلبات راجعة</th>
                                <th>معدل النجاح</th>
                                <th>العمولة الثابتة</th>
                                <th>العمولة المستحقة</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${driverStats.map(driver => `
                                <tr>
                                    <td>${driver.name}</td>
                                    <td>${driver.city}</td>
                                    <td>${driver.totalOrders}</td>
                                    <td>${driver.deliveredOrders}</td>
                                    <td>${driver.returnedOrders}</td>
                                    <td>${driver.successRate}%</td>
                                    <td>${driver.fixedCommission || driver.commissionRate * 100 || 1000} د.ع</td>
                                    <td>${driver.commission.toLocaleString()} د.ع</td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="report-summary">
                <div class="report-item">
                    <div class="report-value">${driverStats.reduce((sum, d) => sum + d.totalOrders, 0)}</div>
                    <div class="report-label">إجمالي الطلبات</div>
                </div>
                <div class="report-item">
                    <div class="report-value">${driverStats.reduce((sum, d) => sum + d.commission, 0).toLocaleString()} د.ع</div>
                    <div class="report-label">إجمالي العمولات</div>
                </div>
            </div>
        `;
    }

    generateCustomerReport(orders, customers) {
        const customerStats = this.calculateCustomerStats(orders, customers);

        return `
            <div class="report-header">
                <h3>تقرير العملاء</h3>
                <p>من ${this.formatDate(this.currentReport.fromDate)} إلى ${this.formatDate(this.currentReport.toDate)}</p>
            </div>

            <div class="report-section">
                <div class="table-container">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>اسم العميل</th>
                                <th>النوع</th>
                                <th>المحافظة</th>
                                <th>إجمالي الطلبات</th>
                                <th>طلبات مسلمة</th>
                                <th>طلبات راجعة</th>
                                <th>معدل النجاح</th>
                                <th>إجمالي المبلغ</th>
                                <th>الحالة</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${customerStats.map(customer => `
                                <tr>
                                    <td>${customer.name}</td>
                                    <td>
                                        <span class="type-badge type-${customer.type}">
                                            ${customer.type === 'company' ? 'شركة' : 'فرد'}
                                        </span>
                                    </td>
                                    <td>${customer.city || '-'}</td>
                                    <td>${customer.totalOrders}</td>
                                    <td>${customer.deliveredOrders}</td>
                                    <td>${customer.returnedOrders}</td>
                                    <td>${customer.successRate}%</td>
                                    <td>${customer.totalAmount.toLocaleString()} د.ع</td>
                                    <td>
                                        <span class="status-badge status-${customer.status}">
                                            ${customer.status === 'active' ? 'نشط' : 'غير نشط'}
                                        </span>
                                    </td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="report-summary">
                <div class="report-item">
                    <div class="report-value">${customers.length}</div>
                    <div class="report-label">إجمالي العملاء</div>
                </div>
                <div class="report-item">
                    <div class="report-value">${customers.filter(c => c.status === 'active').length}</div>
                    <div class="report-label">عملاء نشطون</div>
                </div>
                <div class="report-item">
                    <div class="report-value">${customerStats.reduce((sum, c) => sum + c.totalOrders, 0)}</div>
                    <div class="report-label">إجمالي الطلبات</div>
                </div>
                <div class="report-item">
                    <div class="report-value">${customerStats.reduce((sum, c) => sum + c.totalAmount, 0).toLocaleString()} د.ع</div>
                    <div class="report-label">إجمالي المبالغ</div>
                </div>
            </div>
        `;
    }

    calculateGeneralStats(orders, drivers) {
        const stats = {
            totalOrders: orders.length,
            deliveredOrders: orders.filter(o => o.status === 'delivered').length,
            returnedOrders: orders.filter(o => o.status === 'returned' || o.status === 'partial_return').length,
            pendingOrders: orders.filter(o => o.status === 'pending').length,
            totalRevenue: 0,
            totalCommissions: 0,
            netProfit: 0,
            successRate: 0,
            cityDistribution: {},
            driverPerformance: []
        };

        // Calculate revenue and commissions
        const deliveredOrders = orders.filter(o => o.status === 'delivered');
        stats.totalRevenue = deliveredOrders.reduce((sum, order) => sum + order.deliveryPrice, 0);

        // Calculate commissions
        deliveredOrders.forEach(order => {
            if (order.driverId) {
                const driver = drivers.find(d => d.id == order.driverId);
                if (driver) {
                    stats.totalCommissions += (driver.fixedCommission || driver.commissionRate * 100 || 1000);
                }
            }
        });

        stats.netProfit = stats.totalRevenue - stats.totalCommissions;
        stats.successRate = stats.totalOrders > 0 ? Math.round((stats.deliveredOrders / stats.totalOrders) * 100) : 0;

        // City distribution
        orders.forEach(order => {
            stats.cityDistribution[order.city] = (stats.cityDistribution[order.city] || 0) + 1;
        });

        // Driver performance
        stats.driverPerformance = drivers.map(driver => {
            const driverOrders = orders.filter(o => o.driverId == driver.id);
            const driverDelivered = driverOrders.filter(o => o.status === 'delivered');
            const commission = driverDelivered.length * (driver.fixedCommission || driver.commissionRate * 100 || 1000);

            return {
                name: driver.name,
                totalOrders: driverOrders.length,
                deliveredOrders: driverDelivered.length,
                successRate: driverOrders.length > 0 ? Math.round((driverDelivered.length / driverOrders.length) * 100) : 0,
                commission: Math.round(commission)
            };
        }).filter(d => d.totalOrders > 0);

        return stats;
    }

    groupOrdersByCompany(orders) {
        const companies = {};

        orders.forEach(order => {
            if (!companies[order.clientName]) {
                companies[order.clientName] = {
                    totalOrders: 0,
                    delivered: 0,
                    returned: 0,
                    pending: 0,
                    totalAmount: 0,
                    successRate: 0
                };
            }

            const company = companies[order.clientName];
            company.totalOrders++;

            switch (order.status) {
                case 'delivered':
                    company.delivered++;
                    company.totalAmount += order.deliveryPrice;
                    break;
                case 'returned':
                case 'partial_return':
                    company.returned++;
                    break;
                case 'pending':
                    company.pending++;
                    break;
            }

            company.successRate = company.totalOrders > 0 ? 
                Math.round((company.delivered / company.totalOrders) * 100) : 0;
        });

        return companies;
    }

    calculateDriverStats(orders, drivers) {
        return drivers.map(driver => {
            const driverOrders = orders.filter(o => o.driverId == driver.id);
            const deliveredOrders = driverOrders.filter(o => o.status === 'delivered');
            const returnedOrders = driverOrders.filter(o => o.status === 'returned' || o.status === 'partial_return');
            const commission = deliveredOrders.length * (driver.fixedCommission || driver.commissionRate * 100 || 1000);

            return {
                name: driver.name,
                city: driver.city,
                totalOrders: driverOrders.length,
                deliveredOrders: deliveredOrders.length,
                returnedOrders: returnedOrders.length,
                successRate: driverOrders.length > 0 ? 
                    Math.round((deliveredOrders.length / driverOrders.length) * 100) : 0,
                fixedCommission: driver.fixedCommission || driver.commissionRate * 100 || 1000,
                commission: Math.round(commission)
            };
        }).filter(d => d.totalOrders > 0);
    }

    calculateCustomerStats(orders, customers) {
        return customers.map(customer => {
            const customerOrders = orders.filter(o => o.customerId == customer.id);
            const deliveredOrders = customerOrders.filter(o => o.status === 'delivered');
            const returnedOrders = customerOrders.filter(o => o.status === 'returned' || o.status === 'partial_return');
            const totalAmount = deliveredOrders.reduce((sum, order) => sum + order.deliveryPrice, 0);

            return {
                name: customer.name,
                type: customer.type,
                city: customer.city,
                status: customer.status,
                totalOrders: customerOrders.length,
                deliveredOrders: deliveredOrders.length,
                returnedOrders: returnedOrders.length,
                successRate: customerOrders.length > 0 ?
                    Math.round((deliveredOrders.length / customerOrders.length) * 100) : 0,
                totalAmount: totalAmount
            };
        }).filter(c => c.totalOrders > 0);
    }

    displayReport(content) {
        const reportContent = document.getElementById('reportContent');
        if (reportContent) {
            reportContent.innerHTML = content;
        }
    }

    exportToExcel() {
        if (!this.currentReport) {
            window.app.showNotification('يرجى إنشاء تقرير أولاً', 'error');
            return;
        }

        // Create CSV content
        let csvContent = '';
        const { orders, type } = this.currentReport;

        if (type === 'general' || type === 'company') {
            csvContent = this.generateCSVContent(orders);
        }

        // Download CSV file
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', `تقرير-${type}-${new Date().toISOString().split('T')[0]}.csv`);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        window.app.showNotification('تم تصدير التقرير بنجاح', 'success');
    }

    generateCSVContent(orders) {
        const headers = ['رقم الطلب', 'رقم الوصل', 'اسم العميل', 'اسم الزبون', 'المحافظة', 'سعر الطلب', 'الحالة', 'التاريخ'];
        let csv = headers.join(',') + '\n';

        orders.forEach(order => {
            const row = [
                order.id,
                `"${order.trackingNumber || '-'}"`,
                `"${order.clientName}"`,
                `"${order.customerName}"`,
                `"${order.city}"`,
                order.deliveryPrice,
                `"${this.getStatusText(order.status)}"`,
                `"${this.formatDate(order.date)}"`
            ];
            csv += row.join(',') + '\n';
        });

        return csv;
    }

    printReport() {
        if (!this.currentReport) {
            window.app.showNotification('يرجى إنشاء تقرير أولاً', 'error');
            return;
        }

        const printWindow = window.open('', '_blank');
        const reportContent = document.getElementById('reportContent').innerHTML;

        printWindow.document.write(`
            <!DOCTYPE html>
            <html dir="rtl" lang="ar">
            <head>
                <meta charset="UTF-8">
                <title>تقرير نظام التوصيل</title>
                <style>
                    body { font-family: Arial, sans-serif; direction: rtl; }
                    .report-header { text-align: center; margin-bottom: 30px; }
                    .report-summary { display: flex; flex-wrap: wrap; gap: 20px; margin-bottom: 30px; }
                    .report-item { text-align: center; padding: 15px; border: 1px solid #ddd; }
                    .report-value { font-size: 24px; font-weight: bold; color: #4f46e5; }
                    .report-label { color: #666; margin-top: 5px; }
                    table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
                    th, td { padding: 10px; border: 1px solid #ddd; text-align: right; }
                    th { background-color: #f5f5f5; }
                    @media print { body { margin: 0; } }
                </style>
            </head>
            <body>
                ${reportContent}
            </body>
            </html>
        `);

        printWindow.document.close();
        printWindow.print();
    }

    getStatusText(status) {
        const statusMap = {
            'delivered': 'مسلم',
            'pending': 'معلق',
            'returned': 'راجع',
            'partial_return': 'راجع جزئي'
        };
        return statusMap[status] || status;
    }

    formatDate(dateString) {
        return new Date(dateString).toLocaleDateString('ar-IQ');
    }
}

// Initialize reports manager
document.addEventListener('DOMContentLoaded', () => {
    window.reportsManager = new ReportsManager();
});
