// Order Classification System
class OrderClassificationManager {
    constructor() {
        this.classifications = {
            delivered: {
                name: 'واصلة',
                icon: 'fas fa-check-circle',
                color: '#10b981',
                description: 'طلبات تم تسليمها بنجاح'
            },
            returned: {
                name: 'راجعة',
                icon: 'fas fa-undo',
                color: '#ef4444',
                description: 'طلبات راجعة كاملة'
            },
            partial: {
                name: 'راجعة جزئي',
                icon: 'fas fa-exclamation-triangle',
                color: '#f59e0b',
                description: 'طلبات راجعة جزئياً'
            },
            delayed: {
                name: 'مؤجلة',
                icon: 'fas fa-clock',
                color: '#3b82f6',
                description: 'طلبات مؤجلة للتوصيل لاحقاً'
            },
            pending: {
                name: 'معلقة',
                icon: 'fas fa-hourglass-half',
                color: '#6b7280',
                description: 'طلبات لم تُحدد حالتها بعد'
            }
        };
        this.init();
    }

    async init() {
        await this.loadClassificationData();
        this.setupEventListeners();
        this.renderClassificationDashboard();
    }

    setupEventListeners() {
        // Auto-refresh classification when orders change
        document.addEventListener('ordersUpdated', () => {
            this.refreshClassifications();
        });

        // Classification filter buttons
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('classification-filter')) {
                const status = e.target.dataset.status;
                this.filterOrdersByStatus(status);
            }
        });
    }

    async loadClassificationData() {
        try {
            const orders = await storage.getAll('orders');
            this.classifyOrders(orders);
        } catch (error) {
            console.error('Error loading classification data:', error);
        }
    }

    classifyOrders(orders) {
        this.stats = {
            total: orders.length,
            delivered: orders.filter(order => order.status === 'delivered').length,
            returned: orders.filter(order => order.status === 'returned').length,
            partial: orders.filter(order => order.status === 'partial').length,
            delayed: orders.filter(order => order.status === 'delayed').length,
            pending: orders.filter(order => order.status === 'pending').length
        };

        // Calculate percentages
        this.percentages = {};
        Object.keys(this.stats).forEach(key => {
            if (key !== 'total') {
                this.percentages[key] = this.stats.total > 0 
                    ? ((this.stats[key] / this.stats.total) * 100).toFixed(1)
                    : 0;
            }
        });

        // Calculate financial impact
        this.calculateFinancialImpact(orders);
    }

    calculateFinancialImpact(orders) {
        this.financial = {
            totalRevenue: 0,
            deliveredRevenue: 0,
            returnedLoss: 0,
            partialLoss: 0,
            pendingRevenue: 0
        };

        orders.forEach(order => {
            const price = parseFloat(order.deliveryPrice) || 0;
            
            switch (order.status) {
                case 'delivered':
                    this.financial.deliveredRevenue += price;
                    this.financial.totalRevenue += price;
                    break;
                case 'returned':
                    this.financial.returnedLoss += price;
                    break;
                case 'partial':
                    this.financial.partialLoss += price * 0.5; // Assume 50% loss
                    this.financial.totalRevenue += price * 0.5;
                    break;
                case 'pending':
                case 'delayed':
                    this.financial.pendingRevenue += price;
                    break;
            }
        });
    }

    renderClassificationDashboard() {
        const container = document.getElementById('classificationDashboard');
        if (!container) return;

        const dashboardHTML = `
            <div class="classification-dashboard">
                <div class="classification-header">
                    <h3>تصنيف الطلبات التلقائي</h3>
                    <div class="classification-summary">
                        <span class="total-orders">إجمالي الطلبات: ${this.stats.total}</span>
                        <button class="btn-secondary refresh-btn" onclick="orderClassification.refreshClassifications()">
                            <i class="fas fa-sync-alt"></i>
                            تحديث
                        </button>
                    </div>
                </div>

                <div class="classification-grid">
                    ${Object.entries(this.classifications).map(([status, config]) => `
                        <div class="classification-card ${status}" data-status="${status}">
                            <div class="classification-icon">
                                <i class="${config.icon}"></i>
                            </div>
                            <div class="classification-content">
                                <h4>${config.name}</h4>
                                <div class="classification-stats">
                                    <span class="count">${this.stats[status] || 0}</span>
                                    <span class="percentage">${this.percentages[status] || 0}%</span>
                                </div>
                                <p class="description">${config.description}</p>
                                <button class="classification-filter btn-outline" data-status="${status}">
                                    عرض الطلبات
                                </button>
                            </div>
                            <div class="classification-progress">
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: ${this.percentages[status] || 0}%; background-color: ${config.color}"></div>
                                </div>
                            </div>
                        </div>
                    `).join('')}
                </div>

                <div class="financial-impact">
                    <h4>التأثير المالي</h4>
                    <div class="financial-grid">
                        <div class="financial-item success">
                            <i class="fas fa-coins"></i>
                            <div>
                                <span class="label">إيرادات مُحققة</span>
                                <span class="value">${this.formatCurrency(this.financial.deliveredRevenue)}</span>
                            </div>
                        </div>
                        <div class="financial-item warning">
                            <i class="fas fa-exclamation-triangle"></i>
                            <div>
                                <span class="label">خسائر جزئية</span>
                                <span class="value">${this.formatCurrency(this.financial.partialLoss)}</span>
                            </div>
                        </div>
                        <div class="financial-item danger">
                            <i class="fas fa-times-circle"></i>
                            <div>
                                <span class="label">خسائر راجعة</span>
                                <span class="value">${this.formatCurrency(this.financial.returnedLoss)}</span>
                            </div>
                        </div>
                        <div class="financial-item info">
                            <i class="fas fa-hourglass-half"></i>
                            <div>
                                <span class="label">إيرادات معلقة</span>
                                <span class="value">${this.formatCurrency(this.financial.pendingRevenue)}</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="classification-insights">
                    <h4>رؤى وتحليلات</h4>
                    <div class="insights-grid">
                        ${this.generateInsights()}
                    </div>
                </div>
            </div>
        `;

        container.innerHTML = dashboardHTML;
    }

    generateInsights() {
        const insights = [];

        // Success rate insight
        const successRate = this.percentages.delivered;
        if (successRate >= 80) {
            insights.push({
                type: 'success',
                icon: 'fas fa-trophy',
                title: 'معدل نجاح ممتاز',
                message: `معدل التسليم الناجح ${successRate}% - أداء ممتاز!`
            });
        } else if (successRate >= 60) {
            insights.push({
                type: 'warning',
                icon: 'fas fa-chart-line',
                title: 'معدل نجاح جيد',
                message: `معدل التسليم ${successRate}% - يمكن تحسينه`
            });
        } else {
            insights.push({
                type: 'danger',
                icon: 'fas fa-exclamation-circle',
                title: 'معدل نجاح منخفض',
                message: `معدل التسليم ${successRate}% - يحتاج تحسين عاجل`
            });
        }

        // Return rate insight
        const returnRate = parseFloat(this.percentages.returned) + parseFloat(this.percentages.partial);
        if (returnRate > 20) {
            insights.push({
                type: 'warning',
                icon: 'fas fa-undo',
                title: 'معدل إرجاع مرتفع',
                message: `معدل الإرجاع ${returnRate.toFixed(1)}% - راجع أسباب الإرجاع`
            });
        }

        // Pending orders insight
        if (this.stats.pending > 10) {
            insights.push({
                type: 'info',
                icon: 'fas fa-clock',
                title: 'طلبات معلقة كثيرة',
                message: `${this.stats.pending} طلب معلق - يحتاج متابعة`
            });
        }

        // Financial insight
        const totalLoss = this.financial.returnedLoss + this.financial.partialLoss;
        if (totalLoss > 50000) {
            insights.push({
                type: 'danger',
                icon: 'fas fa-chart-line-down',
                title: 'خسائر مالية مرتفعة',
                message: `خسائر بقيمة ${this.formatCurrency(totalLoss)} - راجع العمليات`
            });
        }

        return insights.map(insight => `
            <div class="insight-item ${insight.type}">
                <i class="${insight.icon}"></i>
                <div>
                    <h5>${insight.title}</h5>
                    <p>${insight.message}</p>
                </div>
            </div>
        `).join('');
    }

    async filterOrdersByStatus(status) {
        try {
            const orders = await storage.getAll('orders');
            const filteredOrders = orders.filter(order => order.status === status);
            
            // Trigger custom event to update orders display
            const event = new CustomEvent('filterOrders', {
                detail: { orders: filteredOrders, status: status }
            });
            document.dispatchEvent(event);

            // Show notification
            const statusName = this.classifications[status]?.name || status;
            window.app.showNotification(`تم عرض ${filteredOrders.length} طلب من فئة "${statusName}"`, 'info');
            
        } catch (error) {
            console.error('Error filtering orders:', error);
            window.app.showNotification('خطأ في فلترة الطلبات', 'error');
        }
    }

    async refreshClassifications() {
        await this.loadClassificationData();
        this.renderClassificationDashboard();
        window.app.showNotification('تم تحديث التصنيفات', 'success');
    }

    formatCurrency(amount) {
        return new Intl.NumberFormat('ar-IQ', {
            style: 'currency',
            currency: 'IQD',
            minimumFractionDigits: 0
        }).format(amount);
    }

    // Auto-classify order based on rules
    autoClassifyOrder(order) {
        // This can be enhanced with ML or rule-based classification
        const daysSinceCreated = Math.floor((new Date() - new Date(order.createdAt)) / (1000 * 60 * 60 * 24));
        
        if (order.status === 'pending' && daysSinceCreated > 7) {
            return 'delayed';
        }
        
        return order.status;
    }

    // Get classification statistics for a specific period
    async getClassificationStats(startDate, endDate) {
        try {
            const orders = await storage.getAll('orders');
            const filteredOrders = orders.filter(order => {
                const orderDate = new Date(order.createdAt);
                return orderDate >= startDate && orderDate <= endDate;
            });
            
            this.classifyOrders(filteredOrders);
            return {
                stats: this.stats,
                percentages: this.percentages,
                financial: this.financial
            };
        } catch (error) {
            console.error('Error getting classification stats:', error);
            return null;
        }
    }
}

// Initialize classification manager
const orderClassification = new OrderClassificationManager();

// Export for use in other modules
window.orderClassification = orderClassification;
