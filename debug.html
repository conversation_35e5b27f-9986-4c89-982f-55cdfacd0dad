<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تشخيص مشكلة التنقل</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .nav-menu {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            padding: 0;
            list-style: none;
        }
        .nav-link {
            padding: 10px 20px;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .nav-link:hover {
            background: #0056b3;
        }
        .nav-link.active {
            background: #28a745;
        }
        .content-section {
            display: none;
            padding: 20px;
            border: 2px solid #ddd;
            border-radius: 4px;
            margin-top: 20px;
        }
        .content-section.active {
            display: block;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>تشخيص مشكلة التنقل في نظام إدارة التوصيل</h1>
        
        <div id="diagnostics"></div>
        
        <nav>
            <ul class="nav-menu">
                <li><a href="#dashboard" class="nav-link active" data-section="dashboard">لوحة التحكم</a></li>
                <li><a href="#orders" class="nav-link" data-section="orders">إدارة الطلبات</a></li>
                <li><a href="#drivers" class="nav-link" data-section="drivers">إدارة المندوبين</a></li>
                <li><a href="#customers" class="nav-link" data-section="customers">إدارة العملاء</a></li>
                <li><a href="#assignment" class="nav-link" data-section="assignment">إسناد الطلبات</a></li>
                <li><a href="#reports" class="nav-link" data-section="reports">التقارير</a></li>
                <li><a href="#accounting" class="nav-link" data-section="accounting">المحاسبة</a></li>
                <li><a href="#settings" class="nav-link" data-section="settings">الإعدادات</a></li>
            </ul>
        </nav>

        <section id="dashboard" class="content-section active">
            <h2>لوحة التحكم</h2>
            <p>هذا قسم لوحة التحكم - يجب أن يظهر بشكل افتراضي</p>
        </section>

        <section id="orders" class="content-section">
            <h2>إدارة الطلبات</h2>
            <p>هذا قسم إدارة الطلبات</p>
        </section>

        <section id="drivers" class="content-section">
            <h2>إدارة المندوبين</h2>
            <p>هذا قسم إدارة المندوبين</p>
        </section>

        <section id="customers" class="content-section">
            <h2>إدارة العملاء</h2>
            <p>هذا قسم إدارة العملاء</p>
        </section>

        <section id="assignment" class="content-section">
            <h2>إسناد الطلبات</h2>
            <p>هذا قسم إسناد الطلبات</p>
        </section>

        <section id="reports" class="content-section">
            <h2>التقارير</h2>
            <p>هذا قسم التقارير</p>
        </section>

        <section id="accounting" class="content-section">
            <h2>المحاسبة</h2>
            <p>هذا قسم المحاسبة</p>
        </section>

        <section id="settings" class="content-section">
            <h2>الإعدادات</h2>
            <p>هذا قسم الإعدادات</p>
        </section>
    </div>

    <script>
        // تشخيص المشكلة
        function runDiagnostics() {
            const diagnostics = document.getElementById('diagnostics');
            let results = [];

            // فحص وجود العناصر
            const navLinks = document.querySelectorAll('.nav-link');
            const sections = document.querySelectorAll('.content-section');
            
            results.push({
                type: 'info',
                message: `تم العثور على ${navLinks.length} روابط تنقل`
            });
            
            results.push({
                type: 'info',
                message: `تم العثور على ${sections.length} أقسام محتوى`
            });

            // فحص الأقسام النشطة
            const activeSections = document.querySelectorAll('.content-section.active');
            results.push({
                type: activeSections.length === 1 ? 'success' : 'error',
                message: `عدد الأقسام النشطة: ${activeSections.length} (يجب أن يكون 1)`
            });

            // عرض النتائج
            diagnostics.innerHTML = results.map(result => 
                `<div class="status ${result.type}">${result.message}</div>`
            ).join('');
        }

        // إعداد التنقل
        function setupNavigation() {
            document.querySelectorAll('.nav-link').forEach(link => {
                link.addEventListener('click', (e) => {
                    e.preventDefault();
                    const section = link.dataset.section;
                    showSection(section);
                });
            });
        }

        function showSection(sectionName) {
            // إخفاء جميع الأقسام
            document.querySelectorAll('.content-section').forEach(section => {
                section.classList.remove('active');
            });

            // إظهار القسم المطلوب
            const targetSection = document.getElementById(sectionName);
            if (targetSection) {
                targetSection.classList.add('active');
            }

            // تحديث التنقل
            document.querySelectorAll('.nav-link').forEach(link => {
                link.classList.remove('active');
            });

            const activeLink = document.querySelector(`[data-section="${sectionName}"]`);
            if (activeLink) {
                activeLink.classList.add('active');
            }

            // تشغيل التشخيص مرة أخرى
            setTimeout(runDiagnostics, 100);
        }

        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', () => {
            setupNavigation();
            runDiagnostics();
        });
    </script>
</body>
</html>
