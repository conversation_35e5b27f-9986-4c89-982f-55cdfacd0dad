<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نظام إدارة التوصيل - مستقل</title>
    <style>
        /* نسخة مبسطة من CSS الأساسي */
        :root {
            --primary-color: #4f46e5;
            --bg-primary: #f0f2f5;
            --bg-secondary: #ffffff;
            --text-primary: #2d3748;
            --spacing-md: 1rem;
            --spacing-lg: 1.5rem;
            --radius-md: 0.5rem;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: var(--bg-primary);
            color: var(--text-primary);
            direction: rtl;
        }

        .header {
            background: var(--bg-secondary);
            padding: var(--spacing-md);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
        }

        .sidebar {
            position: fixed;
            top: 70px;
            right: 0;
            width: 250px;
            height: calc(100vh - 70px);
            background: var(--bg-secondary);
            box-shadow: 2px 0 4px rgba(0,0,0,0.1);
            overflow-y: auto;
        }

        .nav-menu {
            list-style: none;
            padding: var(--spacing-md);
        }

        .nav-item {
            margin-bottom: 0.5rem;
        }

        .nav-link {
            display: flex;
            align-items: center;
            padding: var(--spacing-md);
            color: var(--text-primary);
            text-decoration: none;
            border-radius: var(--radius-md);
            transition: all 0.3s ease;
        }

        .nav-link:hover {
            background: var(--primary-color);
            color: white;
        }

        .nav-item.active .nav-link {
            background: var(--primary-color);
            color: white;
        }

        .nav-link i {
            margin-left: 0.5rem;
            width: 20px;
        }

        .main-content {
            margin-top: 70px;
            margin-right: 250px;
            padding: var(--spacing-lg);
            min-height: calc(100vh - 70px);
        }

        .content-section {
            display: none;
            background: var(--bg-secondary);
            padding: var(--spacing-lg);
            border-radius: var(--radius-md);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .content-section.active {
            display: block;
        }

        .page-header {
            margin-bottom: var(--spacing-lg);
            padding-bottom: var(--spacing-md);
            border-bottom: 2px solid var(--primary-color);
        }

        .page-header h2 {
            color: var(--primary-color);
            margin-bottom: 0.5rem;
        }

        .status-indicator {
            position: fixed;
            top: 80px;
            left: 20px;
            background: white;
            padding: 10px;
            border-radius: 4px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            font-size: 0.9rem;
            z-index: 1001;
        }

        .status-success {
            border-left: 4px solid #28a745;
            color: #155724;
        }

        .status-error {
            border-left: 4px solid #dc3545;
            color: #721c24;
        }

        .status-info {
            border-left: 4px solid #17a2b8;
            color: #0c5460;
        }

        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(100%);
                transition: transform 0.3s ease;
            }
            
            .main-content {
                margin-right: 0;
            }
        }
    </style>
</head>
<body>
    <!-- Status Indicator -->
    <div id="statusIndicator" class="status-indicator status-info">
        جاري التحميل...
    </div>

    <!-- Header -->
    <header class="header">
        <div style="display: flex; justify-content: space-between; align-items: center;">
            <div style="display: flex; align-items: center; gap: 10px;">
                <i class="fas fa-truck" style="font-size: 1.5rem; color: var(--primary-color);"></i>
                <h1 style="color: var(--primary-color);">نظام إدارة التوصيل</h1>
            </div>
            <div>
                <span>مرحباً، المدير</span>
                <i class="fas fa-user-circle" style="margin-right: 10px;"></i>
            </div>
        </div>
    </header>

    <!-- Sidebar -->
    <nav class="sidebar">
        <ul class="nav-menu">
            <li class="nav-item active">
                <a href="#dashboard" class="nav-link" data-section="dashboard">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>لوحة التحكم</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="#orders" class="nav-link" data-section="orders">
                    <i class="fas fa-box"></i>
                    <span>إدارة الطلبات</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="#drivers" class="nav-link" data-section="drivers">
                    <i class="fas fa-users"></i>
                    <span>إدارة المندوبين</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="#customers" class="nav-link" data-section="customers">
                    <i class="fas fa-building"></i>
                    <span>إدارة العملاء</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="#assignment" class="nav-link" data-section="assignment">
                    <i class="fas fa-clipboard-list"></i>
                    <span>إسناد الطلبات</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="#reports" class="nav-link" data-section="reports">
                    <i class="fas fa-chart-bar"></i>
                    <span>التقارير</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="#accounting" class="nav-link" data-section="accounting">
                    <i class="fas fa-calculator"></i>
                    <span>المحاسبة</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="#settings" class="nav-link" data-section="settings">
                    <i class="fas fa-cog"></i>
                    <span>الإعدادات</span>
                </a>
            </li>
        </ul>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
        <section id="dashboard" class="content-section active">
            <div class="page-header">
                <h2>لوحة التحكم</h2>
                <p>نظرة عامة على أداء الشركة</p>
            </div>
            <div>
                <h3>إحصائيات سريعة</h3>
                <p>هذا قسم لوحة التحكم يعمل بشكل صحيح!</p>
                <ul>
                    <li>إجمالي الطلبات: 150</li>
                    <li>طلبات مسلمة: 120</li>
                    <li>طلبات معلقة: 25</li>
                    <li>طلبات راجعة: 5</li>
                </ul>
            </div>
        </section>

        <section id="orders" class="content-section">
            <div class="page-header">
                <h2>إدارة الطلبات</h2>
                <p>إدارة جميع طلبات التوصيل</p>
            </div>
            <div>
                <h3>قائمة الطلبات</h3>
                <p>هذا قسم إدارة الطلبات يعمل بشكل صحيح!</p>
            </div>
        </section>

        <section id="drivers" class="content-section">
            <div class="page-header">
                <h2>إدارة المندوبين</h2>
                <p>إدارة بيانات المندوبين والعمولات</p>
            </div>
            <div>
                <h3>قائمة المندوبين</h3>
                <p>هذا قسم إدارة المندوبين يعمل بشكل صحيح!</p>
            </div>
        </section>

        <section id="customers" class="content-section">
            <div class="page-header">
                <h2>إدارة العملاء</h2>
                <p>إدارة بيانات العملاء والشركات</p>
            </div>
            <div>
                <h3>قائمة العملاء</h3>
                <p>هذا قسم إدارة العملاء يعمل بشكل صحيح!</p>
            </div>
        </section>

        <section id="assignment" class="content-section">
            <div class="page-header">
                <h2>إسناد الطلبات</h2>
                <p>إسناد الطلبات للمندوبين</p>
            </div>
            <div>
                <h3>إسناد الطلبات</h3>
                <p>هذا قسم إسناد الطلبات يعمل بشكل صحيح!</p>
            </div>
        </section>

        <section id="reports" class="content-section">
            <div class="page-header">
                <h2>التقارير المالية</h2>
                <p>تقارير شاملة عن الأداء المالي</p>
            </div>
            <div>
                <h3>التقارير</h3>
                <p>هذا قسم التقارير يعمل بشكل صحيح!</p>
            </div>
        </section>

        <section id="accounting" class="content-section">
            <div class="page-header">
                <h2>المحاسبة</h2>
                <p>إدارة الحسابات والعمولات</p>
            </div>
            <div>
                <h3>المحاسبة</h3>
                <p>هذا قسم المحاسبة يعمل بشكل صحيح!</p>
            </div>
        </section>

        <section id="settings" class="content-section">
            <div class="page-header">
                <h2>الإعدادات</h2>
                <p>إعدادات النظام والنسخ الاحتياطي</p>
            </div>
            <div>
                <h3>الإعدادات</h3>
                <p>هذا قسم الإعدادات يعمل بشكل صحيح!</p>
            </div>
        </section>
    </main>

    <!-- Font Awesome CDN -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <script>
        // نسخة مبسطة من منطق التنقل
        class SimpleDeliveryApp {
            constructor() {
                this.currentSection = 'dashboard';
                this.init();
            }

            init() {
                this.setupEventListeners();
                this.updateStatus('تم تحميل النظام بنجاح', 'success');
                console.log('Simple Delivery App initialized');
            }

            setupEventListeners() {
                const navLinks = document.querySelectorAll('.nav-link');
                console.log(`Found ${navLinks.length} navigation links`);
                
                navLinks.forEach(link => {
                    link.addEventListener('click', (e) => {
                        e.preventDefault();
                        const section = link.dataset.section;
                        console.log(`Navigating to section: ${section}`);
                        this.showSection(section);
                    });
                });

                this.updateStatus(`تم إعداد ${navLinks.length} روابط تنقل`, 'info');
            }

            showSection(sectionName) {
                console.log(`Showing section: ${sectionName}`);
                
                // إخفاء جميع الأقسام
                const allSections = document.querySelectorAll('.content-section');
                console.log(`Found ${allSections.length} content sections`);
                
                allSections.forEach(section => {
                    section.classList.remove('active');
                });

                // إظهار القسم المطلوب
                const targetSection = document.getElementById(sectionName);
                if (targetSection) {
                    targetSection.classList.add('active');
                    console.log(`Section ${sectionName} activated successfully`);
                    this.updateStatus(`تم الانتقال إلى: ${this.getSectionTitle(sectionName)}`, 'success');
                } else {
                    console.error(`Section ${sectionName} not found!`);
                    this.updateStatus(`خطأ: القسم ${sectionName} غير موجود`, 'error');
                }

                // تحديث التنقل
                document.querySelectorAll('.nav-item').forEach(item => {
                    item.classList.remove('active');
                });

                const activeNavItem = document.querySelector(`[data-section="${sectionName}"]`)?.parentElement;
                if (activeNavItem) {
                    activeNavItem.classList.add('active');
                    console.log(`Navigation updated for ${sectionName}`);
                } else {
                    console.error(`Navigation item for ${sectionName} not found!`);
                }

                this.currentSection = sectionName;
            }

            getSectionTitle(sectionName) {
                const titles = {
                    'dashboard': 'لوحة التحكم',
                    'orders': 'إدارة الطلبات',
                    'drivers': 'إدارة المندوبين',
                    'customers': 'إدارة العملاء',
                    'assignment': 'إسناد الطلبات',
                    'reports': 'التقارير',
                    'accounting': 'المحاسبة',
                    'settings': 'الإعدادات'
                };
                return titles[sectionName] || sectionName;
            }

            updateStatus(message, type = 'info') {
                const statusIndicator = document.getElementById('statusIndicator');
                if (statusIndicator) {
                    statusIndicator.textContent = message;
                    statusIndicator.className = `status-indicator status-${type}`;
                    
                    // إخفاء الرسالة بعد 3 ثوان
                    setTimeout(() => {
                        statusIndicator.style.display = 'none';
                    }, 3000);
                }
            }
        }

        // تهيئة التطبيق عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', () => {
            try {
                window.simpleApp = new SimpleDeliveryApp();
                console.log('Simple app initialized successfully');
            } catch (error) {
                console.error('Error initializing simple app:', error);
            }
        });
    </script>
</body>
</html>
