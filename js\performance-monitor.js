// Performance Monitoring and Optimization System
class PerformanceMonitor {
    constructor() {
        this.metrics = {
            pageLoad: null,
            domReady: null,
            firstPaint: null,
            firstContentfulPaint: null,
            largestContentfulPaint: null,
            memoryUsage: null,
            navigationTiming: null
        };
        
        this.observers = [];
        this.isMonitoring = false;
        this.init();
    }

    init() {
        if (typeof window === 'undefined' || !window.performance) {
            console.warn('Performance API not available');
            return;
        }

        this.startMonitoring();
        this.setupObservers();
        this.measureInitialMetrics();
    }

    startMonitoring() {
        this.isMonitoring = true;
        console.log('📊 Performance monitoring started');

        // Monitor page load
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                this.measureDOMReady();
            });
        } else {
            this.measureDOMReady();
        }

        window.addEventListener('load', () => {
            this.measurePageLoad();
        });
    }

    setupObservers() {
        // Performance Observer for paint metrics
        if (window.PerformanceObserver) {
            try {
                // Paint metrics
                const paintObserver = new PerformanceObserver((list) => {
                    for (const entry of list.getEntries()) {
                        if (entry.name === 'first-paint') {
                            this.metrics.firstPaint = entry.startTime;
                        } else if (entry.name === 'first-contentful-paint') {
                            this.metrics.firstContentfulPaint = entry.startTime;
                        }
                    }
                });
                paintObserver.observe({ entryTypes: ['paint'] });
                this.observers.push(paintObserver);

                // Largest Contentful Paint
                const lcpObserver = new PerformanceObserver((list) => {
                    const entries = list.getEntries();
                    const lastEntry = entries[entries.length - 1];
                    this.metrics.largestContentfulPaint = lastEntry.startTime;
                });
                lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });
                this.observers.push(lcpObserver);

                // Navigation timing
                const navigationObserver = new PerformanceObserver((list) => {
                    for (const entry of list.getEntries()) {
                        this.metrics.navigationTiming = {
                            domainLookup: entry.domainLookupEnd - entry.domainLookupStart,
                            tcpConnect: entry.connectEnd - entry.connectStart,
                            request: entry.responseStart - entry.requestStart,
                            response: entry.responseEnd - entry.responseStart,
                            domProcessing: entry.domContentLoadedEventStart - entry.responseEnd,
                            domComplete: entry.domComplete - entry.domContentLoadedEventStart
                        };
                    }
                });
                navigationObserver.observe({ entryTypes: ['navigation'] });
                this.observers.push(navigationObserver);

            } catch (error) {
                console.warn('Some performance observers not supported:', error);
            }
        }
    }

    measureInitialMetrics() {
        // Memory usage (if available)
        if (performance.memory) {
            this.metrics.memoryUsage = {
                used: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024),
                total: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024),
                limit: Math.round(performance.memory.jsHeapSizeLimit / 1024 / 1024)
            };
        }
    }

    measureDOMReady() {
        const navigation = performance.getEntriesByType('navigation')[0];
        if (navigation) {
            this.metrics.domReady = navigation.domContentLoadedEventEnd - navigation.fetchStart;
        }
        console.log(`📄 DOM Ready: ${this.metrics.domReady}ms`);
    }

    measurePageLoad() {
        const navigation = performance.getEntriesByType('navigation')[0];
        if (navigation) {
            this.metrics.pageLoad = navigation.loadEventEnd - navigation.fetchStart;
        }
        console.log(`🚀 Page Load: ${this.metrics.pageLoad}ms`);
        
        // Log all metrics
        this.logMetrics();
    }

    logMetrics() {
        console.group('📊 Performance Metrics');
        
        if (this.metrics.pageLoad) {
            console.log(`Page Load Time: ${Math.round(this.metrics.pageLoad)}ms`);
        }
        
        if (this.metrics.domReady) {
            console.log(`DOM Ready Time: ${Math.round(this.metrics.domReady)}ms`);
        }
        
        if (this.metrics.firstPaint) {
            console.log(`First Paint: ${Math.round(this.metrics.firstPaint)}ms`);
        }
        
        if (this.metrics.firstContentfulPaint) {
            console.log(`First Contentful Paint: ${Math.round(this.metrics.firstContentfulPaint)}ms`);
        }
        
        if (this.metrics.largestContentfulPaint) {
            console.log(`Largest Contentful Paint: ${Math.round(this.metrics.largestContentfulPaint)}ms`);
        }
        
        if (this.metrics.memoryUsage) {
            console.log(`Memory Usage: ${this.metrics.memoryUsage.used}MB / ${this.metrics.memoryUsage.total}MB`);
        }
        
        if (this.metrics.navigationTiming) {
            console.log('Navigation Timing:', this.metrics.navigationTiming);
        }
        
        console.groupEnd();
        
        // Performance recommendations
        this.generateRecommendations();
    }

    generateRecommendations() {
        const recommendations = [];
        
        if (this.metrics.pageLoad > 3000) {
            recommendations.push('⚠️ Page load time is slow (>3s). Consider optimizing images and scripts.');
        }
        
        if (this.metrics.domReady > 1500) {
            recommendations.push('⚠️ DOM ready time is slow (>1.5s). Consider reducing JavaScript execution time.');
        }
        
        if (this.metrics.firstContentfulPaint > 2000) {
            recommendations.push('⚠️ First Contentful Paint is slow (>2s). Consider optimizing critical rendering path.');
        }
        
        if (this.metrics.memoryUsage && this.metrics.memoryUsage.used > 50) {
            recommendations.push('⚠️ High memory usage detected. Consider optimizing JavaScript objects.');
        }
        
        if (recommendations.length > 0) {
            console.group('💡 Performance Recommendations');
            recommendations.forEach(rec => console.log(rec));
            console.groupEnd();
        } else {
            console.log('✅ Performance looks good!');
        }
    }

    // Get current metrics
    getMetrics() {
        return { ...this.metrics };
    }

    // Monitor specific function performance
    measureFunction(fn, name = 'Function') {
        return function(...args) {
            const start = performance.now();
            const result = fn.apply(this, args);
            const end = performance.now();
            console.log(`⏱️ ${name} execution time: ${(end - start).toFixed(2)}ms`);
            return result;
        };
    }

    // Monitor async function performance
    measureAsyncFunction(fn, name = 'Async Function') {
        return async function(...args) {
            const start = performance.now();
            const result = await fn.apply(this, args);
            const end = performance.now();
            console.log(`⏱️ ${name} execution time: ${(end - start).toFixed(2)}ms`);
            return result;
        };
    }

    // Resource timing analysis
    analyzeResources() {
        const resources = performance.getEntriesByType('resource');
        const analysis = {
            scripts: [],
            stylesheets: [],
            images: [],
            other: []
        };

        resources.forEach(resource => {
            const info = {
                name: resource.name,
                duration: Math.round(resource.duration),
                size: resource.transferSize || 0,
                cached: resource.transferSize === 0
            };

            if (resource.name.includes('.js')) {
                analysis.scripts.push(info);
            } else if (resource.name.includes('.css')) {
                analysis.stylesheets.push(info);
            } else if (resource.name.match(/\.(jpg|jpeg|png|gif|svg|webp)$/i)) {
                analysis.images.push(info);
            } else {
                analysis.other.push(info);
            }
        });

        // Sort by duration (slowest first)
        Object.keys(analysis).forEach(key => {
            analysis[key].sort((a, b) => b.duration - a.duration);
        });

        console.group('📦 Resource Analysis');
        console.log('Scripts:', analysis.scripts);
        console.log('Stylesheets:', analysis.stylesheets);
        console.log('Images:', analysis.images);
        console.log('Other:', analysis.other);
        console.groupEnd();

        return analysis;
    }

    // Memory monitoring
    startMemoryMonitoring(interval = 5000) {
        if (!performance.memory) {
            console.warn('Memory API not available');
            return;
        }

        const monitor = () => {
            const memory = {
                used: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024),
                total: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024),
                limit: Math.round(performance.memory.jsHeapSizeLimit / 1024 / 1024)
            };

            if (memory.used > memory.limit * 0.8) {
                console.warn('🚨 High memory usage detected:', memory);
            }

            this.metrics.memoryUsage = memory;
        };

        monitor(); // Initial measurement
        return setInterval(monitor, interval);
    }

    // Clean up observers
    destroy() {
        this.observers.forEach(observer => {
            try {
                observer.disconnect();
            } catch (error) {
                console.warn('Error disconnecting observer:', error);
            }
        });
        this.observers = [];
        this.isMonitoring = false;
        console.log('📊 Performance monitoring stopped');
    }

    // Export metrics for reporting
    exportMetrics() {
        const report = {
            timestamp: new Date().toISOString(),
            url: window.location.href,
            userAgent: navigator.userAgent,
            metrics: this.getMetrics(),
            resources: this.analyzeResources()
        };

        const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `performance-report-${Date.now()}.json`;
        a.click();
        URL.revokeObjectURL(url);
    }
}

// Initialize performance monitor
let performanceMonitor;

function initializePerformanceMonitor() {
    try {
        performanceMonitor = new PerformanceMonitor();
        window.performanceMonitor = performanceMonitor;
        console.log('✅ Performance Monitor initialized');
    } catch (error) {
        console.error('❌ Failed to initialize Performance Monitor:', error);
    }
}

// Initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializePerformanceMonitor);
} else {
    initializePerformanceMonitor();
}

// Global performance utilities
window.measurePerformance = {
    function: (fn, name) => performanceMonitor?.measureFunction(fn, name) || fn,
    async: (fn, name) => performanceMonitor?.measureAsyncFunction(fn, name) || fn,
    resources: () => performanceMonitor?.analyzeResources(),
    export: () => performanceMonitor?.exportMetrics(),
    metrics: () => performanceMonitor?.getMetrics()
};
