<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنشاء أيقونات PWA</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f0f0f3;
        }
        .icon-generator {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        .icon-preview {
            width: 200px;
            height: 200px;
            margin: 20px auto;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 80px;
            box-shadow: 0 20px 40px rgba(102, 126, 234, 0.3);
        }
        .download-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            font-size: 16px;
            cursor: pointer;
            margin: 10px;
        }
        .size-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        .size-item {
            text-align: center;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
        }
        .size-icon {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 20%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 10px;
        }
    </style>
</head>
<body>
    <div class="icon-generator">
        <h1>مولد أيقونات PWA - نظام إدارة التوصيل</h1>
        
        <div class="icon-preview" id="mainIcon">
            <i class="fas fa-shipping-fast"></i>
        </div>
        
        <div style="text-align: center;">
            <button class="download-btn" onclick="generateAllIcons()">تحميل جميع الأيقونات</button>
        </div>
        
        <div class="size-grid">
            <div class="size-item">
                <div class="size-icon" style="width: 72px; height: 72px; font-size: 30px;">
                    <i class="fas fa-shipping-fast"></i>
                </div>
                <p>72x72</p>
                <button class="download-btn" onclick="downloadIcon(72)">تحميل</button>
            </div>
            
            <div class="size-item">
                <div class="size-icon" style="width: 96px; height: 96px; font-size: 40px;">
                    <i class="fas fa-shipping-fast"></i>
                </div>
                <p>96x96</p>
                <button class="download-btn" onclick="downloadIcon(96)">تحميل</button>
            </div>
            
            <div class="size-item">
                <div class="size-icon" style="width: 128px; height: 128px; font-size: 50px;">
                    <i class="fas fa-shipping-fast"></i>
                </div>
                <p>128x128</p>
                <button class="download-btn" onclick="downloadIcon(128)">تحميل</button>
            </div>
            
            <div class="size-item">
                <div class="size-icon" style="width: 144px; height: 144px; font-size: 60px;">
                    <i class="fas fa-shipping-fast"></i>
                </div>
                <p>144x144</p>
                <button class="download-btn" onclick="downloadIcon(144)">تحميل</button>
            </div>
            
            <div class="size-item">
                <div class="size-icon" style="width: 152px; height: 152px; font-size: 60px;">
                    <i class="fas fa-shipping-fast"></i>
                </div>
                <p>152x152</p>
                <button class="download-btn" onclick="downloadIcon(152)">تحميل</button>
            </div>
            
            <div class="size-item">
                <div class="size-icon" style="width: 192px; height: 192px; font-size: 80px;">
                    <i class="fas fa-shipping-fast"></i>
                </div>
                <p>192x192</p>
                <button class="download-btn" onclick="downloadIcon(192)">تحميل</button>
            </div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/js/all.min.js"></script>
    <script>
        function createIcon(size) {
            const canvas = document.createElement('canvas');
            canvas.width = size;
            canvas.height = size;
            const ctx = canvas.getContext('2d');
            
            // Create gradient background
            const gradient = ctx.createLinearGradient(0, 0, size, size);
            gradient.addColorStop(0, '#667eea');
            gradient.addColorStop(1, '#764ba2');
            
            // Draw rounded rectangle background
            const radius = size * 0.2;
            ctx.fillStyle = gradient;
            ctx.beginPath();
            ctx.roundRect(0, 0, size, size, radius);
            ctx.fill();
            
            // Draw truck icon (simplified)
            ctx.fillStyle = 'white';
            ctx.font = `${size * 0.4}px "Font Awesome 6 Free"`;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText('🚚', size / 2, size / 2);
            
            return canvas;
        }
        
        function downloadIcon(size) {
            const canvas = createIcon(size);
            const link = document.createElement('a');
            link.download = `icon-${size}x${size}.png`;
            link.href = canvas.toDataURL();
            link.click();
        }
        
        function generateAllIcons() {
            const sizes = [72, 96, 128, 144, 152, 192, 384, 512];
            sizes.forEach(size => {
                setTimeout(() => downloadIcon(size), size * 10);
            });
        }
        
        // Add roundRect polyfill for older browsers
        if (!CanvasRenderingContext2D.prototype.roundRect) {
            CanvasRenderingContext2D.prototype.roundRect = function(x, y, width, height, radius) {
                this.moveTo(x + radius, y);
                this.lineTo(x + width - radius, y);
                this.quadraticCurveTo(x + width, y, x + width, y + radius);
                this.lineTo(x + width, y + height - radius);
                this.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
                this.lineTo(x + radius, y + height);
                this.quadraticCurveTo(x, y + height, x, y + height - radius);
                this.lineTo(x, y + radius);
                this.quadraticCurveTo(x, y, x + radius, y);
                this.closePath();
            };
        }
    </script>
</body>
</html>
