<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة شركة التوصيل</title>
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="styles/neumorphic.css">
    <link rel="stylesheet" href="styles/enhanced-transitions.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- <PERSON><PERSON> Manifest -->
    <link rel="manifest" href="manifest.json">

    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#667eea">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="إدارة التوصيل">

    <!-- PWA Icons -->
    <link rel="apple-touch-icon" href="icon-192x192.png">
    <link rel="icon" type="image/png" sizes="192x192" href="icon-192x192.png">
    <link rel="icon" type="image/png" sizes="512x512" href="icon-512x512.png">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <i class="fas fa-truck"></i>
                    <h1>نظام إدارة التوصيل</h1>
                </div>
                <div class="header-actions">
                    <button class="btn-icon theme-toggle" id="themeToggle">
                        <i class="fas fa-moon"></i>
                    </button>
                    <div id="userInfo">
                        <!-- User info will be populated by user management system -->
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Navigation -->
    <nav class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <button class="sidebar-toggle" id="sidebarToggle">
                <i class="fas fa-bars"></i>
            </button>
        </div>
        <ul class="nav-menu">
            <li class="nav-item active">
                <a href="#dashboard" class="nav-link" data-section="dashboard">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>لوحة التحكم</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="#orders" class="nav-link" data-section="orders">
                    <i class="fas fa-box"></i>
                    <span>إدارة الطلبات</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="#drivers" class="nav-link" data-section="drivers">
                    <i class="fas fa-users"></i>
                    <span>إدارة المندوبين</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="#customers" class="nav-link" data-section="customers">
                    <i class="fas fa-building"></i>
                    <span>إدارة العملاء</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="#assignment" class="nav-link" data-section="assignment">
                    <i class="fas fa-clipboard-list"></i>
                    <span>إسناد الطلبات</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="#reports" class="nav-link" data-section="reports">
                    <i class="fas fa-chart-bar"></i>
                    <span>التقارير</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="#accounting" class="nav-link" data-section="accounting">
                    <i class="fas fa-calculator"></i>
                    <span>المحاسبة</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="#settings" class="nav-link" data-section="settings">
                    <i class="fas fa-cog"></i>
                    <span>الإعدادات</span>
                </a>
            </li>
        </ul>
    </nav>

    <!-- Main Content -->
    <main class="main-content" id="mainContent">
        <!-- Dashboard Section -->
        <section id="dashboard" class="content-section active">
            <div class="container">
                <div class="page-header">
                    <h2>لوحة التحكم</h2>
                    <p>نظرة عامة على أداء الشركة</p>
                </div>

                <!-- Stats Cards -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-box"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="totalOrders">0</h3>
                            <p>إجمالي الطلبات</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon delivered">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="deliveredOrders">0</h3>
                            <p>طلبات مسلمة</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon pending">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="pendingOrders">0</h3>
                            <p>طلبات معلقة</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon returned">
                            <i class="fas fa-undo"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="returnedOrders">0</h3>
                            <p>طلبات راجعة</p>
                        </div>
                    </div>
                </div>

                <!-- Recent Orders -->
                <div class="dashboard-section">
                    <div class="section-header">
                        <h3>الطلبات الأخيرة</h3>
                        <button class="btn-primary" onclick="window.app && window.app.showSection('orders')">
                            عرض الكل
                        </button>
                    </div>
                    <div class="recent-orders" id="recentOrders">
                        <!-- Recent orders will be populated here -->
                    </div>
                </div>
            </div>
        </section>

        <!-- Orders Section -->
        <section id="orders" class="content-section">
            <div class="container">
                <div class="page-header">
                    <h2>إدارة الطلبات</h2>
                    <button class="btn-primary" id="addOrderBtn">
                        <i class="fas fa-plus"></i>
                        إضافة طلب جديد
                    </button>
                </div>

                <!-- Search and Filter -->
                <div class="search-filter-section">
                    <div class="search-box">
                        <i class="fas fa-search"></i>
                        <input type="text" id="searchOrders" placeholder="البحث في الطلبات أو رقم الوصل...">
                    </div>
                    <div class="quick-tracking-search">
                        <input type="text" id="quickTrackingSearch" placeholder="البحث السريع برقم الوصل..." class="tracking-search-input">
                        <button class="btn-primary" onclick="ordersManager.quickTrackingSearch()">
                            <i class="fas fa-barcode"></i>
                            تتبع
                        </button>
                    </div>
                    <div class="filter-controls">
                        <select id="customerFilter" class="filter-select">
                            <option value="">جميع العملاء</option>
                        </select>
                        <select id="statusFilter" class="filter-select">
                            <option value="">جميع الحالات</option>
                            <option value="delivered">مسلم</option>
                            <option value="pending">معلق</option>
                            <option value="returned">راجع</option>
                            <option value="partial_return">راجع جزئي</option>
                        </select>
                        <select id="cityFilter" class="filter-select">
                            <option value="">جميع المحافظات</option>
                        </select>
                        <input type="date" id="dateFilter" class="filter-date">
                    </div>
                </div>

                <!-- Order Classification Dashboard -->
                <div id="classificationDashboard">
                    <!-- Classification dashboard will be populated here -->
                </div>

                <!-- Orders Table -->
                <div class="table-container">
                    <table class="data-table" id="ordersTable">
                        <thead>
                            <tr>
                                <th>رقم الطلب</th>
                                <th>رقم الوصل</th>
                                <th>اسم العميل</th>
                                <th>اسم الزبون</th>
                                <th>المحافظة</th>
                                <th>سعر الطلب</th>
                                <th>الحالة</th>
                                <th>التاريخ</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="ordersTableBody">
                            <!-- Orders will be populated here -->
                        </tbody>
                    </table>
                </div>
            </div>
        </section>

        <!-- Drivers Section -->
        <section id="drivers" class="content-section">
            <div class="container">
                <div class="page-header">
                    <h2>إدارة المندوبين</h2>
                    <button class="btn-primary" id="addDriverBtn">
                        <i class="fas fa-plus"></i>
                        إضافة مندوب جديد
                    </button>
                </div>

                <!-- Drivers Grid -->
                <div class="drivers-grid" id="driversGrid">
                    <!-- Drivers will be populated here -->
                </div>
            </div>
        </section>

        <!-- Assignment Section -->
        <section id="assignment" class="content-section">
            <div class="container">
                <div class="page-header">
                    <h2>إسناد الطلبات للمندوبين</h2>
                    <div class="page-actions">
                        <button class="btn-secondary" onclick="window.assignmentManager && window.assignmentManager.showBulkAssignmentModal()">
                            <i class="fas fa-list"></i>
                            إسناد متعدد
                        </button>
                        <button class="btn-primary" onclick="window.assignmentManager && window.assignmentManager.showUnassignedOrders()">
                            <i class="fas fa-eye"></i>
                            عرض الطلبات غير المُسندة
                        </button>
                    </div>
                </div>

                <!-- Assignment Form -->
                <div class="assignment-container">
                    <!-- Multiple Assignment Section -->
                    <div class="multiple-assignment-card">
                        <h3>إسناد متعدد بالأرقام</h3>
                        <form id="multipleAssignmentForm" class="multiple-assignment-form">
                            <div class="form-group">
                                <label class="form-label">أرقام الوصل المتعددة *</label>
                                <textarea class="form-textarea"
                                          id="multipleTrackingInput"
                                          placeholder="أدخل أرقام الوصل مفصولة بفواصل أو أسطر جديدة&#10;مثال:&#10;TRK001234567&#10;TRK001234568, TRK001234569&#10;TRK001234570"
                                          rows="4"></textarea>
                                <small class="form-help">يمكنك إدخال عدة أرقام وصل مفصولة بفواصل (,) أو أسطر جديدة</small>
                                <div id="multipleTrackingError" class="error-message" style="display: none;"></div>
                            </div>

                            <div class="form-group">
                                <label class="form-label">اختيار المندوب للإسناد المتعدد *</label>
                                <select class="form-select" id="multipleDriverSelect" required>
                                    <option value="">اختر المندوب</option>
                                </select>
                            </div>

                            <div class="form-actions">
                                <button type="button" class="btn-secondary" onclick="window.assignmentManager && window.assignmentManager.clearMultipleForm()">
                                    <i class="fas fa-times"></i>
                                    مسح
                                </button>
                                <button type="button" class="btn-primary" onclick="window.assignmentManager && window.assignmentManager.validateMultipleTracking()">
                                    <i class="fas fa-search"></i>
                                    التحقق من الأرقام
                                </button>
                                <button type="submit" class="btn-success" id="multipleAssignBtn" disabled>
                                    <i class="fas fa-users"></i>
                                    إسناد الكل
                                </button>
                            </div>
                        </form>

                        <!-- Multiple Orders Display -->
                        <div id="multipleOrdersDisplay" class="multiple-orders-display" style="display: none;">
                            <h4>الطلبات المُدخلة</h4>
                            <div id="multipleOrdersList" class="multiple-orders-list"></div>
                        </div>
                    </div>

                    <div class="assignment-form-card">
                        <h3>إسناد طلب واحد</h3>
                        <form id="assignmentForm" class="assignment-form">
                            <div class="form-group">
                                <label class="form-label">رقم الوصل/الباركود *</label>
                                <div class="tracking-search-group">
                                    <input type="text"
                                           class="form-input tracking-input"
                                           id="assignmentTrackingInput"
                                           placeholder="أدخل رقم الوصل للبحث عن الطلب..."
                                           autocomplete="off">
                                    <button type="button" class="btn-secondary search-btn" onclick="window.assignmentManager && window.assignmentManager.searchByTracking()">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </div>
                                <div id="trackingSearchError" class="error-message" style="display: none;"></div>
                            </div>

                            <!-- Order Details Display -->
                            <div id="orderDetailsCard" class="order-details-card" style="display: none;">
                                <h4>تفاصيل الطلب</h4>
                                <div class="order-info-grid">
                                    <div class="info-item">
                                        <span class="info-label">رقم الطلب:</span>
                                        <span class="info-value" id="orderIdDisplay">-</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label">العميل:</span>
                                        <span class="info-value" id="clientNameDisplay">-</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label">الزبون:</span>
                                        <span class="info-value" id="customerNameDisplay">-</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label">المحافظة:</span>
                                        <span class="info-value" id="cityDisplay">-</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label">السعر:</span>
                                        <span class="info-value" id="priceDisplay">-</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label">الحالة:</span>
                                        <span class="info-value" id="statusDisplay">-</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label">المندوب الحالي:</span>
                                        <span class="info-value" id="currentDriverDisplay">-</span>
                                    </div>
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="form-label">اختيار المندوب *</label>
                                <select class="form-select" id="assignmentDriverSelect" required>
                                    <option value="">اختر المندوب</option>
                                </select>
                            </div>

                            <div class="form-actions">
                                <button type="button" class="btn-secondary" onclick="window.assignmentManager && window.assignmentManager.clearForm()">
                                    <i class="fas fa-times"></i>
                                    مسح
                                </button>
                                <button type="button" class="btn-danger" id="unassignBtn" onclick="window.assignmentManager && window.assignmentManager.unassignOrder()" style="display: none;">
                                    <i class="fas fa-user-times"></i>
                                    إلغاء الإسناد
                                </button>
                                <button type="submit" class="btn-primary" id="assignBtn">
                                    <i class="fas fa-user-check"></i>
                                    إسناد الطلب
                                </button>
                            </div>
                        </form>
                    </div>

                    <!-- Assignment History -->
                    <div class="assignment-history-card">
                        <h3>آخر العمليات</h3>
                        <div id="assignmentHistory" class="assignment-history">
                            <p class="no-history">لا توجد عمليات إسناد حديثة</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Customers Section -->
        <section id="customers" class="content-section">
            <div class="container">
                <div class="page-header">
                    <h2>إدارة العملاء</h2>
                    <div class="page-actions">
                        <button class="btn-secondary" id="toggleCustomersView">
                            <i class="fas fa-th-large"></i>
                            عرض البطاقات
                        </button>
                        <button class="btn-primary" id="addCustomerBtn">
                            <i class="fas fa-plus"></i>
                            إضافة عميل جديد
                        </button>
                    </div>
                </div>

                <!-- Search and Filter for Customers -->
                <div class="search-filter-section">
                    <div class="search-box">
                        <i class="fas fa-search"></i>
                        <input type="text" id="searchCustomers" placeholder="البحث في العملاء...">
                    </div>
                    <div class="filter-controls">
                        <select id="customerTypeFilter" class="filter-select">
                            <option value="">جميع الأنواع</option>
                            <option value="individual">فرد</option>
                            <option value="company">شركة</option>
                        </select>
                        <select id="customerStatusFilter" class="filter-select">
                            <option value="">جميع الحالات</option>
                            <option value="active">نشط</option>
                            <option value="inactive">غير نشط</option>
                        </select>
                        <select id="customerCityFilter" class="filter-select">
                            <option value="">جميع المحافظات</option>
                        </select>
                    </div>
                </div>

                <!-- Customers Content -->
                <div id="customersContent">
                    <!-- Customers Grid View -->
                    <div class="customers-grid" id="customersGrid" style="display: block;">
                        <!-- Customers will be populated here -->
                    </div>

                    <!-- Customers Table View -->
                    <div class="table-container" id="customersTable" style="display: none;">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>اسم العميل</th>
                                    <th>النوع</th>
                                    <th>الهاتف</th>
                                    <th>المحافظة</th>
                                    <th>عدد الطلبات</th>
                                    <th>الحالة</th>
                                    <th>تاريخ التسجيل</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="customersTableBody">
                                <!-- Customers will be populated here -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </section>

        <!-- Reports Section -->
        <section id="reports" class="content-section">
            <div class="container">
                <div class="page-header">
                    <h2>التقارير المالية</h2>
                    <div class="report-actions">
                        <button class="btn-secondary" id="exportExcel">
                            <i class="fas fa-file-excel"></i>
                            تصدير Excel
                        </button>
                        <button class="btn-secondary" id="printReport">
                            <i class="fas fa-print"></i>
                            طباعة
                        </button>
                    </div>
                </div>

                <!-- Advanced Reports Container -->
                <div id="advancedReportsContainer">
                    <!-- Advanced reports interface will be populated here -->
                </div>

                <!-- Report Filters -->
                <div class="report-filters">
                    <div class="filter-group">
                        <label>نوع التقرير:</label>
                        <select id="reportType">
                            <option value="general">تقرير عام</option>
                            <option value="company">تقرير شركة</option>
                            <option value="driver">تقرير مندوب</option>
                            <option value="customer">تقرير عملاء</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label>من تاريخ:</label>
                        <input type="date" id="reportFromDate">
                    </div>
                    <div class="filter-group">
                        <label>إلى تاريخ:</label>
                        <input type="date" id="reportToDate">
                    </div>
                    <button class="btn-primary" id="generateReport">
                        إنشاء التقرير
                    </button>
                </div>

                <!-- Report Content -->
                <div class="report-content" id="reportContent">
                    <!-- Report will be generated here -->
                </div>
            </div>
        </section>

        <!-- Accounting Section -->
        <section id="accounting" class="content-section">
            <div class="container">
                <div class="page-header">
                    <h2>المحاسبة</h2>
                </div>

                <!-- Accounting Summary -->
                <div class="accounting-summary">
                    <div class="summary-card">
                        <h3>إجمالي المبالغ المستلمة</h3>
                        <div class="amount" id="totalReceived">0 د.ع</div>
                    </div>
                    <div class="summary-card">
                        <h3>إجمالي العمولات</h3>
                        <div class="amount" id="totalCommissions">0 د.ع</div>
                    </div>
                    <div class="summary-card">
                        <h3>صافي الربح</h3>
                        <div class="amount" id="netProfit">0 د.ع</div>
                    </div>
                </div>

                <!-- Company Balances -->
                <div class="balances-section">
                    <h3>أرصدة الشركات</h3>
                    <div class="balances-table" id="companyBalances">
                        <!-- Company balances will be populated here -->
                    </div>
                </div>
            </div>
        </section>

        <!-- Settings Section -->
        <section id="settings" class="content-section">
            <div class="container">
                <div class="page-header">
                    <h2>الإعدادات</h2>
                </div>

                <!-- User Management Section -->
                <div class="settings-section">
                    <h3>إدارة المستخدمين</h3>
                    <div class="section-actions">
                        <button class="btn-primary add-user-btn" data-permission="all">
                            <i class="fas fa-user-plus"></i>
                            إضافة مستخدم
                        </button>
                    </div>
                    <div id="usersTableContainer">
                        <!-- Users table will be populated here -->
                    </div>
                </div>

                <div class="settings-grid">
                    <div class="settings-card">
                        <h3>إعدادات عامة</h3>
                        <div class="setting-item">
                            <label>اسم الشركة:</label>
                            <input type="text" id="companyName" placeholder="اسم شركة التوصيل">
                        </div>
                        <div class="setting-item">
                            <label>العملة الافتراضية:</label>
                            <select id="defaultCurrency">
                                <option value="IQD">دينار عراقي (د.ع)</option>
                                <option value="USD">دولار أمريكي ($)</option>
                            </select>
                        </div>
                    </div>

                    <div class="settings-card">
                        <h3>النسخ الاحتياطي</h3>
                        <button class="btn-primary" id="backupData">
                            <i class="fas fa-download"></i>
                            تصدير البيانات
                        </button>
                        <button class="btn-secondary" id="importData">
                            <i class="fas fa-upload"></i>
                            استيراد البيانات
                        </button>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Modals -->
    <div id="modalOverlay" class="modal-overlay">
        <!-- Modals will be inserted here -->
    </div>

    <!-- Scripts - Optimized Loading Order -->
    <!-- Core Dependencies First -->
    <script src="js/storage.js" defer></script>
    <script src="js/user-management.js" defer></script>
    <script src="js/notifications.js" defer></script>
    <script src="js/performance-monitor.js" defer></script>
    <script src="js/performance-optimizer.js" defer></script>

    <!-- Feature Modules -->
    <script src="js/orders.js" defer></script>
    <script src="js/drivers.js" defer></script>
    <script src="js/customers.js" defer></script>
    <script src="js/assignment.js" defer></script>
    <script src="js/reports.js" defer></script>
    <script src="js/accounting.js" defer></script>

    <!-- Advanced Features -->
    <script src="js/order-classification.js" defer></script>
    <script src="js/advanced-search.js" defer></script>
    <script src="js/advanced-reports.js" defer></script>

    <!-- Sample Data and Main App (Last) -->
    <script src="js/sample-data.js" defer></script>
    <script src="js/main.js" defer></script>
</body>
</html>
