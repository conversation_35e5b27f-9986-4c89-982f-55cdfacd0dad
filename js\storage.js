// Storage Management System using IndexedDB
class StorageManager {
    constructor() {
        this.dbName = 'DeliverySystemDB';
        this.dbVersion = 4; // Incremented for fixed commission system
        this.db = null;
        this.init();
    }

    async init() {
        try {
            this.db = await this.openDB();
            console.log('Database initialized successfully');
        } catch (error) {
            console.error('Failed to initialize database:', error);
            // Fallback to localStorage
            this.useLocalStorage = true;
        }
    }

    openDB() {
        return new Promise((resolve, reject) => {
            const request = indexedDB.open(this.dbName, this.dbVersion);

            request.onerror = () => reject(request.error);
            request.onsuccess = () => resolve(request.result);

            request.onupgradeneeded = (event) => {
                const db = event.target.result;

                // Orders store
                if (!db.objectStoreNames.contains('orders')) {
                    const ordersStore = db.createObjectStore('orders', { keyPath: 'id', autoIncrement: true });
                    ordersStore.createIndex('clientName', 'clientName', { unique: false });
                    ordersStore.createIndex('customerName', 'customerName', { unique: false });
                    ordersStore.createIndex('status', 'status', { unique: false });
                    ordersStore.createIndex('city', 'city', { unique: false });
                    ordersStore.createIndex('date', 'date', { unique: false });
                    ordersStore.createIndex('driverId', 'driverId', { unique: false });
                    ordersStore.createIndex('trackingNumber', 'trackingNumber', { unique: true, multiEntry: false });
                } else {
                    // Add tracking number index if it doesn't exist (for existing databases)
                    const transaction = event.target.transaction;
                    if (transaction) {
                        const ordersStore = transaction.objectStore('orders');
                        if (!ordersStore.indexNames.contains('trackingNumber')) {
                            ordersStore.createIndex('trackingNumber', 'trackingNumber', { unique: true, multiEntry: false });
                        }
                    }
                }

                // Drivers store
                if (!db.objectStoreNames.contains('drivers')) {
                    const driversStore = db.createObjectStore('drivers', { keyPath: 'id', autoIncrement: true });
                    driversStore.createIndex('name', 'name', { unique: false });
                    driversStore.createIndex('phone', 'phone', { unique: true });
                    driversStore.createIndex('city', 'city', { unique: false });
                }

                // Settings store
                if (!db.objectStoreNames.contains('settings')) {
                    db.createObjectStore('settings', { keyPath: 'key' });
                }

                // Handle commission system upgrade from percentage to fixed amount
                if (event.oldVersion < 4) {
                    this.upgradeCommissionSystem(event);
                }

                // Companies store
                if (!db.objectStoreNames.contains('companies')) {
                    const companiesStore = db.createObjectStore('companies', { keyPath: 'id', autoIncrement: true });
                    companiesStore.createIndex('name', 'name', { unique: true });
                }

                // Payments store
                if (!db.objectStoreNames.contains('payments')) {
                    const paymentsStore = db.createObjectStore('payments', { keyPath: 'id', autoIncrement: true });
                    paymentsStore.createIndex('companyName', 'companyName', { unique: false });
                    paymentsStore.createIndex('paymentDate', 'paymentDate', { unique: false });
                }

                // Customers store
                if (!db.objectStoreNames.contains('customers')) {
                    const customersStore = db.createObjectStore('customers', { keyPath: 'id', autoIncrement: true });
                    customersStore.createIndex('name', 'name', { unique: false });
                    customersStore.createIndex('type', 'type', { unique: false });
                    customersStore.createIndex('phone', 'phone', { unique: false });
                    customersStore.createIndex('city', 'city', { unique: false });
                    customersStore.createIndex('email', 'email', { unique: false });
                    customersStore.createIndex('status', 'status', { unique: false });
                    customersStore.createIndex('createdAt', 'createdAt', { unique: false });
                }
            };
        });
    }

    // Generic CRUD operations
    async add(storeName, data) {
        if (this.useLocalStorage) {
            return this.addToLocalStorage(storeName, data);
        }

        const transaction = this.db.transaction([storeName], 'readwrite');
        const store = transaction.objectStore(storeName);
        
        return new Promise((resolve, reject) => {
            const request = store.add(data);
            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
        });
    }

    async getAll(storeName) {
        if (this.useLocalStorage) {
            return this.getAllFromLocalStorage(storeName);
        }

        const transaction = this.db.transaction([storeName], 'readonly');
        const store = transaction.objectStore(storeName);
        
        return new Promise((resolve, reject) => {
            const request = store.getAll();
            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
        });
    }

    async getById(storeName, id) {
        if (this.useLocalStorage) {
            return this.getByIdFromLocalStorage(storeName, id);
        }

        const transaction = this.db.transaction([storeName], 'readonly');
        const store = transaction.objectStore(storeName);
        
        return new Promise((resolve, reject) => {
            const request = store.get(id);
            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
        });
    }

    async update(storeName, data) {
        if (this.useLocalStorage) {
            return this.updateInLocalStorage(storeName, data);
        }

        const transaction = this.db.transaction([storeName], 'readwrite');
        const store = transaction.objectStore(storeName);
        
        return new Promise((resolve, reject) => {
            const request = store.put(data);
            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
        });
    }

    async delete(storeName, id) {
        if (this.useLocalStorage) {
            return this.deleteFromLocalStorage(storeName, id);
        }

        const transaction = this.db.transaction([storeName], 'readwrite');
        const store = transaction.objectStore(storeName);
        
        return new Promise((resolve, reject) => {
            const request = store.delete(id);
            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
        });
    }

    // Search functionality
    async search(storeName, indexName, value) {
        if (this.useLocalStorage) {
            return this.searchInLocalStorage(storeName, indexName, value);
        }

        const transaction = this.db.transaction([storeName], 'readonly');
        const store = transaction.objectStore(storeName);
        const index = store.index(indexName);
        
        return new Promise((resolve, reject) => {
            const request = index.getAll(value);
            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
        });
    }

    // LocalStorage fallback methods
    addToLocalStorage(storeName, data) {
        const items = this.getAllFromLocalStorage(storeName);
        const newId = Math.max(...items.map(item => item.id || 0), 0) + 1;
        data.id = newId;
        items.push(data);
        localStorage.setItem(storeName, JSON.stringify(items));
        return Promise.resolve(newId);
    }

    getAllFromLocalStorage(storeName) {
        const data = localStorage.getItem(storeName);
        return data ? JSON.parse(data) : [];
    }

    getByIdFromLocalStorage(storeName, id) {
        const items = this.getAllFromLocalStorage(storeName);
        return Promise.resolve(items.find(item => item.id === id));
    }

    updateInLocalStorage(storeName, data) {
        const items = this.getAllFromLocalStorage(storeName);
        const index = items.findIndex(item => item.id === data.id);
        if (index !== -1) {
            items[index] = data;
            localStorage.setItem(storeName, JSON.stringify(items));
        }
        return Promise.resolve(data.id);
    }

    deleteFromLocalStorage(storeName, id) {
        const items = this.getAllFromLocalStorage(storeName);
        const filteredItems = items.filter(item => item.id !== id);
        localStorage.setItem(storeName, JSON.stringify(filteredItems));
        return Promise.resolve();
    }

    searchInLocalStorage(storeName, field, value) {
        const items = this.getAllFromLocalStorage(storeName);
        const results = items.filter(item => 
            item[field] && item[field].toString().toLowerCase().includes(value.toLowerCase())
        );
        return Promise.resolve(results);
    }

    // Backup and restore
    async exportData() {
        const data = {};
        const stores = ['orders', 'drivers', 'companies', 'customers', 'payments', 'settings'];
        
        for (const store of stores) {
            data[store] = await this.getAll(store);
        }
        
        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = `delivery-system-backup-${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }

    async importData(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = async (e) => {
                try {
                    const data = JSON.parse(e.target.result);
                    
                    // Clear existing data
                    const stores = ['orders', 'drivers', 'companies', 'customers', 'payments', 'settings'];
                    for (const store of stores) {
                        if (this.useLocalStorage) {
                            localStorage.removeItem(store);
                        } else {
                            const transaction = this.db.transaction([store], 'readwrite');
                            const objectStore = transaction.objectStore(store);
                            await new Promise((res, rej) => {
                                const clearRequest = objectStore.clear();
                                clearRequest.onsuccess = () => res();
                                clearRequest.onerror = () => rej(clearRequest.error);
                            });
                        }
                    }
                    
                    // Import new data
                    for (const [storeName, items] of Object.entries(data)) {
                        if (Array.isArray(items)) {
                            for (const item of items) {
                                await this.add(storeName, item);
                            }
                        }
                    }
                    
                    resolve();
                } catch (error) {
                    reject(error);
                }
            };
            reader.onerror = () => reject(reader.error);
            reader.readAsText(file);
        });
    }

    // Settings management
    async getSetting(key, defaultValue = null) {
        try {
            const setting = await this.getById('settings', key);
            return setting ? setting.value : defaultValue;
        } catch (error) {
            return defaultValue;
        }
    }

    async setSetting(key, value) {
        return await this.update('settings', { key, value });
    }

    // Check if tracking number exists
    async isTrackingNumberExists(trackingNumber, excludeOrderId = null) {
        if (!trackingNumber) return false;

        try {
            if (this.useLocalStorage) {
                const orders = this.getAllFromLocalStorage('orders');
                return orders.some(order =>
                    order.trackingNumber === trackingNumber &&
                    order.id !== excludeOrderId
                );
            }

            const transaction = this.db.transaction(['orders'], 'readonly');
            const store = transaction.objectStore('orders');
            const index = store.index('trackingNumber');

            return new Promise((resolve, reject) => {
                const request = index.get(trackingNumber);
                request.onsuccess = () => {
                    const result = request.result;
                    if (!result) {
                        resolve(false);
                    } else if (excludeOrderId && result.id === excludeOrderId) {
                        resolve(false);
                    } else {
                        resolve(true);
                    }
                };
                request.onerror = () => reject(request.error);
            });
        } catch (error) {
            console.error('Error checking tracking number:', error);
            return false;
        }
    }

    // Upgrade commission system from percentage to fixed amount
    upgradeCommissionSystem(event) {
        try {
            console.log('Upgrading commission system from percentage to fixed amount...');

            const transaction = event.target.transaction;
            if (!transaction) return;

            const driversStore = transaction.objectStore('drivers');

            // Get all drivers and convert them
            const request = driversStore.getAll();
            request.onsuccess = () => {
                const drivers = request.result;
                let updateCount = 0;

                drivers.forEach(driver => {
                    if (driver.commissionRate !== undefined && driver.fixedCommission === undefined) {
                        // Convert percentage to fixed amount (example: 10% -> 1000 IQD)
                        const fixedCommission = Math.round(driver.commissionRate * 100);

                        driver.fixedCommission = fixedCommission;
                        // Keep old field for backward compatibility

                        // Update driver record
                        const updateRequest = driversStore.put(driver);
                        updateRequest.onsuccess = () => {
                            updateCount++;
                            if (updateCount === drivers.length) {
                                console.log(`Upgraded ${updateCount} drivers to fixed commission system`);
                            }
                        };
                    }
                });
            };

            request.onerror = () => {
                console.error('Error reading drivers for upgrade:', request.error);
            };
        } catch (error) {
            console.error('Error upgrading commission system:', error);
        }
    }
}

// Initialize storage manager
const storage = new StorageManager();

// Export for use in other modules
window.storage = storage;
