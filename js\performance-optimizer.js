// Performance Optimization and PWA Features
class PerformanceOptimizer {
    constructor() {
        this.isOnline = navigator.onLine;
        this.performanceMetrics = {};
        this.lazyLoadObserver = null;
        this.init();
    }

    async init() {
        this.setupPerformanceMonitoring();
        this.setupNetworkMonitoring();
        this.setupLazyLoading();
        this.setupServiceWorker();
        this.setupPWAFeatures();
        this.optimizeInitialLoad();
    }

    setupPerformanceMonitoring() {
        // Monitor Core Web Vitals
        this.measureCLS();
        this.measureFID();
        this.measureLCP();
        this.measureTTFB();
        
        // Monitor custom metrics
        this.measurePageLoadTime();
        this.measureDOMContentLoaded();
        this.measureResourceLoadTimes();
    }

    measureCLS() {
        // Cumulative Layout Shift
        let clsValue = 0;
        let clsEntries = [];

        const observer = new PerformanceObserver((list) => {
            for (const entry of list.getEntries()) {
                if (!entry.hadRecentInput) {
                    clsValue += entry.value;
                    clsEntries.push(entry);
                }
            }
            this.performanceMetrics.cls = clsValue;
        });

        observer.observe({ type: 'layout-shift', buffered: true });
    }

    measureFID() {
        // First Input Delay
        const observer = new PerformanceObserver((list) => {
            for (const entry of list.getEntries()) {
                this.performanceMetrics.fid = entry.processingStart - entry.startTime;
                break;
            }
        });

        observer.observe({ type: 'first-input', buffered: true });
    }

    measureLCP() {
        // Largest Contentful Paint
        const observer = new PerformanceObserver((list) => {
            const entries = list.getEntries();
            const lastEntry = entries[entries.length - 1];
            this.performanceMetrics.lcp = lastEntry.startTime;
        });

        observer.observe({ type: 'largest-contentful-paint', buffered: true });
    }

    measureTTFB() {
        // Time to First Byte
        const navigationEntry = performance.getEntriesByType('navigation')[0];
        if (navigationEntry) {
            this.performanceMetrics.ttfb = navigationEntry.responseStart - navigationEntry.requestStart;
        }
    }

    measurePageLoadTime() {
        window.addEventListener('load', () => {
            const loadTime = performance.now();
            this.performanceMetrics.pageLoadTime = loadTime;
            console.log(`Page Load Time: ${loadTime.toFixed(2)}ms`);
        });
    }

    measureDOMContentLoaded() {
        document.addEventListener('DOMContentLoaded', () => {
            const domLoadTime = performance.now();
            this.performanceMetrics.domContentLoaded = domLoadTime;
            console.log(`DOM Content Loaded: ${domLoadTime.toFixed(2)}ms`);
        });
    }

    measureResourceLoadTimes() {
        window.addEventListener('load', () => {
            const resources = performance.getEntriesByType('resource');
            const slowResources = resources.filter(resource => resource.duration > 1000);
            
            if (slowResources.length > 0) {
                console.warn('Slow loading resources detected:', slowResources);
            }
            
            this.performanceMetrics.resourceCount = resources.length;
            this.performanceMetrics.slowResourceCount = slowResources.length;
        });
    }

    setupNetworkMonitoring() {
        // Monitor online/offline status
        window.addEventListener('online', () => {
            this.isOnline = true;
            this.handleOnlineStatus();
        });

        window.addEventListener('offline', () => {
            this.isOnline = false;
            this.handleOfflineStatus();
        });

        // Monitor connection quality
        if ('connection' in navigator) {
            const connection = navigator.connection;
            this.monitorConnectionQuality(connection);
            
            connection.addEventListener('change', () => {
                this.monitorConnectionQuality(connection);
            });
        }
    }

    handleOnlineStatus() {
        console.log('Application is online');
        
        // Show online indicator
        this.showNetworkStatus('online', 'متصل بالإنترنت');
        
        // Sync pending data
        this.syncPendingData();
        
        // Enable real-time features
        this.enableRealTimeFeatures();
    }

    handleOfflineStatus() {
        console.log('Application is offline');
        
        // Show offline indicator
        this.showNetworkStatus('offline', 'غير متصل بالإنترنت - وضع عدم الاتصال');
        
        // Enable offline mode
        this.enableOfflineMode();
    }

    monitorConnectionQuality(connection) {
        const quality = this.getConnectionQuality(connection);
        console.log(`Connection quality: ${quality}`);
        
        // Adjust features based on connection quality
        this.adjustFeaturesForConnection(quality);
    }

    getConnectionQuality(connection) {
        const effectiveType = connection.effectiveType;
        const downlink = connection.downlink;
        const rtt = connection.rtt;

        if (effectiveType === '4g' && downlink > 10) {
            return 'excellent';
        } else if (effectiveType === '4g' || (effectiveType === '3g' && downlink > 1.5)) {
            return 'good';
        } else if (effectiveType === '3g' || effectiveType === '2g') {
            return 'poor';
        } else {
            return 'unknown';
        }
    }

    adjustFeaturesForConnection(quality) {
        const body = document.body;
        
        // Remove existing quality classes
        body.classList.remove('connection-excellent', 'connection-good', 'connection-poor');
        
        // Add current quality class
        body.classList.add(`connection-${quality}`);
        
        // Adjust features based on quality
        switch (quality) {
            case 'poor':
                this.enableDataSavingMode();
                break;
            case 'good':
                this.enableStandardMode();
                break;
            case 'excellent':
                this.enableEnhancedMode();
                break;
        }
    }

    enableDataSavingMode() {
        console.log('Enabling data saving mode');
        
        // Reduce image quality
        this.optimizeImages(0.7);
        
        // Disable auto-refresh
        this.disableAutoRefresh();
        
        // Reduce animation complexity
        document.body.classList.add('reduced-animations');
    }

    enableStandardMode() {
        console.log('Enabling standard mode');
        
        // Standard image quality
        this.optimizeImages(0.85);
        
        // Enable limited auto-refresh
        this.enableLimitedAutoRefresh();
        
        // Standard animations
        document.body.classList.remove('reduced-animations', 'enhanced-animations');
    }

    enableEnhancedMode() {
        console.log('Enabling enhanced mode');
        
        // High image quality
        this.optimizeImages(1.0);
        
        // Enable full auto-refresh
        this.enableFullAutoRefresh();
        
        // Enhanced animations
        document.body.classList.add('enhanced-animations');
    }

    setupLazyLoading() {
        // Lazy load images and content
        this.lazyLoadObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    this.loadElement(entry.target);
                    this.lazyLoadObserver.unobserve(entry.target);
                }
            });
        }, {
            rootMargin: '50px'
        });

        // Observe lazy load elements
        document.querySelectorAll('[data-lazy]').forEach(element => {
            this.lazyLoadObserver.observe(element);
        });
    }

    loadElement(element) {
        if (element.dataset.src) {
            element.src = element.dataset.src;
            element.removeAttribute('data-src');
        }
        
        if (element.dataset.lazy === 'content') {
            this.loadLazyContent(element);
        }
        
        element.classList.add('loaded');
    }

    async loadLazyContent(element) {
        const contentType = element.dataset.contentType;
        
        switch (contentType) {
            case 'reports':
                await this.loadReportsContent(element);
                break;
            case 'charts':
                await this.loadChartsContent(element);
                break;
            case 'statistics':
                await this.loadStatisticsContent(element);
                break;
        }
    }

    async setupServiceWorker() {
        if ('serviceWorker' in navigator) {
            try {
                const registration = await navigator.serviceWorker.register('/sw.js');
                console.log('Service Worker registered successfully:', registration);
                
                // Handle service worker updates
                registration.addEventListener('updatefound', () => {
                    const newWorker = registration.installing;
                    newWorker.addEventListener('statechange', () => {
                        if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                            this.showUpdateAvailable();
                        }
                    });
                });
                
            } catch (error) {
                console.error('Service Worker registration failed:', error);
            }
        }
    }

    setupPWAFeatures() {
        // Add to home screen prompt
        this.setupInstallPrompt();
        
        // Handle app shortcuts
        this.handleAppShortcuts();
        
        // Setup push notifications
        this.setupPushNotifications();
        
        // Handle app lifecycle
        this.setupAppLifecycle();
    }

    setupInstallPrompt() {
        let deferredPrompt;
        
        window.addEventListener('beforeinstallprompt', (e) => {
            e.preventDefault();
            deferredPrompt = e;
            this.showInstallButton(deferredPrompt);
        });
        
        window.addEventListener('appinstalled', () => {
            console.log('PWA was installed');
            this.hideInstallButton();
        });
    }

    showInstallButton(deferredPrompt) {
        const installButton = document.createElement('button');
        installButton.className = 'install-app-btn btn-primary';
        installButton.innerHTML = '<i class="fas fa-download"></i> تثبيت التطبيق';
        
        installButton.addEventListener('click', async () => {
            deferredPrompt.prompt();
            const { outcome } = await deferredPrompt.userChoice;
            console.log(`User response to install prompt: ${outcome}`);
            deferredPrompt = null;
            this.hideInstallButton();
        });
        
        const header = document.querySelector('.header-actions');
        if (header) {
            header.appendChild(installButton);
        }
    }

    hideInstallButton() {
        const installButton = document.querySelector('.install-app-btn');
        if (installButton) {
            installButton.remove();
        }
    }

    handleAppShortcuts() {
        // Handle URL parameters for shortcuts
        const urlParams = new URLSearchParams(window.location.search);
        const action = urlParams.get('action');
        const section = urlParams.get('section');
        
        if (action === 'new-order') {
            // Open new order modal
            setTimeout(() => {
                if (window.ordersManager) {
                    window.ordersManager.showAddOrderModal();
                }
            }, 1000);
        }
        
        if (section) {
            // Navigate to specific section
            setTimeout(() => {
                if (window.app) {
                    window.app.showSection(section);
                }
            }, 500);
        }
    }

    async setupPushNotifications() {
        if ('Notification' in window && 'serviceWorker' in navigator) {
            const permission = await Notification.requestPermission();
            
            if (permission === 'granted') {
                console.log('Push notifications enabled');
                this.subscribeToPushNotifications();
            }
        }
    }

    async subscribeToPushNotifications() {
        try {
            const registration = await navigator.serviceWorker.ready;
            const subscription = await registration.pushManager.subscribe({
                userVisibleOnly: true,
                applicationServerKey: this.urlBase64ToUint8Array('YOUR_VAPID_PUBLIC_KEY')
            });
            
            console.log('Push subscription:', subscription);
            // Send subscription to server
        } catch (error) {
            console.error('Failed to subscribe to push notifications:', error);
        }
    }

    urlBase64ToUint8Array(base64String) {
        const padding = '='.repeat((4 - base64String.length % 4) % 4);
        const base64 = (base64String + padding)
            .replace(/-/g, '+')
            .replace(/_/g, '/');
        
        const rawData = window.atob(base64);
        const outputArray = new Uint8Array(rawData.length);
        
        for (let i = 0; i < rawData.length; ++i) {
            outputArray[i] = rawData.charCodeAt(i);
        }
        return outputArray;
    }

    setupAppLifecycle() {
        // Handle app visibility changes
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.handleAppHidden();
            } else {
                this.handleAppVisible();
            }
        });
        
        // Handle page unload
        window.addEventListener('beforeunload', () => {
            this.handleAppUnload();
        });
    }

    handleAppHidden() {
        console.log('App hidden - pausing non-essential operations');
        
        // Pause auto-refresh
        this.pauseAutoRefresh();
        
        // Save current state
        this.saveAppState();
    }

    handleAppVisible() {
        console.log('App visible - resuming operations');
        
        // Resume auto-refresh
        this.resumeAutoRefresh();
        
        // Check for updates
        this.checkForUpdates();
    }

    handleAppUnload() {
        // Save any pending data
        this.savePendingData();
        
        // Send analytics
        this.sendAnalytics();
    }

    optimizeInitialLoad() {
        // Preload critical resources
        this.preloadCriticalResources();
        
        // Defer non-critical scripts
        this.deferNonCriticalScripts();
        
        // Optimize font loading
        this.optimizeFontLoading();
        
        // Enable resource hints
        this.enableResourceHints();
    }

    preloadCriticalResources() {
        const criticalResources = [
            '/styles/main.css',
            '/styles/neumorphic.css',
            '/js/main.js',
            '/js/storage.js'
        ];
        
        criticalResources.forEach(resource => {
            const link = document.createElement('link');
            link.rel = 'preload';
            link.href = resource;
            link.as = resource.endsWith('.css') ? 'style' : 'script';
            document.head.appendChild(link);
        });
    }

    showNetworkStatus(status, message) {
        // Remove existing status
        const existingStatus = document.querySelector('.network-status');
        if (existingStatus) {
            existingStatus.remove();
        }
        
        // Create status indicator
        const statusElement = document.createElement('div');
        statusElement.className = `network-status ${status}`;
        statusElement.innerHTML = `
            <i class="fas fa-${status === 'online' ? 'wifi' : 'wifi-slash'}"></i>
            <span>${message}</span>
        `;
        
        document.body.appendChild(statusElement);
        
        // Auto-hide after 3 seconds for online status
        if (status === 'online') {
            setTimeout(() => {
                statusElement.remove();
            }, 3000);
        }
    }

    showUpdateAvailable() {
        const updateBanner = document.createElement('div');
        updateBanner.className = 'update-banner';
        updateBanner.innerHTML = `
            <div class="update-content">
                <i class="fas fa-download"></i>
                <span>تحديث جديد متاح</span>
                <button class="btn-primary update-btn">تحديث الآن</button>
                <button class="btn-secondary dismiss-btn">لاحقاً</button>
            </div>
        `;
        
        updateBanner.querySelector('.update-btn').addEventListener('click', () => {
            window.location.reload();
        });
        
        updateBanner.querySelector('.dismiss-btn').addEventListener('click', () => {
            updateBanner.remove();
        });
        
        document.body.appendChild(updateBanner);
    }

    // Placeholder methods for features to be implemented
    syncPendingData() { console.log('Syncing pending data...'); }
    enableRealTimeFeatures() { console.log('Enabling real-time features...'); }
    enableOfflineMode() { console.log('Enabling offline mode...'); }
    optimizeImages(quality) { console.log(`Optimizing images at ${quality} quality`); }
    disableAutoRefresh() { console.log('Disabling auto-refresh'); }
    enableLimitedAutoRefresh() { console.log('Enabling limited auto-refresh'); }
    enableFullAutoRefresh() { console.log('Enabling full auto-refresh'); }
    loadReportsContent(element) { console.log('Loading reports content'); }
    loadChartsContent(element) { console.log('Loading charts content'); }
    loadStatisticsContent(element) { console.log('Loading statistics content'); }
    pauseAutoRefresh() { console.log('Pausing auto-refresh'); }
    resumeAutoRefresh() { console.log('Resuming auto-refresh'); }
    saveAppState() { console.log('Saving app state'); }
    checkForUpdates() { console.log('Checking for updates'); }
    savePendingData() { console.log('Saving pending data'); }
    sendAnalytics() { console.log('Sending analytics'); }
    deferNonCriticalScripts() { console.log('Deferring non-critical scripts'); }
    optimizeFontLoading() { console.log('Optimizing font loading'); }
    enableResourceHints() { console.log('Enabling resource hints'); }
}

// Initialize performance optimizer
const performanceOptimizer = new PerformanceOptimizer();

// Export for use in other modules
window.performanceOptimizer = performanceOptimizer;
