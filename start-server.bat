@echo off
echo Starting Delivery Management System Server...
echo.

REM Try Python 3 first
python --version >nul 2>&1
if %errorlevel% == 0 (
    echo Using Python 3...
    echo Server will be available at: http://localhost:8000
    echo Press Ctrl+C to stop the server
    echo.
    python -m http.server 8000
    goto :end
)

REM Try Python 2 if Python 3 is not available
python2 --version >nul 2>&1
if %errorlevel% == 0 (
    echo Using Python 2...
    echo Server will be available at: http://localhost:8000
    echo Press Ctrl+C to stop the server
    echo.
    python2 -m SimpleHTTPServer 8000
    goto :end
)

REM Try py command (Windows Python Launcher)
py --version >nul 2>&1
if %errorlevel% == 0 (
    echo Using Python via py launcher...
    echo Server will be available at: http://localhost:8000
    echo Press Ctrl+C to stop the server
    echo.
    py -m http.server 8000
    goto :end
)

echo Error: Python is not installed or not in PATH
echo Please install Python from https://python.org
echo.
pause

:end
