@echo off
echo ========================================
echo    نظام إدارة شركة التوصيل
echo    Delivery Management System Server
echo ========================================
echo.

REM Try Node.js first (recommended)
node --version >nul 2>&1
if %errorlevel% == 0 (
    echo ✅ Using Node.js...
    echo 🌐 Server will be available at: http://localhost:8000
    echo 🛑 Press Ctrl+C to stop the server
    echo.
    node server.js
    goto :end
)

REM Try Python 3
python --version >nul 2>&1
if %errorlevel% == 0 (
    echo ✅ Using Python 3...
    echo 🌐 Server will be available at: http://localhost:8000
    echo 🛑 Press Ctrl+C to stop the server
    echo.
    python -m http.server 8000
    goto :end
)

REM Try Python 2 if Python 3 is not available
python2 --version >nul 2>&1
if %errorlevel% == 0 (
    echo ✅ Using Python 2...
    echo 🌐 Server will be available at: http://localhost:8000
    echo 🛑 Press Ctrl+C to stop the server
    echo.
    python2 -m SimpleHTTPServer 8000
    goto :end
)

REM Try py command (Windows Python Launcher)
py --version >nul 2>&1
if %errorlevel% == 0 (
    echo ✅ Using Python via py launcher...
    echo 🌐 Server will be available at: http://localhost:8000
    echo 🛑 Press Ctrl+C to stop the server
    echo.
    py -m http.server 8000
    goto :end
)

echo ❌ Error: No suitable server found!
echo.
echo Please install one of the following:
echo 1. Node.js (recommended): https://nodejs.org
echo 2. Python: https://python.org
echo.
echo Alternative solutions:
echo - Use VS Code Live Server extension
echo - Use XAMPP/WAMP and copy files to htdocs
echo - Open comprehensive-test.html directly in browser
echo.
pause

:end
