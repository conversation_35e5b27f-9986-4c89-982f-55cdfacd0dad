// Drivers Management System
class DriversManager {
    constructor() {
        this.drivers = [];
        this.currentEditingDriver = null;
        this.init();
    }

    async init() {
        this.setupEventListeners();
        await this.loadDrivers();
    }

    setupEventListeners() {
        // Add driver button
        const addDriverBtn = document.getElementById('addDriverBtn');
        if (addDriverBtn) {
            addDriverBtn.addEventListener('click', () => this.showAddDriverModal());
        }
    }

    async loadDrivers() {
        try {
            this.drivers = await storage.getAll('drivers');
            await this.renderDrivers();
        } catch (error) {
            console.error('Error loading drivers:', error);
            window.app.showNotification('خطأ في تحميل المندوبين', 'error');
        }
    }

    async renderDrivers() {
        const container = document.getElementById('driversGrid');
        if (!container) return;

        if (this.drivers.length === 0) {
            container.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-users fa-3x"></i>
                    <h3>لا يوجد مندوبين</h3>
                    <p>ابدأ بإضافة مندوب جديد</p>
                    <button class="btn-primary" onclick="driversManager.showAddDriverModal()">
                        <i class="fas fa-plus"></i>
                        إضافة مندوب
                    </button>
                </div>
            `;
            return;
        }

        // Get orders for each driver to calculate stats
        const orders = await storage.getAll('orders');
        
        container.innerHTML = this.drivers.map(driver => {
            const driverOrders = orders.filter(order => order.driverId == driver.id);
            const deliveredOrders = driverOrders.filter(order => order.status === 'delivered');
            const totalCommission = deliveredOrders.length * (driver.fixedCommission || driver.commissionRate * 100 || 1000);

            return `
                <div class="driver-card">
                    <div class="driver-header">
                        <div class="driver-avatar">
                            ${driver.name.charAt(0)}
                        </div>
                        <div class="driver-info">
                            <h3>${driver.name}</h3>
                            <p><i class="fas fa-phone"></i> ${driver.phone}</p>
                            <p><i class="fas fa-map-marker-alt"></i> ${driver.city}</p>
                        </div>
                    </div>
                    
                    <div class="driver-stats">
                        <div class="driver-stat">
                            <div class="driver-stat-value">${driverOrders.length}</div>
                            <div class="driver-stat-label">إجمالي الطلبات</div>
                        </div>
                        <div class="driver-stat">
                            <div class="driver-stat-value">${deliveredOrders.length}</div>
                            <div class="driver-stat-label">طلبات مسلمة</div>
                        </div>
                        <div class="driver-stat">
                            <div class="driver-stat-value">${driver.fixedCommission || driver.commissionRate * 100 || 1000} د.ع</div>
                            <div class="driver-stat-label">العمولة الثابتة</div>
                        </div>
                        <div class="driver-stat">
                            <div class="driver-stat-value">${Math.round(totalCommission)}</div>
                            <div class="driver-stat-label">العمولة (د.ع)</div>
                        </div>
                    </div>

                    <div class="driver-performance">
                        <div class="performance-item">
                            <span>معدل النجاح:</span>
                            <span class="performance-value">
                                ${driverOrders.length > 0 ? Math.round((deliveredOrders.length / driverOrders.length) * 100) : 0}%
                            </span>
                        </div>
                    </div>

                    <div class="driver-actions">
                        <button class="btn-secondary" onclick="driversManager.editDriver(${driver.id})">
                            <i class="fas fa-edit"></i>
                            تعديل
                        </button>
                        <button class="btn-success" onclick="driversManager.viewDriverOrders(${driver.id})">
                            <i class="fas fa-list"></i>
                            الطلبات
                        </button>
                        <button class="btn-danger" onclick="driversManager.deleteDriver(${driver.id})">
                            <i class="fas fa-trash"></i>
                            حذف
                        </button>
                    </div>
                </div>
            `;
        }).join('');
    }

    showAddDriverModal() {
        const modalContent = `
            <div class="modal">
                <div class="modal-header">
                    <h3 class="modal-title">إضافة مندوب جديد</h3>
                    <button class="modal-close">&times;</button>
                </div>
                <div class="modal-body">
                    <form id="driverForm">
                        <div class="form-group">
                            <label class="form-label">اسم المندوب *</label>
                            <input type="text" class="form-input" name="name" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">رقم الهاتف *</label>
                            <input type="tel" class="form-input" name="phone" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">المحافظة/المنطقة *</label>
                            <select class="form-select" name="city" required>
                                <option value="">اختر المحافظة</option>
                                <option value="بغداد">بغداد</option>
                                <option value="البصرة">البصرة</option>
                                <option value="أربيل">أربيل</option>
                                <option value="النجف">النجف</option>
                                <option value="كربلاء">كربلاء</option>
                                <option value="الموصل">الموصل</option>
                                <option value="السليمانية">السليمانية</option>
                                <option value="دهوك">دهوك</option>
                                <option value="الأنبار">الأنبار</option>
                                <option value="بابل">بابل</option>
                                <option value="ديالى">ديالى</option>
                                <option value="ذي قار">ذي قار</option>
                                <option value="المثنى">المثنى</option>
                                <option value="القادسية">القادسية</option>
                                <option value="واسط">واسط</option>
                                <option value="ميسان">ميسان</option>
                                <option value="كركوك">كركوك</option>
                                <option value="صلاح الدين">صلاح الدين</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">العمولة الثابتة (د.ع) *</label>
                            <input type="number" class="form-input" name="fixedCommission" min="0" step="100" required placeholder="مثال: 1000">
                            <small class="form-help">المبلغ الثابت الذي يحصل عليه المندوب لكل طلب مُسلم</small>
                        </div>
                        <div class="form-group">
                            <label class="form-label">العنوان</label>
                            <textarea class="form-textarea" name="address"></textarea>
                        </div>
                        <div class="form-group">
                            <label class="form-label">ملاحظات</label>
                            <textarea class="form-textarea" name="notes"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn-secondary modal-close">إلغاء</button>
                    <button type="button" class="btn-primary" onclick="driversManager.saveDriver()">حفظ المندوب</button>
                </div>
            </div>
        `;

        window.app.showModal(modalContent);
    }

    async saveDriver() {
        const form = document.getElementById('driverForm');
        const formData = new FormData(form);
        
        if (!form.checkValidity()) {
            form.reportValidity();
            return;
        }

        const driverData = {
            name: formData.get('name'),
            phone: formData.get('phone'),
            city: formData.get('city'),
            fixedCommission: parseFloat(formData.get('fixedCommission')),
            address: formData.get('address') || '',
            notes: formData.get('notes') || '',
            createdAt: new Date().toISOString(),
            isActive: true
        };

        try {
            // Check if phone number already exists (for new drivers)
            if (!this.currentEditingDriver) {
                const existingDrivers = await storage.search('drivers', 'phone', driverData.phone);
                if (existingDrivers.length > 0) {
                    window.app.showNotification('رقم الهاتف مستخدم بالفعل', 'error');
                    return;
                }
            }

            if (this.currentEditingDriver) {
                driverData.id = this.currentEditingDriver.id;
                driverData.createdAt = this.currentEditingDriver.createdAt;
                await storage.update('drivers', driverData);
                window.app.showNotification('تم تحديث المندوب بنجاح', 'success');
            } else {
                await storage.add('drivers', driverData);
                window.app.showNotification('تم إضافة المندوب بنجاح', 'success');
            }

            this.currentEditingDriver = null;
            window.app.closeModal();
            await this.loadDrivers();
        } catch (error) {
            console.error('Error saving driver:', error);
            window.app.showNotification('خطأ في حفظ المندوب', 'error');
        }
    }

    async editDriver(driverId) {
        try {
            const driver = await storage.getById('drivers', driverId);
            if (!driver) {
                window.app.showNotification('المندوب غير موجود', 'error');
                return;
            }

            this.currentEditingDriver = driver;
            this.showAddDriverModal();

            // Fill form with driver data
            setTimeout(() => {
                const form = document.getElementById('driverForm');
                if (form) {
                    form.name.value = driver.name;
                    form.phone.value = driver.phone;
                    form.city.value = driver.city;
                    form.fixedCommission.value = driver.fixedCommission || driver.commissionRate * 100 || 1000; // Fallback for old data
                    form.address.value = driver.address || '';
                    form.notes.value = driver.notes || '';
                }

                // Update modal title
                const modalTitle = document.querySelector('.modal-title');
                if (modalTitle) {
                    modalTitle.textContent = 'تعديل المندوب';
                }
            }, 100);

        } catch (error) {
            console.error('Error loading driver for edit:', error);
            window.app.showNotification('خطأ في تحميل المندوب', 'error');
        }
    }

    async deleteDriver(driverId) {
        if (!confirm('هل أنت متأكد من حذف هذا المندوب؟\nسيتم إلغاء ربطه بجميع الطلبات المرتبطة به.')) {
            return;
        }

        try {
            // Remove driver from all orders
            const orders = await storage.getAll('orders');
            const driverOrders = orders.filter(order => order.driverId == driverId);
            
            for (const order of driverOrders) {
                order.driverId = null;
                await storage.update('orders', order);
            }

            // Delete driver
            await storage.delete('drivers', driverId);
            window.app.showNotification('تم حذف المندوب بنجاح', 'success');
            await this.loadDrivers();
        } catch (error) {
            console.error('Error deleting driver:', error);
            window.app.showNotification('خطأ في حذف المندوب', 'error');
        }
    }

    async viewDriverOrders(driverId) {
        try {
            const driver = await storage.getById('drivers', driverId);
            const orders = await storage.getAll('orders');
            const driverOrders = orders.filter(order => order.driverId == driverId);

            const modalContent = `
                <div class="modal" style="max-width: 800px;">
                    <div class="modal-header">
                        <h3 class="modal-title">طلبات المندوب: ${driver.name}</h3>
                        <button class="modal-close">&times;</button>
                    </div>
                    <div class="modal-body">
                        ${driverOrders.length === 0 ? 
                            '<p class="text-center">لا توجد طلبات لهذا المندوب</p>' :
                            `<div class="table-container">
                                <table class="data-table">
                                    <thead>
                                        <tr>
                                            <th>رقم الطلب</th>
                                            <th>رقم الوصل</th>
                                            <th>العميل</th>
                                            <th>الزبون</th>
                                            <th>المحافظة</th>
                                            <th>السعر</th>
                                            <th>الحالة</th>
                                            <th>التاريخ</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        ${driverOrders.map(order => `
                                            <tr>
                                                <td>#${order.id}</td>
                                                <td>
                                                    ${order.trackingNumber ?
                                                        `<span class="tracking-number" onclick="navigator.clipboard.writeText('${order.trackingNumber}')" title="انقر للنسخ">${order.trackingNumber}</span>` :
                                                        '<span class="no-tracking">-</span>'
                                                    }
                                                </td>
                                                <td>${order.clientName}</td>
                                                <td>${order.customerName}</td>
                                                <td>${order.city}</td>
                                                <td>${order.deliveryPrice} د.ع</td>
                                                <td>
                                                    <span class="status-badge status-${order.status}">
                                                        ${this.getStatusText(order.status)}
                                                    </span>
                                                </td>
                                                <td>${this.formatDate(order.date)}</td>
                                                <td>
                                                    ${order.status === 'assigned' || order.status === 'pending' ?
                                                        `<button class="btn-secondary btn-sm" onclick="driversManager.reassignOrder(${order.id})" title="إعادة إسناد">
                                                            <i class="fas fa-exchange-alt"></i>
                                                        </button>` : ''
                                                    }
                                                </td>
                                            </tr>
                                        `).join('')}
                                    </tbody>
                                </table>
                            </div>`
                        }
                        
                        <div class="driver-summary">
                            <h4>ملخص الأداء:</h4>
                            <div class="summary-stats">
                                <div class="summary-item">
                                    <span>إجمالي الطلبات:</span>
                                    <strong>${driverOrders.length}</strong>
                                </div>
                                <div class="summary-item">
                                    <span>طلبات مسلمة:</span>
                                    <strong>${driverOrders.filter(o => o.status === 'delivered').length}</strong>
                                </div>
                                <div class="summary-item">
                                    <span>طلبات راجعة:</span>
                                    <strong>${driverOrders.filter(o => o.status === 'returned' || o.status === 'partial_return').length}</strong>
                                </div>
                                <div class="summary-item">
                                    <span>إجمالي العمولة:</span>
                                    <strong>${Math.round(driverOrders.filter(o => o.status === 'delivered').length * (driver.fixedCommission || driver.commissionRate * 100 || 1000))} د.ع</strong>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn-secondary modal-close">إغلاق</button>
                    </div>
                </div>
            `;

            window.app.showModal(modalContent);
        } catch (error) {
            console.error('Error loading driver orders:', error);
            window.app.showNotification('خطأ في تحميل طلبات المندوب', 'error');
        }
    }

    getStatusText(status) {
        const statusMap = {
            'delivered': 'مسلم',
            'pending': 'معلق',
            'returned': 'راجع',
            'partial_return': 'راجع جزئي'
        };
        return statusMap[status] || status;
    }

    async reassignOrder(orderId) {
        try {
            const order = await storage.getById('orders', orderId);
            if (!order) {
                window.app.showNotification('الطلب غير موجود', 'error');
                return;
            }

            // Close current modal and switch to assignment section
            window.app.hideModal();
            window.app.showSection('assignment');

            // Fill the tracking input if order has tracking number
            if (order.trackingNumber && window.assignmentManager) {
                const trackingInput = document.getElementById('assignmentTrackingInput');
                if (trackingInput) {
                    trackingInput.value = order.trackingNumber;
                    await window.assignmentManager.searchByTracking(order.trackingNumber);
                }
            }

            window.app.showNotification('تم تحميل الطلب في قسم الإسناد', 'info');
        } catch (error) {
            console.error('Error in reassign order:', error);
            window.app.showNotification('خطأ في إعادة الإسناد', 'error');
        }
    }

    formatDate(dateString) {
        return new Date(dateString).toLocaleDateString('ar-IQ');
    }
}

// Initialize drivers manager
document.addEventListener('DOMContentLoaded', () => {
    window.driversManager = new DriversManager();
});
