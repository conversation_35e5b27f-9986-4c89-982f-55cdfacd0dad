<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فحص شامل للنظام - نظام إدارة التوصيل</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            direction: rtl;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.2);
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e0e5ec;
        }

        .header h1 {
            color: #2d3748;
            margin-bottom: 10px;
        }

        .header p {
            color: #4a5568;
        }

        .check-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .check-card {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            border: 2px solid transparent;
            transition: all 0.3s ease;
        }

        .check-card.success {
            border-color: #28a745;
            background: #f8fff9;
        }

        .check-card.error {
            border-color: #dc3545;
            background: #fff8f8;
        }

        .check-card.warning {
            border-color: #ffc107;
            background: #fffdf5;
        }

        .check-title {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 15px;
            font-weight: 600;
            color: #2d3748;
        }

        .check-icon {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
        }

        .check-icon.success { background: #28a745; }
        .check-icon.error { background: #dc3545; }
        .check-icon.warning { background: #ffc107; }
        .check-icon.loading { background: #6c757d; }

        .check-details {
            font-size: 14px;
            color: #4a5568;
            line-height: 1.5;
        }

        .check-list {
            list-style: none;
            margin: 10px 0;
        }

        .check-list li {
            padding: 5px 0;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .check-list li::before {
            content: '•';
            color: #28a745;
            font-weight: bold;
        }

        .check-list li.error::before {
            color: #dc3545;
        }

        .check-list li.warning::before {
            color: #ffc107;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
            margin: 5px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        .btn-secondary {
            background: #6c757d;
        }

        .btn-success {
            background: #28a745;
        }

        .btn-danger {
            background: #dc3545;
        }

        .actions {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 2px solid #e0e5ec;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
            margin: 20px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #28a745, #20c997);
            width: 0%;
            transition: width 0.3s ease;
        }

        .log-area {
            background: #000;
            color: #0f0;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            height: 200px;
            overflow-y: auto;
            margin: 20px 0;
            white-space: pre-wrap;
        }

        .status-summary {
            display: flex;
            justify-content: space-around;
            margin: 20px 0;
            text-align: center;
        }

        .status-item {
            padding: 15px;
            border-radius: 10px;
            background: #f8f9fa;
            min-width: 120px;
        }

        .status-number {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .status-label {
            font-size: 12px;
            color: #6c757d;
        }

        @media (max-width: 768px) {
            .check-grid {
                grid-template-columns: 1fr;
            }
            
            .status-summary {
                flex-direction: column;
                gap: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 فحص شامل للنظام</h1>
            <p>تشخيص وفحص جميع مكونات نظام إدارة التوصيل</p>
        </div>

        <div class="progress-bar">
            <div class="progress-fill" id="progressFill"></div>
        </div>

        <div class="status-summary" id="statusSummary">
            <div class="status-item">
                <div class="status-number" id="passedCount">0</div>
                <div class="status-label">نجح</div>
            </div>
            <div class="status-item">
                <div class="status-number" id="failedCount">0</div>
                <div class="status-label">فشل</div>
            </div>
            <div class="status-item">
                <div class="status-number" id="warningCount">0</div>
                <div class="status-label">تحذير</div>
            </div>
            <div class="status-item">
                <div class="status-number" id="totalCount">0</div>
                <div class="status-label">المجموع</div>
            </div>
        </div>

        <div class="check-grid" id="checkGrid">
            <!-- Check cards will be populated by JavaScript -->
        </div>

        <div class="log-area" id="logArea"></div>

        <div class="actions">
            <button class="btn" onclick="runAllChecks()">🔄 إعادة فحص شامل</button>
            <button class="btn btn-secondary" onclick="exportReport()">📄 تصدير التقرير</button>
            <button class="btn btn-success" onclick="window.location.href='index.html'">🏠 الذهاب للنظام</button>
            <button class="btn btn-danger" onclick="clearAllData()">🗑️ مسح البيانات</button>
        </div>
    </div>

    <script>
        let checkResults = [];
        let logArea;

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logMessage = `[${timestamp}] ${message}`;
            console.log(logMessage);
            
            if (logArea) {
                const color = {
                    'info': '#0f0',
                    'error': '#f00',
                    'warning': '#ff0',
                    'success': '#0f0'
                }[type] || '#0f0';
                
                logArea.innerHTML += `<span style="color: ${color}">${logMessage}</span>\n`;
                logArea.scrollTop = logArea.scrollHeight;
            }
        }

        function updateProgress() {
            const total = checkResults.length;
            const completed = checkResults.filter(r => r.status !== 'loading').length;
            const percentage = total > 0 ? (completed / total) * 100 : 0;
            
            document.getElementById('progressFill').style.width = `${percentage}%`;
            
            const passed = checkResults.filter(r => r.status === 'success').length;
            const failed = checkResults.filter(r => r.status === 'error').length;
            const warnings = checkResults.filter(r => r.status === 'warning').length;
            
            document.getElementById('passedCount').textContent = passed;
            document.getElementById('failedCount').textContent = failed;
            document.getElementById('warningCount').textContent = warnings;
            document.getElementById('totalCount').textContent = total;
        }

        function createCheckCard(check) {
            return `
                <div class="check-card ${check.status}" id="check-${check.id}">
                    <div class="check-title">
                        <div class="check-icon ${check.status}">
                            ${check.status === 'success' ? '✓' : 
                              check.status === 'error' ? '✗' : 
                              check.status === 'warning' ? '⚠' : '⏳'}
                        </div>
                        ${check.title}
                    </div>
                    <div class="check-details">
                        ${check.details}
                        ${check.items ? `
                            <ul class="check-list">
                                ${check.items.map(item => `
                                    <li class="${item.status || ''}">${item.text}</li>
                                `).join('')}
                            </ul>
                        ` : ''}
                    </div>
                </div>
            `;
        }

        function updateCheckCard(checkId, updates) {
            const index = checkResults.findIndex(c => c.id === checkId);
            if (index !== -1) {
                checkResults[index] = { ...checkResults[index], ...updates };
                const card = document.getElementById(`check-${checkId}`);
                if (card) {
                    card.outerHTML = createCheckCard(checkResults[index]);
                }
                updateProgress();
            }
        }

        async function checkFiles() {
            log('فحص الملفات...', 'info');
            
            const requiredFiles = [
                'js/storage.js',
                'js/main.js',
                'js/orders.js',
                'js/drivers.js',
                'js/customers.js',
                'styles/main.css',
                'styles/neumorphic.css'
            ];
            
            const items = [];
            let allFound = true;
            
            for (const file of requiredFiles) {
                try {
                    const response = await fetch(file, { method: 'HEAD' });
                    if (response.ok) {
                        items.push({ text: `${file} - موجود`, status: 'success' });
                    } else {
                        items.push({ text: `${file} - مفقود`, status: 'error' });
                        allFound = false;
                    }
                } catch (error) {
                    items.push({ text: `${file} - خطأ في التحقق`, status: 'error' });
                    allFound = false;
                }
            }
            
            updateCheckCard('files', {
                status: allFound ? 'success' : 'error',
                details: allFound ? 'جميع الملفات المطلوبة موجودة' : 'بعض الملفات مفقودة',
                items
            });
            
            log(`فحص الملفات: ${allFound ? 'نجح' : 'فشل'}`, allFound ? 'success' : 'error');
        }

        async function checkStorage() {
            log('فحص نظام التخزين...', 'info');
            
            const items = [];
            let storageWorking = true;
            
            try {
                // Test localStorage
                localStorage.setItem('test', 'value');
                const testValue = localStorage.getItem('test');
                localStorage.removeItem('test');
                
                if (testValue === 'value') {
                    items.push({ text: 'localStorage - يعمل', status: 'success' });
                } else {
                    items.push({ text: 'localStorage - لا يعمل', status: 'error' });
                    storageWorking = false;
                }
                
                // Test IndexedDB
                if (window.indexedDB) {
                    items.push({ text: 'IndexedDB - متاح', status: 'success' });
                } else {
                    items.push({ text: 'IndexedDB - غير متاح', status: 'warning' });
                }
                
                // Check if storage module is loaded
                if (window.storage) {
                    items.push({ text: 'Storage Module - محمل', status: 'success' });
                } else {
                    items.push({ text: 'Storage Module - غير محمل', status: 'error' });
                    storageWorking = false;
                }
                
            } catch (error) {
                items.push({ text: `خطأ في الاختبار: ${error.message}`, status: 'error' });
                storageWorking = false;
            }
            
            updateCheckCard('storage', {
                status: storageWorking ? 'success' : 'error',
                details: storageWorking ? 'نظام التخزين يعمل بشكل صحيح' : 'مشاكل في نظام التخزين',
                items
            });
            
            log(`فحص التخزين: ${storageWorking ? 'نجح' : 'فشل'}`, storageWorking ? 'success' : 'error');
        }

        async function checkNavigation() {
            log('فحص التنقل...', 'info');
            
            const items = [];
            let navigationWorking = true;
            
            try {
                const navLinks = document.querySelectorAll('.nav-link');
                const sections = document.querySelectorAll('.content-section');
                
                items.push({ 
                    text: `روابط التنقل: ${navLinks.length}`, 
                    status: navLinks.length > 0 ? 'success' : 'error' 
                });
                
                items.push({ 
                    text: `الأقسام: ${sections.length}`, 
                    status: sections.length > 0 ? 'success' : 'error' 
                });
                
                if (navLinks.length === 0 || sections.length === 0) {
                    navigationWorking = false;
                }
                
                // Check if main app is loaded
                if (window.app || window.deliveryApp) {
                    items.push({ text: 'التطبيق الرئيسي - محمل', status: 'success' });
                } else {
                    items.push({ text: 'التطبيق الرئيسي - غير محمل', status: 'error' });
                    navigationWorking = false;
                }
                
            } catch (error) {
                items.push({ text: `خطأ في فحص التنقل: ${error.message}`, status: 'error' });
                navigationWorking = false;
            }
            
            updateCheckCard('navigation', {
                status: navigationWorking ? 'success' : 'error',
                details: navigationWorking ? 'التنقل يعمل بشكل صحيح' : 'مشاكل في التنقل',
                items
            });
            
            log(`فحص التنقل: ${navigationWorking ? 'نجح' : 'فشل'}`, navigationWorking ? 'success' : 'error');
        }

        async function checkBrowser() {
            log('فحص المتصفح...', 'info');
            
            const items = [];
            let browserSupported = true;
            
            try {
                // Check browser features
                const features = {
                    'ES6 Support': () => {
                        try { eval('const test = () => {}'); return true; } catch { return false; }
                    },
                    'Fetch API': () => typeof fetch !== 'undefined',
                    'Local Storage': () => typeof localStorage !== 'undefined',
                    'CSS Grid': () => CSS.supports('display', 'grid'),
                    'CSS Flexbox': () => CSS.supports('display', 'flex')
                };
                
                for (const [feature, test] of Object.entries(features)) {
                    const supported = test();
                    items.push({ 
                        text: `${feature} - ${supported ? 'مدعوم' : 'غير مدعوم'}`, 
                        status: supported ? 'success' : 'error' 
                    });
                    if (!supported) browserSupported = false;
                }
                
                // Browser info
                items.push({ 
                    text: `المتصفح: ${navigator.userAgent.split(' ').pop()}`, 
                    status: 'info' 
                });
                
            } catch (error) {
                items.push({ text: `خطأ في فحص المتصفح: ${error.message}`, status: 'error' });
                browserSupported = false;
            }
            
            updateCheckCard('browser', {
                status: browserSupported ? 'success' : 'warning',
                details: browserSupported ? 'المتصفح مدعوم بالكامل' : 'بعض الميزات غير مدعومة',
                items
            });
            
            log(`فحص المتصفح: ${browserSupported ? 'نجح' : 'تحذير'}`, browserSupported ? 'success' : 'warning');
        }

        async function checkPerformance() {
            log('فحص الأداء...', 'info');
            
            const items = [];
            let performanceGood = true;
            
            try {
                if (window.performance) {
                    const navigation = performance.getEntriesByType('navigation')[0];
                    
                    const loadTime = navigation.loadEventEnd - navigation.fetchStart;
                    const domTime = navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart;
                    
                    items.push({ 
                        text: `وقت التحميل: ${Math.round(loadTime)}ms`, 
                        status: loadTime < 3000 ? 'success' : loadTime < 5000 ? 'warning' : 'error' 
                    });
                    
                    items.push({ 
                        text: `وقت تحميل DOM: ${Math.round(domTime)}ms`, 
                        status: domTime < 1000 ? 'success' : domTime < 2000 ? 'warning' : 'error' 
                    });
                    
                    if (loadTime > 5000 || domTime > 2000) {
                        performanceGood = false;
                    }
                    
                    // Memory usage (if available)
                    if (performance.memory) {
                        const memoryMB = Math.round(performance.memory.usedJSHeapSize / 1024 / 1024);
                        items.push({ 
                            text: `استخدام الذاكرة: ${memoryMB}MB`, 
                            status: memoryMB < 50 ? 'success' : memoryMB < 100 ? 'warning' : 'error' 
                        });
                    }
                } else {
                    items.push({ text: 'Performance API غير متاح', status: 'warning' });
                }
                
            } catch (error) {
                items.push({ text: `خطأ في فحص الأداء: ${error.message}`, status: 'error' });
                performanceGood = false;
            }
            
            updateCheckCard('performance', {
                status: performanceGood ? 'success' : 'warning',
                details: performanceGood ? 'الأداء جيد' : 'الأداء يحتاج تحسين',
                items
            });
            
            log(`فحص الأداء: ${performanceGood ? 'نجح' : 'تحذير'}`, performanceGood ? 'success' : 'warning');
        }

        async function runAllChecks() {
            logArea = document.getElementById('logArea');
            logArea.innerHTML = '';
            
            log('بدء الفحص الشامل للنظام...', 'info');
            
            // Initialize check results
            checkResults = [
                { id: 'files', title: '📁 فحص الملفات', status: 'loading', details: 'جاري فحص الملفات المطلوبة...' },
                { id: 'storage', title: '💾 فحص التخزين', status: 'loading', details: 'جاري فحص نظام التخزين...' },
                { id: 'navigation', title: '🧭 فحص التنقل', status: 'loading', details: 'جاري فحص نظام التنقل...' },
                { id: 'browser', title: '🌐 فحص المتصفح', status: 'loading', details: 'جاري فحص دعم المتصفح...' },
                { id: 'performance', title: '⚡ فحص الأداء', status: 'loading', details: 'جاري فحص أداء النظام...' }
            ];
            
            // Render initial cards
            const grid = document.getElementById('checkGrid');
            grid.innerHTML = checkResults.map(check => createCheckCard(check)).join('');
            updateProgress();
            
            // Run checks with delays for better UX
            setTimeout(() => checkFiles(), 500);
            setTimeout(() => checkStorage(), 1000);
            setTimeout(() => checkNavigation(), 1500);
            setTimeout(() => checkBrowser(), 2000);
            setTimeout(() => checkPerformance(), 2500);
            
            setTimeout(() => {
                log('اكتمل الفحص الشامل للنظام', 'success');
            }, 3000);
        }

        function exportReport() {
            const report = {
                timestamp: new Date().toISOString(),
                checks: checkResults,
                summary: {
                    total: checkResults.length,
                    passed: checkResults.filter(r => r.status === 'success').length,
                    failed: checkResults.filter(r => r.status === 'error').length,
                    warnings: checkResults.filter(r => r.status === 'warning').length
                }
            };
            
            const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `system-check-report-${new Date().toISOString().split('T')[0]}.json`;
            a.click();
            URL.revokeObjectURL(url);
            
            log('تم تصدير التقرير', 'success');
        }

        function clearAllData() {
            if (confirm('هل أنت متأكد من مسح جميع البيانات؟ هذا الإجراء لا يمكن التراجع عنه.')) {
                localStorage.clear();
                sessionStorage.clear();
                log('تم مسح جميع البيانات', 'warning');
                alert('تم مسح البيانات. سيتم إعادة تحميل الصفحة.');
                location.reload();
            }
        }

        // Auto-run checks on page load
        window.addEventListener('load', () => {
            setTimeout(runAllChecks, 1000);
        });
    </script>
</body>
</html>
