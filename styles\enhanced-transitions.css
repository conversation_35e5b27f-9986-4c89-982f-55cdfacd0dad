/* Enhanced Transitions and Animations */

/* Smooth Section Transitions */
.content-section {
    display: none;
    opacity: 0;
    visibility: hidden;
    transform: translateY(30px);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    will-change: opacity, transform, visibility;
}

.content-section.active {
    display: block;
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

/* Enhanced Fade Animations */
.fade-in {
    animation: enhancedFadeIn 0.5s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.fade-out {
    animation: enhancedFadeOut 0.3s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.slide-in-right {
    animation: enhancedSlideInRight 0.4s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.slide-in-left {
    animation: enhancedSlideInLeft 0.4s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.slide-up {
    animation: enhancedSlideUp 0.4s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.scale-in {
    animation: enhancedScaleIn 0.3s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

/* Keyframe Animations */
@keyframes enhancedFadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes enhancedFadeOut {
    from {
        opacity: 1;
        transform: translateY(0);
    }
    to {
        opacity: 0;
        transform: translateY(-20px);
    }
}

@keyframes enhancedSlideInRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes enhancedSlideInLeft {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes enhancedSlideUp {
    from {
        opacity: 0;
        transform: translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes enhancedScaleIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* Navigation Transitions */
.nav-link {
    position: relative;
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.nav-link:hover::before {
    left: 100%;
}

.nav-link.active {
    transform: translateX(-3px);
}

.nav-link.active::after {
    content: '';
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 60%;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 2px;
    animation: activeIndicator 0.3s ease-out;
}

@keyframes activeIndicator {
    from {
        height: 0;
        opacity: 0;
    }
    to {
        height: 60%;
        opacity: 1;
    }
}

/* Button Hover Effects */
.btn {
    position: relative;
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
}

.btn:hover::before {
    width: 300px;
    height: 300px;
}

.btn:active {
    transform: scale(0.98);
}

/* Loading States */
.loading-section {
    position: relative;
    pointer-events: none;
}

.loading-section::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(224, 229, 236, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
}

.loading-section::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 40px;
    height: 40px;
    border: 4px solid rgba(102, 126, 234, 0.2);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    animation: spin 1s linear infinite;
    z-index: 11;
}

@keyframes spin {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
}

/* Success States */
.success-state {
    animation: successPulse 0.6s ease-out;
}

@keyframes successPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.02); }
    100% { transform: scale(1); }
}

.success-checkmark {
    animation: checkmarkDraw 0.5s ease-out forwards;
}

@keyframes checkmarkDraw {
    0% {
        stroke-dasharray: 0 100;
        stroke-dashoffset: 0;
    }
    100% {
        stroke-dasharray: 100 0;
        stroke-dashoffset: 0;
    }
}

/* Error States */
.error-state {
    animation: errorShake 0.5s ease-in-out;
}

@keyframes errorShake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-10px); }
    75% { transform: translateX(10px); }
}

/* Stagger Animations for Lists */
.stagger-item {
    opacity: 0;
    transform: translateY(20px);
    animation: staggerFadeIn 0.4s ease-out forwards;
}

.stagger-item:nth-child(1) { animation-delay: 0.1s; }
.stagger-item:nth-child(2) { animation-delay: 0.2s; }
.stagger-item:nth-child(3) { animation-delay: 0.3s; }
.stagger-item:nth-child(4) { animation-delay: 0.4s; }
.stagger-item:nth-child(5) { animation-delay: 0.5s; }

@keyframes staggerFadeIn {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Mobile Optimizations */
@media (max-width: 768px) {
    .content-section {
        transform: translateY(20px);
    }
    
    .nav-link {
        transition: all 0.2s ease;
    }
    
    .btn::before {
        transition: width 0.3s, height 0.3s;
    }
    
    /* Reduce motion for better performance on mobile */
    @media (prefers-reduced-motion: reduce) {
        .content-section,
        .nav-link,
        .btn {
            transition: none;
        }
        
        .fade-in,
        .fade-out,
        .slide-in-right,
        .slide-in-left,
        .slide-up,
        .scale-in {
            animation: none;
        }
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .nav-link::before {
        background: linear-gradient(90deg, transparent, rgba(0,0,0,0.3), transparent);
    }
    
    .btn::before {
        background: rgba(0, 0, 0, 0.1);
    }
}

/* Dark mode transitions */
@media (prefers-color-scheme: dark) {
    .nav-link::before {
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
    }
    
    .loading-section::after {
        background: rgba(45, 55, 72, 0.8);
    }
}

/* Focus states for accessibility */
.nav-link:focus,
.btn:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

.nav-link:focus-visible,
.btn:focus-visible {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* Smooth scrolling for anchor links */
html {
    scroll-behavior: smooth;
}

/* Enhanced hover states */
.interactive-element {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.interactive-element:hover {
    transform: translateY(-2px);
    box-shadow: 
        0 10px 25px rgba(0, 0, 0, 0.1),
        0 6px 10px rgba(0, 0, 0, 0.1);
}

.interactive-element:active {
    transform: translateY(0);
    transition: transform 0.1s;
}
