// Advanced Financial Reports System
class AdvancedReportsManager {
    constructor() {
        this.reportTypes = {
            client: {
                name: 'تقرير العميل/الشركة',
                icon: 'fas fa-building',
                description: 'تقرير مفصل عن أداء عميل أو شركة محددة'
            },
            driver: {
                name: 'تقرير المندوب',
                icon: 'fas fa-user-tie',
                description: 'تقرير أداء وعمولات المندوب'
            },
            comprehensive: {
                name: 'التقرير الشامل',
                icon: 'fas fa-chart-line',
                description: 'تقرير شامل عن جميع العمليات والإحصائيات'
            },
            financial: {
                name: 'التقرير المالي',
                icon: 'fas fa-coins',
                description: 'تقرير مالي مفصل عن الإيرادات والخسائر'
            },
            performance: {
                name: 'تقرير الأداء',
                icon: 'fas fa-chart-bar',
                description: 'تقرير أداء العمليات ومعدلات النجاح'
            }
        };
        this.currentReport = null;
        this.init();
    }

    async init() {
        this.setupEventListeners();
        this.renderReportsInterface();
    }

    setupEventListeners() {
        // Report generation buttons
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('generate-report-btn')) {
                const reportType = e.target.dataset.reportType;
                this.showReportConfigModal(reportType);
            }
            
            if (e.target.classList.contains('export-pdf-btn')) {
                this.exportToPDF();
            }
            
            if (e.target.classList.contains('export-excel-btn')) {
                this.exportToExcel();
            }
            
            if (e.target.classList.contains('print-report-btn')) {
                this.printReport();
            }
        });
    }

    renderReportsInterface() {
        const container = document.getElementById('advancedReportsContainer');
        if (!container) return;

        const interfaceHTML = `
            <div class="reports-dashboard">
                <div class="reports-header">
                    <h3>التقارير المالية المتقدمة</h3>
                    <div class="reports-actions">
                        <button class="btn-secondary" onclick="advancedReports.refreshData()">
                            <i class="fas fa-sync-alt"></i>
                            تحديث البيانات
                        </button>
                    </div>
                </div>

                <div class="report-types-grid">
                    ${Object.entries(this.reportTypes).map(([type, config]) => `
                        <div class="report-type-card">
                            <div class="report-icon">
                                <i class="${config.icon}"></i>
                            </div>
                            <div class="report-content">
                                <h4>${config.name}</h4>
                                <p>${config.description}</p>
                                <button class="generate-report-btn btn-primary" data-report-type="${type}">
                                    إنشاء التقرير
                                </button>
                            </div>
                        </div>
                    `).join('')}
                </div>

                <div class="current-report-container" id="currentReportContainer" style="display: none;">
                    <div class="report-header">
                        <h4 id="currentReportTitle">التقرير الحالي</h4>
                        <div class="report-actions">
                            <button class="print-report-btn btn-outline">
                                <i class="fas fa-print"></i>
                                طباعة
                            </button>
                            <button class="export-pdf-btn btn-secondary">
                                <i class="fas fa-file-pdf"></i>
                                تصدير PDF
                            </button>
                            <button class="export-excel-btn btn-success">
                                <i class="fas fa-file-excel"></i>
                                تصدير Excel
                            </button>
                        </div>
                    </div>
                    <div class="report-content" id="reportContent">
                        <!-- Report content will be generated here -->
                    </div>
                </div>
            </div>
        `;

        container.innerHTML = interfaceHTML;
    }

    showReportConfigModal(reportType) {
        const config = this.reportTypes[reportType];
        
        const modalContent = `
            <div class="modal report-config-modal">
                <div class="modal-header">
                    <h3 class="modal-title">${config.name}</h3>
                    <button class="modal-close">&times;</button>
                </div>
                <div class="modal-body">
                    <form id="reportConfigForm">
                        <input type="hidden" name="reportType" value="${reportType}">
                        
                        <div class="config-section">
                            <h4>فترة التقرير</h4>
                            <div class="form-row">
                                <div class="form-group">
                                    <label class="form-label">من تاريخ</label>
                                    <input type="date" class="form-input" name="dateFrom" required>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">إلى تاريخ</label>
                                    <input type="date" class="form-input" name="dateTo" required>
                                </div>
                            </div>
                            <div class="quick-periods">
                                <button type="button" class="period-btn" data-period="today">اليوم</button>
                                <button type="button" class="period-btn" data-period="week">هذا الأسبوع</button>
                                <button type="button" class="period-btn" data-period="month">هذا الشهر</button>
                                <button type="button" class="period-btn" data-period="quarter">هذا الربع</button>
                                <button type="button" class="period-btn" data-period="year">هذا العام</button>
                            </div>
                        </div>

                        ${this.getSpecificConfigFields(reportType)}

                        <div class="config-section">
                            <h4>خيارات التقرير</h4>
                            <div class="checkbox-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" name="includeCharts" checked>
                                    <span>تضمين الرسوم البيانية</span>
                                </label>
                                <label class="checkbox-label">
                                    <input type="checkbox" name="includeDetails" checked>
                                    <span>تضمين التفاصيل</span>
                                </label>
                                <label class="checkbox-label">
                                    <input type="checkbox" name="includeSummary" checked>
                                    <span>تضمين الملخص</span>
                                </label>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn-secondary modal-close">إلغاء</button>
                    <button type="button" class="btn-primary" onclick="advancedReports.generateReport()">إنشاء التقرير</button>
                </div>
            </div>
        `;

        window.app.showModal(modalContent);
        this.setupConfigModal();
    }

    getSpecificConfigFields(reportType) {
        switch (reportType) {
            case 'client':
                return `
                    <div class="config-section">
                        <h4>اختيار العميل</h4>
                        <div class="form-group">
                            <label class="form-label">العميل/الشركة</label>
                            <select class="form-select" name="clientId" required>
                                <option value="">اختر العميل</option>
                                <!-- Will be populated dynamically -->
                            </select>
                        </div>
                    </div>
                `;
            case 'driver':
                return `
                    <div class="config-section">
                        <h4>اختيار المندوب</h4>
                        <div class="form-group">
                            <label class="form-label">المندوب</label>
                            <select class="form-select" name="driverId" required>
                                <option value="">اختر المندوب</option>
                                <!-- Will be populated dynamically -->
                            </select>
                        </div>
                    </div>
                `;
            case 'financial':
                return `
                    <div class="config-section">
                        <h4>خيارات التقرير المالي</h4>
                        <div class="checkbox-group">
                            <label class="checkbox-label">
                                <input type="checkbox" name="includeCommissions" checked>
                                <span>تضمين العمولات</span>
                            </label>
                            <label class="checkbox-label">
                                <input type="checkbox" name="includeExpenses">
                                <span>تضمين المصروفات</span>
                            </label>
                            <label class="checkbox-label">
                                <input type="checkbox" name="groupByMonth" checked>
                                <span>تجميع حسب الشهر</span>
                            </label>
                        </div>
                    </div>
                `;
            default:
                return '';
        }
    }

    async setupConfigModal() {
        // Setup period buttons
        document.querySelectorAll('.period-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                const period = btn.dataset.period;
                this.setDatePeriod(period);
            });
        });

        // Populate dropdowns
        await this.populateDropdowns();
    }

    setDatePeriod(period) {
        const dateFrom = document.querySelector('input[name="dateFrom"]');
        const dateTo = document.querySelector('input[name="dateTo"]');
        
        const today = new Date();
        const todayStr = today.toISOString().split('T')[0];
        
        switch (period) {
            case 'today':
                dateFrom.value = todayStr;
                dateTo.value = todayStr;
                break;
            case 'week':
                const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
                dateFrom.value = weekAgo.toISOString().split('T')[0];
                dateTo.value = todayStr;
                break;
            case 'month':
                const monthStart = new Date(today.getFullYear(), today.getMonth(), 1);
                dateFrom.value = monthStart.toISOString().split('T')[0];
                dateTo.value = todayStr;
                break;
            case 'quarter':
                const quarterStart = new Date(today.getFullYear(), Math.floor(today.getMonth() / 3) * 3, 1);
                dateFrom.value = quarterStart.toISOString().split('T')[0];
                dateTo.value = todayStr;
                break;
            case 'year':
                const yearStart = new Date(today.getFullYear(), 0, 1);
                dateFrom.value = yearStart.toISOString().split('T')[0];
                dateTo.value = todayStr;
                break;
        }
    }

    async populateDropdowns() {
        try {
            // Populate clients
            const clientSelect = document.querySelector('select[name="clientId"]');
            if (clientSelect) {
                const customers = await storage.getAll('customers');
                customers.forEach(customer => {
                    const option = document.createElement('option');
                    option.value = customer.id;
                    option.textContent = customer.name;
                    clientSelect.appendChild(option);
                });
            }

            // Populate drivers
            const driverSelect = document.querySelector('select[name="driverId"]');
            if (driverSelect) {
                const drivers = await storage.getAll('drivers');
                drivers.forEach(driver => {
                    const option = document.createElement('option');
                    option.value = driver.id;
                    option.textContent = driver.name;
                    driverSelect.appendChild(option);
                });
            }
        } catch (error) {
            console.error('Error populating dropdowns:', error);
        }
    }

    async generateReport() {
        const form = document.getElementById('reportConfigForm');
        if (!form || !form.checkValidity()) {
            form?.reportValidity();
            return;
        }

        const formData = new FormData(form);
        const config = {};
        for (let [key, value] of formData.entries()) {
            if (form.querySelector(`[name="${key}"]`).type === 'checkbox') {
                config[key] = form.querySelector(`[name="${key}"]`).checked;
            } else {
                config[key] = value;
            }
        }

        try {
            window.app.showNotification('جاري إنشاء التقرير...', 'info');
            
            const reportData = await this.fetchReportData(config);
            this.currentReport = { config, data: reportData };
            
            this.renderReport(config.reportType, reportData, config);
            window.app.closeModal();
            
            window.app.showNotification('تم إنشاء التقرير بنجاح', 'success');
        } catch (error) {
            console.error('Error generating report:', error);
            window.app.showNotification('خطأ في إنشاء التقرير', 'error');
        }
    }

    async fetchReportData(config) {
        const orders = await storage.getAll('orders');
        const drivers = await storage.getAll('drivers');
        const customers = await storage.getAll('customers');

        // Filter orders by date range
        const filteredOrders = orders.filter(order => {
            const orderDate = new Date(order.createdAt || order.date);
            const fromDate = new Date(config.dateFrom);
            const toDate = new Date(config.dateTo + 'T23:59:59');
            return orderDate >= fromDate && orderDate <= toDate;
        });

        return {
            orders: filteredOrders,
            allOrders: orders,
            drivers,
            customers,
            period: {
                from: config.dateFrom,
                to: config.dateTo
            }
        };
    }

    renderReport(reportType, data, config) {
        const container = document.getElementById('currentReportContainer');
        const titleElement = document.getElementById('currentReportTitle');
        const contentElement = document.getElementById('reportContent');

        if (!container || !titleElement || !contentElement) return;

        titleElement.textContent = this.reportTypes[reportType].name;
        
        let reportHTML = '';
        
        switch (reportType) {
            case 'client':
                reportHTML = this.generateClientReport(data, config);
                break;
            case 'driver':
                reportHTML = this.generateDriverReport(data, config);
                break;
            case 'comprehensive':
                reportHTML = this.generateComprehensiveReport(data, config);
                break;
            case 'financial':
                reportHTML = this.generateFinancialReport(data, config);
                break;
            case 'performance':
                reportHTML = this.generatePerformanceReport(data, config);
                break;
        }

        contentElement.innerHTML = reportHTML;
        container.style.display = 'block';
        
        // Scroll to report
        container.scrollIntoView({ behavior: 'smooth' });
    }

    generateClientReport(data, config) {
        const client = data.customers.find(c => c.id == config.clientId);
        if (!client) return '<p>العميل غير موجود</p>';

        const clientOrders = data.orders.filter(order => order.customerId == config.clientId);
        
        const stats = {
            total: clientOrders.length,
            delivered: clientOrders.filter(o => o.status === 'delivered').length,
            returned: clientOrders.filter(o => o.status === 'returned').length,
            pending: clientOrders.filter(o => o.status === 'pending').length,
            totalRevenue: clientOrders.reduce((sum, o) => sum + (o.deliveryPrice || 0), 0),
            deliveredRevenue: clientOrders.filter(o => o.status === 'delivered').reduce((sum, o) => sum + (o.deliveryPrice || 0), 0)
        };

        return `
            <div class="report-content">
                <div class="report-summary">
                    <h3>تقرير العميل: ${client.name}</h3>
                    <div class="period-info">
                        <span>الفترة: من ${config.dateFrom} إلى ${config.dateTo}</span>
                    </div>
                </div>

                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-value">${stats.total}</div>
                        <div class="stat-label">إجمالي الطلبات</div>
                    </div>
                    <div class="stat-item success">
                        <div class="stat-value">${stats.delivered}</div>
                        <div class="stat-label">طلبات مسلمة</div>
                    </div>
                    <div class="stat-item danger">
                        <div class="stat-value">${stats.returned}</div>
                        <div class="stat-label">طلبات راجعة</div>
                    </div>
                    <div class="stat-item warning">
                        <div class="stat-value">${stats.pending}</div>
                        <div class="stat-label">طلبات معلقة</div>
                    </div>
                </div>

                <div class="financial-summary">
                    <h4>الملخص المالي</h4>
                    <div class="financial-grid">
                        <div class="financial-item">
                            <span class="label">إجمالي قيمة الطلبات:</span>
                            <span class="value">${this.formatCurrency(stats.totalRevenue)}</span>
                        </div>
                        <div class="financial-item">
                            <span class="label">الإيرادات المحققة:</span>
                            <span class="value success">${this.formatCurrency(stats.deliveredRevenue)}</span>
                        </div>
                        <div class="financial-item">
                            <span class="label">معدل النجاح:</span>
                            <span class="value">${stats.total > 0 ? ((stats.delivered / stats.total) * 100).toFixed(1) : 0}%</span>
                        </div>
                    </div>
                </div>

                ${config.includeDetails ? this.generateOrdersTable(clientOrders) : ''}
            </div>
        `;
    }

    generateDriverReport(data, config) {
        const driver = data.drivers.find(d => d.id == config.driverId);
        if (!driver) return '<p>المندوب غير موجود</p>';

        const driverOrders = data.orders.filter(order => order.driverId == config.driverId);
        
        const stats = {
            total: driverOrders.length,
            delivered: driverOrders.filter(o => o.status === 'delivered').length,
            returned: driverOrders.filter(o => o.status === 'returned').length,
            commission: driverOrders.filter(o => o.status === 'delivered').length * (driver.commissionAmount || 0)
        };

        return `
            <div class="report-content">
                <div class="report-summary">
                    <h3>تقرير المندوب: ${driver.name}</h3>
                    <div class="period-info">
                        <span>الفترة: من ${config.dateFrom} إلى ${config.dateTo}</span>
                    </div>
                </div>

                <div class="driver-info">
                    <div class="info-grid">
                        <div class="info-item">
                            <span class="label">رقم الهاتف:</span>
                            <span class="value">${driver.phone}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">المنطقة:</span>
                            <span class="value">${driver.city}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">العمولة الثابتة:</span>
                            <span class="value">${this.formatCurrency(driver.commissionAmount || 0)}</span>
                        </div>
                    </div>
                </div>

                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-value">${stats.total}</div>
                        <div class="stat-label">إجمالي الطلبات</div>
                    </div>
                    <div class="stat-item success">
                        <div class="stat-value">${stats.delivered}</div>
                        <div class="stat-label">طلبات مسلمة</div>
                    </div>
                    <div class="stat-item danger">
                        <div class="stat-value">${stats.returned}</div>
                        <div class="stat-label">طلبات راجعة</div>
                    </div>
                    <div class="stat-item primary">
                        <div class="stat-value">${this.formatCurrency(stats.commission)}</div>
                        <div class="stat-label">إجمالي العمولة</div>
                    </div>
                </div>

                <div class="performance-metrics">
                    <h4>مؤشرات الأداء</h4>
                    <div class="metrics-grid">
                        <div class="metric-item">
                            <span class="label">معدل النجاح:</span>
                            <span class="value">${stats.total > 0 ? ((stats.delivered / stats.total) * 100).toFixed(1) : 0}%</span>
                        </div>
                        <div class="metric-item">
                            <span class="label">متوسط الطلبات اليومية:</span>
                            <span class="value">${this.calculateDailyAverage(driverOrders, config)}</span>
                        </div>
                    </div>
                </div>

                ${config.includeDetails ? this.generateOrdersTable(driverOrders) : ''}
            </div>
        `;
    }

    generateOrdersTable(orders) {
        if (orders.length === 0) {
            return '<div class="empty-state"><p>لا توجد طلبات في هذه الفترة</p></div>';
        }

        return `
            <div class="orders-table-container">
                <h4>تفاصيل الطلبات</h4>
                <table class="report-table">
                    <thead>
                        <tr>
                            <th>رقم الطلب</th>
                            <th>رقم الوصل</th>
                            <th>العميل</th>
                            <th>الزبون</th>
                            <th>المحافظة</th>
                            <th>السعر</th>
                            <th>الحالة</th>
                            <th>التاريخ</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${orders.map(order => `
                            <tr>
                                <td>#${order.id}</td>
                                <td>${order.trackingNumber || '-'}</td>
                                <td>${order.clientName}</td>
                                <td>${order.recipientName || order.customerName}</td>
                                <td>${order.governorate || order.city}</td>
                                <td>${this.formatCurrency(order.deliveryPrice)}</td>
                                <td><span class="status-badge status-${order.status}">${this.getStatusText(order.status)}</span></td>
                                <td>${new Date(order.createdAt || order.date).toLocaleDateString('ar-IQ')}</td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>
        `;
    }

    formatCurrency(amount) {
        return new Intl.NumberFormat('ar-IQ', {
            style: 'currency',
            currency: 'IQD',
            minimumFractionDigits: 0
        }).format(amount || 0);
    }

    getStatusText(status) {
        const statusMap = {
            'pending': 'معلق',
            'delivered': 'مسلم',
            'returned': 'راجع',
            'partial': 'راجع جزئي',
            'delayed': 'مؤجل'
        };
        return statusMap[status] || status;
    }

    calculateDailyAverage(orders, config) {
        const fromDate = new Date(config.dateFrom);
        const toDate = new Date(config.dateTo);
        const daysDiff = Math.ceil((toDate - fromDate) / (1000 * 60 * 60 * 24)) + 1;
        return (orders.length / daysDiff).toFixed(1);
    }

    generateComprehensiveReport(data, config) {
        const stats = {
            totalOrders: data.orders.length,
            delivered: data.orders.filter(o => o.status === 'delivered').length,
            returned: data.orders.filter(o => o.status === 'returned').length,
            pending: data.orders.filter(o => o.status === 'pending').length,
            totalRevenue: data.orders.reduce((sum, o) => sum + (o.deliveryPrice || 0), 0),
            deliveredRevenue: data.orders.filter(o => o.status === 'delivered').reduce((sum, o) => sum + (o.deliveryPrice || 0), 0),
            totalDrivers: data.drivers.length,
            totalCustomers: data.customers.length
        };

        // Group orders by governorate
        const governorateStats = {};
        data.orders.forEach(order => {
            const gov = order.governorate || order.city || 'غير محدد';
            if (!governorateStats[gov]) {
                governorateStats[gov] = { count: 0, revenue: 0 };
            }
            governorateStats[gov].count++;
            governorateStats[gov].revenue += order.deliveryPrice || 0;
        });

        return `
            <div class="report-content">
                <div class="report-summary">
                    <h3>التقرير الشامل</h3>
                    <div class="period-info">
                        <span>الفترة: من ${config.dateFrom} إلى ${config.dateTo}</span>
                    </div>
                </div>

                <div class="comprehensive-stats">
                    <div class="stats-section">
                        <h4>إحصائيات الطلبات</h4>
                        <div class="stats-grid">
                            <div class="stat-item">
                                <div class="stat-value">${stats.totalOrders}</div>
                                <div class="stat-label">إجمالي الطلبات</div>
                            </div>
                            <div class="stat-item success">
                                <div class="stat-value">${stats.delivered}</div>
                                <div class="stat-label">طلبات مسلمة</div>
                            </div>
                            <div class="stat-item danger">
                                <div class="stat-value">${stats.returned}</div>
                                <div class="stat-label">طلبات راجعة</div>
                            </div>
                            <div class="stat-item warning">
                                <div class="stat-value">${stats.pending}</div>
                                <div class="stat-label">طلبات معلقة</div>
                            </div>
                        </div>
                    </div>

                    <div class="stats-section">
                        <h4>الإحصائيات المالية</h4>
                        <div class="financial-grid">
                            <div class="financial-item">
                                <span class="label">إجمالي الإيرادات:</span>
                                <span class="value">${this.formatCurrency(stats.totalRevenue)}</span>
                            </div>
                            <div class="financial-item">
                                <span class="label">الإيرادات المحققة:</span>
                                <span class="value success">${this.formatCurrency(stats.deliveredRevenue)}</span>
                            </div>
                            <div class="financial-item">
                                <span class="label">معدل النجاح:</span>
                                <span class="value">${stats.totalOrders > 0 ? ((stats.delivered / stats.totalOrders) * 100).toFixed(1) : 0}%</span>
                            </div>
                        </div>
                    </div>

                    <div class="stats-section">
                        <h4>إحصائيات عامة</h4>
                        <div class="general-stats">
                            <div class="stat-item">
                                <span class="label">عدد المندوبين:</span>
                                <span class="value">${stats.totalDrivers}</span>
                            </div>
                            <div class="stat-item">
                                <span class="label">عدد العملاء:</span>
                                <span class="value">${stats.totalCustomers}</span>
                            </div>
                            <div class="stat-item">
                                <span class="label">متوسط الطلبات اليومية:</span>
                                <span class="value">${this.calculateDailyAverage(data.orders, config)}</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="governorate-distribution">
                    <h4>توزيع الطلبات حسب المحافظة</h4>
                    <div class="distribution-table">
                        <table class="report-table">
                            <thead>
                                <tr>
                                    <th>المحافظة</th>
                                    <th>عدد الطلبات</th>
                                    <th>الإيرادات</th>
                                    <th>النسبة</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${Object.entries(governorateStats)
                                    .sort((a, b) => b[1].count - a[1].count)
                                    .map(([gov, data]) => `
                                        <tr>
                                            <td>${gov}</td>
                                            <td>${data.count}</td>
                                            <td>${this.formatCurrency(data.revenue)}</td>
                                            <td>${((data.count / stats.totalOrders) * 100).toFixed(1)}%</td>
                                        </tr>
                                    `).join('')}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        `;
    }

    generateFinancialReport(data, config) {
        const monthlyData = {};

        data.orders.forEach(order => {
            const date = new Date(order.createdAt || order.date);
            const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;

            if (!monthlyData[monthKey]) {
                monthlyData[monthKey] = {
                    revenue: 0,
                    delivered: 0,
                    returned: 0,
                    commissions: 0
                };
            }

            monthlyData[monthKey].revenue += order.deliveryPrice || 0;
            if (order.status === 'delivered') {
                monthlyData[monthKey].delivered += order.deliveryPrice || 0;
                // Calculate commission (assuming fixed amount per delivery)
                const driver = data.drivers.find(d => d.id === order.driverId);
                if (driver) {
                    monthlyData[monthKey].commissions += driver.commissionAmount || 0;
                }
            } else if (order.status === 'returned') {
                monthlyData[monthKey].returned += order.deliveryPrice || 0;
            }
        });

        const totalStats = {
            revenue: Object.values(monthlyData).reduce((sum, month) => sum + month.revenue, 0),
            delivered: Object.values(monthlyData).reduce((sum, month) => sum + month.delivered, 0),
            returned: Object.values(monthlyData).reduce((sum, month) => sum + month.returned, 0),
            commissions: Object.values(monthlyData).reduce((sum, month) => sum + month.commissions, 0)
        };

        return `
            <div class="report-content">
                <div class="report-summary">
                    <h3>التقرير المالي</h3>
                    <div class="period-info">
                        <span>الفترة: من ${config.dateFrom} إلى ${config.dateTo}</span>
                    </div>
                </div>

                <div class="financial-overview">
                    <h4>نظرة عامة مالية</h4>
                    <div class="financial-grid">
                        <div class="financial-item">
                            <span class="label">إجمالي الإيرادات:</span>
                            <span class="value">${this.formatCurrency(totalStats.revenue)}</span>
                        </div>
                        <div class="financial-item success">
                            <span class="label">الإيرادات المحققة:</span>
                            <span class="value">${this.formatCurrency(totalStats.delivered)}</span>
                        </div>
                        <div class="financial-item danger">
                            <span class="label">الخسائر (راجعة):</span>
                            <span class="value">${this.formatCurrency(totalStats.returned)}</span>
                        </div>
                        <div class="financial-item warning">
                            <span class="label">إجمالي العمولات:</span>
                            <span class="value">${this.formatCurrency(totalStats.commissions)}</span>
                        </div>
                        <div class="financial-item primary">
                            <span class="label">صافي الربح:</span>
                            <span class="value">${this.formatCurrency(totalStats.delivered - totalStats.commissions)}</span>
                        </div>
                    </div>
                </div>

                ${config.groupByMonth ? `
                    <div class="monthly-breakdown">
                        <h4>التفصيل الشهري</h4>
                        <table class="report-table">
                            <thead>
                                <tr>
                                    <th>الشهر</th>
                                    <th>إجمالي الإيرادات</th>
                                    <th>الإيرادات المحققة</th>
                                    <th>الخسائر</th>
                                    <th>العمولات</th>
                                    <th>صافي الربح</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${Object.entries(monthlyData)
                                    .sort((a, b) => a[0].localeCompare(b[0]))
                                    .map(([month, data]) => `
                                        <tr>
                                            <td>${month}</td>
                                            <td>${this.formatCurrency(data.revenue)}</td>
                                            <td>${this.formatCurrency(data.delivered)}</td>
                                            <td>${this.formatCurrency(data.returned)}</td>
                                            <td>${this.formatCurrency(data.commissions)}</td>
                                            <td>${this.formatCurrency(data.delivered - data.commissions)}</td>
                                        </tr>
                                    `).join('')}
                            </tbody>
                        </table>
                    </div>
                ` : ''}
            </div>
        `;
    }

    generatePerformanceReport(data, config) {
        const driverPerformance = {};

        data.drivers.forEach(driver => {
            const driverOrders = data.orders.filter(order => order.driverId === driver.id);
            driverPerformance[driver.id] = {
                name: driver.name,
                total: driverOrders.length,
                delivered: driverOrders.filter(o => o.status === 'delivered').length,
                returned: driverOrders.filter(o => o.status === 'returned').length,
                successRate: driverOrders.length > 0 ? ((driverOrders.filter(o => o.status === 'delivered').length / driverOrders.length) * 100).toFixed(1) : 0
            };
        });

        const overallPerformance = {
            totalOrders: data.orders.length,
            delivered: data.orders.filter(o => o.status === 'delivered').length,
            returned: data.orders.filter(o => o.status === 'returned').length,
            pending: data.orders.filter(o => o.status === 'pending').length,
            successRate: data.orders.length > 0 ? ((data.orders.filter(o => o.status === 'delivered').length / data.orders.length) * 100).toFixed(1) : 0
        };

        return `
            <div class="report-content">
                <div class="report-summary">
                    <h3>تقرير الأداء</h3>
                    <div class="period-info">
                        <span>الفترة: من ${config.dateFrom} إلى ${config.dateTo}</span>
                    </div>
                </div>

                <div class="overall-performance">
                    <h4>الأداء العام</h4>
                    <div class="performance-grid">
                        <div class="performance-item">
                            <span class="label">معدل النجاح الإجمالي:</span>
                            <span class="value ${overallPerformance.successRate >= 80 ? 'success' : overallPerformance.successRate >= 60 ? 'warning' : 'danger'}">${overallPerformance.successRate}%</span>
                        </div>
                        <div class="performance-item">
                            <span class="label">متوسط الطلبات اليومية:</span>
                            <span class="value">${this.calculateDailyAverage(data.orders, config)}</span>
                        </div>
                        <div class="performance-item">
                            <span class="label">عدد المندوبين النشطين:</span>
                            <span class="value">${Object.values(driverPerformance).filter(d => d.total > 0).length}</span>
                        </div>
                    </div>
                </div>

                <div class="driver-performance">
                    <h4>أداء المندوبين</h4>
                    <table class="report-table">
                        <thead>
                            <tr>
                                <th>اسم المندوب</th>
                                <th>إجمالي الطلبات</th>
                                <th>طلبات مسلمة</th>
                                <th>طلبات راجعة</th>
                                <th>معدل النجاح</th>
                                <th>التقييم</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${Object.values(driverPerformance)
                                .sort((a, b) => parseFloat(b.successRate) - parseFloat(a.successRate))
                                .map(driver => `
                                    <tr>
                                        <td>${driver.name}</td>
                                        <td>${driver.total}</td>
                                        <td>${driver.delivered}</td>
                                        <td>${driver.returned}</td>
                                        <td>${driver.successRate}%</td>
                                        <td>
                                            <span class="performance-badge ${driver.successRate >= 80 ? 'excellent' : driver.successRate >= 60 ? 'good' : 'needs-improvement'}">
                                                ${driver.successRate >= 80 ? 'ممتاز' : driver.successRate >= 60 ? 'جيد' : 'يحتاج تحسين'}
                                            </span>
                                        </td>
                                    </tr>
                                `).join('')}
                        </tbody>
                    </table>
                </div>
            </div>
        `;
    }

    // Export functions will be implemented in the next part
    exportToPDF() {
        window.app.showNotification('ميزة تصدير PDF قيد التطوير', 'info');
    }

    exportToExcel() {
        window.app.showNotification('ميزة تصدير Excel قيد التطوير', 'info');
    }

    printReport() {
        const reportContent = document.getElementById('reportContent');
        if (!reportContent) {
            window.app.showNotification('لا يوجد تقرير للطباعة', 'warning');
            return;
        }

        const printWindow = window.open('', '_blank');
        printWindow.document.write(`
            <html>
                <head>
                    <title>طباعة التقرير</title>
                    <style>
                        body { font-family: Arial, sans-serif; direction: rtl; }
                        .report-table { width: 100%; border-collapse: collapse; }
                        .report-table th, .report-table td { border: 1px solid #ddd; padding: 8px; text-align: right; }
                        .stats-grid { display: grid; grid-template-columns: repeat(4, 1fr); gap: 1rem; margin: 1rem 0; }
                        .stat-item { text-align: center; padding: 1rem; border: 1px solid #ddd; }
                        @media print { body { margin: 0; } }
                    </style>
                </head>
                <body>
                    ${reportContent.innerHTML}
                </body>
            </html>
        `);
        printWindow.document.close();
        printWindow.print();
    }

    async refreshData() {
        window.app.showNotification('جاري تحديث البيانات...', 'info');
        // Refresh data logic here
        window.app.showNotification('تم تحديث البيانات', 'success');
    }
}

// Initialize advanced reports manager
const advancedReports = new AdvancedReportsManager();

// Export for use in other modules
window.advancedReports = advancedReports;
