// Sample Data for Testing
class SampleDataManager {
    constructor() {
        this.sampleDrivers = [
            {
                name: 'أحمد محمد',
                phone: '07901234567',
                city: 'بغداد',
                commissionAmount: 1000,
                address: 'منطقة الكرادة',
                notes: 'مندوب ممتاز',
                createdAt: new Date().toISOString(),
                isActive: true
            },
            {
                name: 'علي حسن',
                phone: '07801234567',
                city: 'البصرة',
                commissionAmount: 1200,
                address: 'منطقة العشار',
                notes: 'خبرة 3 سنوات',
                createdAt: new Date().toISOString(),
                isActive: true
            },
            {
                name: 'محمد عبدالله',
                phone: '07701234567',
                city: 'أربيل',
                commissionAmount: 800,
                address: 'وسط المدينة',
                notes: 'مندوب جديد',
                createdAt: new Date().toISOString(),
                isActive: true
            }
        ];

        this.sampleCustomers = [
            {
                name: 'شركة الأمل للتجارة',
                type: 'company',
                phone: '07901111111',
                secondaryPhone: '07801111111',
                city: 'بغداد',
                address: 'منطقة الكرادة - شارع أبو نواس',
                email: '<EMAIL>',
                status: 'active',
                notes: 'عميل مميز',
                createdAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString() // 30 days ago
            },
            {
                name: 'مؤسسة النور',
                type: 'company',
                phone: '07903333333',
                city: 'البصرة',
                address: 'منطقة العشار',
                email: '<EMAIL>',
                status: 'active',
                notes: 'مؤسسة خيرية',
                createdAt: new Date(Date.now() - 20 * 24 * 60 * 60 * 1000).toISOString() // 20 days ago
            },
            {
                name: 'متجر الإلكترونيات',
                type: 'company',
                phone: '07906666666',
                city: 'أربيل',
                address: 'منطقة عنكاوا',
                email: '<EMAIL>',
                status: 'active',
                notes: 'متخصص في الأجهزة الإلكترونية',
                createdAt: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000).toISOString() // 15 days ago
            },
            {
                name: 'شركة التقنية المتقدمة',
                type: 'company',
                phone: '07909999999',
                city: 'بغداد',
                address: 'منطقة الكرادة',
                email: '<EMAIL>',
                status: 'active',
                notes: 'شركة تقنية معلومات',
                createdAt: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000).toISOString() // 10 days ago
            },
            {
                name: 'أحمد محمد الخالدي',
                type: 'individual',
                phone: '07705555555',
                city: 'النجف',
                address: 'حي الأنصار',
                status: 'active',
                notes: 'عميل فردي',
                createdAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString() // 5 days ago
            }
        ];

        this.sampleOrders = [
            {
                customerId: null, // Will be set after customers are added
                clientName: 'شركة الأمل للتجارة',
                senderName: 'سارة أحمد',
                senderPhone: '07901111111',
                recipientName: 'فاطمة علي',
                recipientPhone: '07902222222',
                governorate: 'بغداد',
                city: 'بغداد',
                address: 'حي الجامعة - شارع الكندي - بناية 15 - الطابق الثالث - شقة 8',
                deliveryPrice: 5000,
                trackingNumber: 'TRK001234567',
                driverId: null, // Will be set after drivers are added
                notes: 'طلب عاجل - يرجى التوصيل قبل المساء - الزبون متاح بعد الساعة 3 عصراً',
                status: 'delivered',
                date: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(), // 2 days ago
                createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString()
            },
            {
                customerId: null,
                clientName: 'مؤسسة النور',
                senderName: 'خالد محمد',
                senderPhone: '07903333333',
                recipientName: 'أحمد حسين',
                recipientPhone: '07904444444',
                governorate: 'البصرة',
                city: 'البصرة',
                address: 'منطقة العشار - شارع الاستقلال - مجمع النور التجاري - محل رقم 25',
                deliveryPrice: 7000,
                trackingNumber: 'TRK001234568',
                driverId: null,
                notes: 'يفضل التوصيل مساءً بعد الساعة 6 - الزبون يعمل في المحل',
                status: 'pending',
                date: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(), // 1 day ago
                createdAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString()
            },
            {
                customerId: null,
                clientName: 'شركة الأمل للتجارة',
                senderName: 'سارة أحمد',
                senderPhone: '07901111111',
                customerName: 'زينب محمد',
                customerPhone: '07905555555',
                city: 'النجف',
                address: 'حي الأنصار - قرب الجامعة',
                deliveryPrice: 6000,
                trackingNumber: 'TRK001234569',
                driverId: null,
                notes: '',
                status: 'returned',
                date: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(), // 3 days ago
                createdAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString()
            },
            {
                customerId: null,
                clientName: 'متجر الإلكترونيات',
                senderName: 'عمر سالم',
                senderPhone: '07906666666',
                customerName: 'ليلى عبدالله',
                customerPhone: '07907777777',
                city: 'أربيل',
                address: 'منطقة عنكاوا - شارع الجامعة',
                deliveryPrice: 4500,
                trackingNumber: 'TRK001234570',
                driverId: null,
                notes: 'جهاز إلكتروني - يحتاج عناية',
                status: 'delivered',
                date: new Date().toISOString(), // Today
                createdAt: new Date().toISOString()
            },
            {
                customerId: null,
                clientName: 'مؤسسة النور',
                senderName: 'خالد محمد',
                senderPhone: '07903333333',
                customerName: 'حسام الدين',
                customerPhone: '07908888888',
                city: 'كربلاء',
                address: 'حي الحسين - قرب الحرم',
                deliveryPrice: 8000,
                trackingNumber: 'TRK001234571',
                driverId: null,
                notes: 'طلب مهم',
                status: 'partial_return',
                date: new Date(Date.now() - 4 * 24 * 60 * 60 * 1000).toISOString(), // 4 days ago
                createdAt: new Date(Date.now() - 4 * 24 * 60 * 60 * 1000).toISOString()
            },
            {
                customerId: null,
                clientName: 'شركة التقنية المتقدمة',
                senderName: 'نور الهدى',
                senderPhone: '07909999999',
                customerName: 'مصطفى كريم',
                customerPhone: '07900000000',
                city: 'بغداد',
                address: 'منطقة الكرادة - شارع أبو نواس',
                deliveryPrice: 5500,
                trackingNumber: '', // No tracking number for this order
                driverId: null,
                notes: 'عنوان صعب الوصول',
                status: 'pending',
                date: new Date().toISOString(), // Today
                createdAt: new Date().toISOString()
            }
        ];
    }

    async loadSampleData() {
        try {
            // Check if data already exists
            const existingDrivers = await storage.getAll('drivers');
            const existingOrders = await storage.getAll('orders');
            const existingCustomers = await storage.getAll('customers');

            if (existingDrivers.length > 0 || existingOrders.length > 0 || existingCustomers.length > 0) {
                console.log('Sample data already exists');
                return;
            }

            // Add sample customers
            const customerIds = [];
            for (const customer of this.sampleCustomers) {
                const id = await storage.add('customers', customer);
                customerIds.push(id);
            }

            // Add sample drivers
            const driverIds = [];
            for (const driver of this.sampleDrivers) {
                const id = await storage.add('drivers', driver);
                driverIds.push(id);
            }

            // Add sample orders with driver and customer assignments
            for (let i = 0; i < this.sampleOrders.length; i++) {
                const order = { ...this.sampleOrders[i] };

                // Assign customers based on client name
                if (order.clientName === 'شركة الأمل للتجارة' && customerIds[0]) {
                    order.customerId = customerIds[0];
                } else if (order.clientName === 'مؤسسة النور' && customerIds[1]) {
                    order.customerId = customerIds[1];
                } else if (order.clientName === 'متجر الإلكترونيات' && customerIds[2]) {
                    order.customerId = customerIds[2];
                } else if (order.clientName === 'شركة التقنية المتقدمة' && customerIds[3]) {
                    order.customerId = customerIds[3];
                }

                // Assign drivers based on city
                if (order.city === 'بغداد' && driverIds[0]) {
                    order.driverId = driverIds[0];
                } else if (order.city === 'البصرة' && driverIds[1]) {
                    order.driverId = driverIds[1];
                } else if (order.city === 'أربيل' && driverIds[2]) {
                    order.driverId = driverIds[2];
                }

                await storage.add('orders', order);
            }

            // Add some settings
            await storage.setSetting('companyName', 'شركة التوصيل السريع');
            await storage.setSetting('defaultCurrency', 'IQD');

            console.log('Sample data loaded successfully');
            window.app.showNotification('تم تحميل البيانات التجريبية بنجاح', 'success');

            // Refresh the current section
            if (window.app.currentSection === 'dashboard') {
                await window.app.loadDashboardData();
            }

        } catch (error) {
            console.error('Error loading sample data:', error);
            window.app.showNotification('خطأ في تحميل البيانات التجريبية', 'error');
        }
    }

    async clearAllData() {
        if (!confirm('هل أنت متأكد من حذف جميع البيانات؟ هذا الإجراء لا يمكن التراجع عنه.')) {
            return;
        }

        try {
            // Clear all stores
            const stores = ['orders', 'drivers', 'customers', 'companies', 'settings', 'payments'];

            for (const store of stores) {
                const items = await storage.getAll(store);
                for (const item of items) {
                    await storage.delete(store, item.id || item.key);
                }
            }

            console.log('All data cleared successfully');
            window.app.showNotification('تم حذف جميع البيانات بنجاح', 'success');

            // Refresh the page
            setTimeout(() => {
                location.reload();
            }, 1000);

        } catch (error) {
            console.error('Error clearing data:', error);
            window.app.showNotification('خطأ في حذف البيانات', 'error');
        }
    }
}

// Initialize sample data manager
document.addEventListener('DOMContentLoaded', () => {
    window.sampleDataManager = new SampleDataManager();
    
    // Add buttons to load/clear sample data (for development)
    if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
        const devControls = document.createElement('div');
        devControls.style.cssText = `
            position: fixed;
            bottom: 20px;
            left: 20px;
            z-index: 9999;
            display: flex;
            gap: 10px;
            flex-direction: column;
        `;
        
        const loadDataBtn = document.createElement('button');
        loadDataBtn.textContent = 'تحميل بيانات تجريبية';
        loadDataBtn.className = 'btn-primary';
        loadDataBtn.style.fontSize = '12px';
        loadDataBtn.onclick = () => window.sampleDataManager.loadSampleData();
        
        const clearDataBtn = document.createElement('button');
        clearDataBtn.textContent = 'حذف جميع البيانات';
        clearDataBtn.className = 'btn-danger';
        clearDataBtn.style.fontSize = '12px';
        clearDataBtn.onclick = () => window.sampleDataManager.clearAllData();
        
        devControls.appendChild(loadDataBtn);
        devControls.appendChild(clearDataBtn);
        document.body.appendChild(devControls);
    }
});
