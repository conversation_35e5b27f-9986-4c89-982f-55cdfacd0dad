// Advanced Notifications and Alerts System
class NotificationSystem {
    constructor() {
        this.notifications = [];
        this.settings = {
            enableSound: true,
            enableDesktop: true,
            enableEmail: false,
            autoMarkRead: true,
            maxNotifications: 50
        };
        this.init();
    }

    async init() {
        await this.loadSettings();
        await this.loadNotifications();
        this.setupEventListeners();
        this.requestPermissions();
        this.startPeriodicChecks();
        this.renderNotificationCenter();
    }

    setupEventListeners() {
        // Notification center toggle
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('notifications-toggle')) {
                this.toggleNotificationCenter();
            }
            
            if (e.target.classList.contains('mark-all-read-btn')) {
                this.markAllAsRead();
            }
            
            if (e.target.classList.contains('clear-all-btn')) {
                this.clearAllNotifications();
            }
            
            if (e.target.classList.contains('notification-settings-btn')) {
                this.showSettingsModal();
            }
            
            if (e.target.classList.contains('notification-item')) {
                const notificationId = e.target.dataset.notificationId;
                this.markAsRead(notificationId);
            }
        });

        // Listen for system events that should trigger notifications
        document.addEventListener('ordersUpdated', () => {
            this.checkForNewOrders();
        });

        document.addEventListener('orderStatusChanged', (e) => {
            this.notifyOrderStatusChange(e.detail);
        });
    }

    async requestPermissions() {
        if ('Notification' in window && Notification.permission === 'default') {
            await Notification.requestPermission();
        }
    }

    async loadSettings() {
        try {
            const savedSettings = localStorage.getItem('notificationSettings');
            if (savedSettings) {
                this.settings = { ...this.settings, ...JSON.parse(savedSettings) };
            }
        } catch (error) {
            console.error('Error loading notification settings:', error);
        }
    }

    async saveSettings() {
        try {
            localStorage.setItem('notificationSettings', JSON.stringify(this.settings));
        } catch (error) {
            console.error('Error saving notification settings:', error);
        }
    }

    async loadNotifications() {
        try {
            this.notifications = await storage.getAll('notifications') || [];
            this.updateNotificationBadge();
        } catch (error) {
            console.error('Error loading notifications:', error);
        }
    }

    async addNotification(notification) {
        const newNotification = {
            id: Date.now(),
            title: notification.title,
            message: notification.message,
            type: notification.type || 'info', // info, success, warning, error
            priority: notification.priority || 'normal', // low, normal, high, urgent
            category: notification.category || 'general',
            read: false,
            createdAt: new Date().toISOString(),
            data: notification.data || {}
        };

        this.notifications.unshift(newNotification);

        // Limit notifications count
        if (this.notifications.length > this.settings.maxNotifications) {
            this.notifications = this.notifications.slice(0, this.settings.maxNotifications);
        }

        try {
            await storage.add('notifications', newNotification);
        } catch (error) {
            console.error('Error saving notification:', error);
        }

        this.showNotification(newNotification);
        this.updateNotificationBadge();
        this.renderNotificationCenter();
    }

    showNotification(notification) {
        // Show desktop notification
        if (this.settings.enableDesktop && 'Notification' in window && Notification.permission === 'granted') {
            const desktopNotification = new Notification(notification.title, {
                body: notification.message,
                icon: '/favicon.ico',
                tag: notification.id,
                requireInteraction: notification.priority === 'urgent'
            });

            desktopNotification.onclick = () => {
                window.focus();
                this.markAsRead(notification.id);
                desktopNotification.close();
            };

            // Auto close after 5 seconds for non-urgent notifications
            if (notification.priority !== 'urgent') {
                setTimeout(() => {
                    desktopNotification.close();
                }, 5000);
            }
        }

        // Play sound
        if (this.settings.enableSound) {
            this.playNotificationSound(notification.type);
        }

        // Show in-app notification
        this.showInAppNotification(notification);
    }

    showInAppNotification(notification) {
        const notificationElement = document.createElement('div');
        notificationElement.className = `in-app-notification ${notification.type} ${notification.priority}`;
        notificationElement.innerHTML = `
            <div class="notification-icon">
                <i class="${this.getNotificationIcon(notification.type)}"></i>
            </div>
            <div class="notification-content">
                <div class="notification-title">${notification.title}</div>
                <div class="notification-message">${notification.message}</div>
            </div>
            <button class="notification-close">
                <i class="fas fa-times"></i>
            </button>
        `;

        // Add click handlers
        notificationElement.querySelector('.notification-close').addEventListener('click', () => {
            this.removeInAppNotification(notificationElement);
        });

        notificationElement.addEventListener('click', () => {
            this.markAsRead(notification.id);
            this.removeInAppNotification(notificationElement);
        });

        // Add to container
        let container = document.getElementById('inAppNotifications');
        if (!container) {
            container = document.createElement('div');
            container.id = 'inAppNotifications';
            container.className = 'in-app-notifications-container';
            document.body.appendChild(container);
        }

        container.appendChild(notificationElement);

        // Auto remove after delay
        const delay = notification.priority === 'urgent' ? 10000 : 5000;
        setTimeout(() => {
            this.removeInAppNotification(notificationElement);
        }, delay);
    }

    removeInAppNotification(element) {
        element.style.animation = 'slideOut 0.3s ease-in-out';
        setTimeout(() => {
            if (element.parentNode) {
                element.parentNode.removeChild(element);
            }
        }, 300);
    }

    getNotificationIcon(type) {
        const icons = {
            info: 'fas fa-info-circle',
            success: 'fas fa-check-circle',
            warning: 'fas fa-exclamation-triangle',
            error: 'fas fa-times-circle'
        };
        return icons[type] || icons.info;
    }

    playNotificationSound(type) {
        // Create audio context for notification sounds
        try {
            const audioContext = new (window.AudioContext || window.webkitAudioContext)();
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();

            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);

            // Different frequencies for different notification types
            const frequencies = {
                info: 800,
                success: 1000,
                warning: 600,
                error: 400
            };

            oscillator.frequency.setValueAtTime(frequencies[type] || 800, audioContext.currentTime);
            oscillator.type = 'sine';

            gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.3);

            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + 0.3);
        } catch (error) {
            console.warn('Could not play notification sound:', error);
        }
    }

    updateNotificationBadge() {
        const unreadCount = this.notifications.filter(n => !n.read).length;
        const badge = document.getElementById('notificationBadge');
        
        if (badge) {
            if (unreadCount > 0) {
                badge.textContent = unreadCount > 99 ? '99+' : unreadCount;
                badge.style.display = 'block';
            } else {
                badge.style.display = 'none';
            }
        }
    }

    toggleNotificationCenter() {
        const center = document.getElementById('notificationCenter');
        if (center) {
            center.classList.toggle('open');
            if (center.classList.contains('open')) {
                this.renderNotificationCenter();
            }
        }
    }

    renderNotificationCenter() {
        const container = document.getElementById('notificationCenter');
        if (!container) {
            this.createNotificationCenter();
            return;
        }

        const unreadNotifications = this.notifications.filter(n => !n.read);
        const recentNotifications = this.notifications.slice(0, 20);

        container.innerHTML = `
            <div class="notification-center-header">
                <h3>الإشعارات</h3>
                <div class="notification-actions">
                    <button class="btn-icon mark-all-read-btn" title="تحديد الكل كمقروء">
                        <i class="fas fa-check-double"></i>
                    </button>
                    <button class="btn-icon notification-settings-btn" title="إعدادات الإشعارات">
                        <i class="fas fa-cog"></i>
                    </button>
                    <button class="btn-icon clear-all-btn" title="مسح الكل">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
            
            <div class="notification-center-body">
                ${recentNotifications.length > 0 ? `
                    <div class="notifications-list">
                        ${recentNotifications.map(notification => `
                            <div class="notification-item ${notification.read ? 'read' : 'unread'} ${notification.type}" 
                                 data-notification-id="${notification.id}">
                                <div class="notification-icon">
                                    <i class="${this.getNotificationIcon(notification.type)}"></i>
                                </div>
                                <div class="notification-content">
                                    <div class="notification-title">${notification.title}</div>
                                    <div class="notification-message">${notification.message}</div>
                                    <div class="notification-time">${this.formatTime(notification.createdAt)}</div>
                                </div>
                                ${!notification.read ? '<div class="unread-indicator"></div>' : ''}
                            </div>
                        `).join('')}
                    </div>
                ` : `
                    <div class="empty-notifications">
                        <i class="fas fa-bell-slash"></i>
                        <p>لا توجد إشعارات</p>
                    </div>
                `}
            </div>
        `;
    }

    createNotificationCenter() {
        // Create notification center if it doesn't exist
        const center = document.createElement('div');
        center.id = 'notificationCenter';
        center.className = 'notification-center';
        document.body.appendChild(center);

        // Create notification toggle button
        const toggle = document.createElement('button');
        toggle.className = 'notifications-toggle btn-icon';
        toggle.innerHTML = `
            <i class="fas fa-bell"></i>
            <span id="notificationBadge" class="notification-badge"></span>
        `;

        // Add to header or sidebar
        const header = document.querySelector('.header-actions') || document.querySelector('.sidebar');
        if (header) {
            header.appendChild(toggle);
        }

        this.renderNotificationCenter();
    }

    async markAsRead(notificationId) {
        const notification = this.notifications.find(n => n.id == notificationId);
        if (notification && !notification.read) {
            notification.read = true;
            
            try {
                await storage.update('notifications', notification);
            } catch (error) {
                console.error('Error updating notification:', error);
            }
            
            this.updateNotificationBadge();
            this.renderNotificationCenter();
        }
    }

    async markAllAsRead() {
        const unreadNotifications = this.notifications.filter(n => !n.read);
        
        for (const notification of unreadNotifications) {
            notification.read = true;
            try {
                await storage.update('notifications', notification);
            } catch (error) {
                console.error('Error updating notification:', error);
            }
        }
        
        this.updateNotificationBadge();
        this.renderNotificationCenter();
    }

    async clearAllNotifications() {
        if (confirm('هل أنت متأكد من مسح جميع الإشعارات؟')) {
            this.notifications = [];
            
            try {
                const allNotifications = await storage.getAll('notifications');
                for (const notification of allNotifications) {
                    await storage.delete('notifications', notification.id);
                }
            } catch (error) {
                console.error('Error clearing notifications:', error);
            }
            
            this.updateNotificationBadge();
            this.renderNotificationCenter();
        }
    }

    formatTime(dateString) {
        const date = new Date(dateString);
        const now = new Date();
        const diffMs = now - date;
        const diffMins = Math.floor(diffMs / 60000);
        const diffHours = Math.floor(diffMs / 3600000);
        const diffDays = Math.floor(diffMs / 86400000);

        if (diffMins < 1) return 'الآن';
        if (diffMins < 60) return `منذ ${diffMins} دقيقة`;
        if (diffHours < 24) return `منذ ${diffHours} ساعة`;
        if (diffDays < 7) return `منذ ${diffDays} يوم`;
        
        return date.toLocaleDateString('ar-IQ');
    }

    // Periodic checks for new events
    startPeriodicChecks() {
        setInterval(() => {
            this.checkForDelayedOrders();
            this.checkForHighPriorityOrders();
        }, 60000); // Check every minute
    }

    async checkForNewOrders() {
        try {
            const orders = await storage.getAll('orders');
            const recentOrders = orders.filter(order => {
                const orderTime = new Date(order.createdAt || order.date);
                const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);
                return orderTime > fiveMinutesAgo;
            });

            for (const order of recentOrders) {
                await this.addNotification({
                    title: 'طلب جديد',
                    message: `طلب جديد من ${order.clientName} - ${order.recipientName || order.customerName}`,
                    type: 'info',
                    priority: 'normal',
                    category: 'orders',
                    data: { orderId: order.id }
                });
            }
        } catch (error) {
            console.error('Error checking for new orders:', error);
        }
    }

    async checkForDelayedOrders() {
        try {
            const orders = await storage.getAll('orders');
            const delayedOrders = orders.filter(order => {
                if (order.status !== 'pending') return false;
                
                const orderTime = new Date(order.createdAt || order.date);
                const twentyFourHoursAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
                return orderTime < twentyFourHoursAgo;
            });

            if (delayedOrders.length > 0) {
                await this.addNotification({
                    title: 'طلبات متأخرة',
                    message: `يوجد ${delayedOrders.length} طلب متأخر يحتاج متابعة`,
                    type: 'warning',
                    priority: 'high',
                    category: 'orders',
                    data: { delayedCount: delayedOrders.length }
                });
            }
        } catch (error) {
            console.error('Error checking for delayed orders:', error);
        }
    }

    async checkForHighPriorityOrders() {
        // Implementation for checking high priority orders
        // This would be based on business rules
    }

    showSettingsModal() {
        const modalContent = `
            <div class="modal notification-settings-modal">
                <div class="modal-header">
                    <h3 class="modal-title">إعدادات الإشعارات</h3>
                    <button class="modal-close">&times;</button>
                </div>
                <div class="modal-body">
                    <form id="notificationSettingsForm">
                        <div class="form-group">
                            <label class="checkbox-label">
                                <input type="checkbox" name="enableSound" ${this.settings.enableSound ? 'checked' : ''}>
                                <span>تفعيل الأصوات</span>
                            </label>
                        </div>
                        <div class="form-group">
                            <label class="checkbox-label">
                                <input type="checkbox" name="enableDesktop" ${this.settings.enableDesktop ? 'checked' : ''}>
                                <span>إشعارات سطح المكتب</span>
                            </label>
                        </div>
                        <div class="form-group">
                            <label class="checkbox-label">
                                <input type="checkbox" name="enableEmail" ${this.settings.enableEmail ? 'checked' : ''}>
                                <span>إشعارات البريد الإلكتروني</span>
                            </label>
                        </div>
                        <div class="form-group">
                            <label class="checkbox-label">
                                <input type="checkbox" name="autoMarkRead" ${this.settings.autoMarkRead ? 'checked' : ''}>
                                <span>تحديد كمقروء تلقائياً</span>
                            </label>
                        </div>
                        <div class="form-group">
                            <label class="form-label">الحد الأقصى للإشعارات</label>
                            <input type="number" class="form-input" name="maxNotifications" value="${this.settings.maxNotifications}" min="10" max="100">
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn-secondary modal-close">إلغاء</button>
                    <button type="button" class="btn-primary" onclick="notificationSystem.saveNotificationSettings()">حفظ الإعدادات</button>
                </div>
            </div>
        `;

        window.app.showModal(modalContent);
    }

    async saveNotificationSettings() {
        const form = document.getElementById('notificationSettingsForm');
        if (!form) return;

        const formData = new FormData(form);
        
        this.settings = {
            enableSound: formData.get('enableSound') === 'on',
            enableDesktop: formData.get('enableDesktop') === 'on',
            enableEmail: formData.get('enableEmail') === 'on',
            autoMarkRead: formData.get('autoMarkRead') === 'on',
            maxNotifications: parseInt(formData.get('maxNotifications')) || 50
        };

        await this.saveSettings();
        window.app.closeModal();
        window.app.showNotification('تم حفظ إعدادات الإشعارات', 'success');
    }
}

// Initialize notification system
const notificationSystem = new NotificationSystem();

// Export for use in other modules
window.notificationSystem = notificationSystem;
