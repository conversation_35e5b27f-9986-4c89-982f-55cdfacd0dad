@echo off
chcp 65001 >nul
cls
echo.
echo ========================================
echo    🚀 نظام إدارة شركة التوصيل
echo ========================================
echo.

echo 🔍 جاري البحث عن Python...

:: Try Python 3 first
python --version >nul 2>&1
if %errorlevel% == 0 (
    echo ✅ تم العثور على Python 3
    goto :start_python3
)

:: Try py command (Windows Python Launcher)
py --version >nul 2>&1
if %errorlevel% == 0 (
    echo ✅ تم العثور على Python عبر py launcher
    goto :start_py
)

:: Try python3 command
python3 --version >nul 2>&1
if %errorlevel% == 0 (
    echo ✅ تم العثور على Python 3
    goto :start_python3_alt
)

:: If no Python found, show alternatives
echo ❌ لم يتم العثور على Python
echo.
echo 📥 يرجى تثبيت Python من:
echo    https://www.python.org/downloads/
echo.
echo 🔧 أو استخدم أحد البدائل التالية:
echo.
echo 1️⃣  Live Server في VS Code:
echo    - ثبت إضافة Live Server
echo    - انقر بالزر الأيمن على login.html
echo    - اختر "Open with Live Server"
echo.
echo 2️⃣  XAMPP أو WAMP:
echo    - ضع الملفات في مجلد htdocs
echo    - اذهب إلى http://localhost/اسم_المجلد
echo.
echo 3️⃣  فتح الملف مباشرة:
echo    - انقر مرتين على login.html
echo    - (قد لا تعمل جميع الميزات)
echo.
echo اضغط أي مفتاح للخروج...
pause >nul
goto :end

:start_python3
echo 🌐 جاري تشغيل الخادم على المنفذ 8000...
echo.
echo 📱 افتح المتصفح واذهب إلى:
echo    http://localhost:8000
echo.
echo 🔄 سيتم فتح المتصفح تلقائياً خلال 3 ثوان...
timeout /t 3 /nobreak >nul
start http://localhost:8000
echo.
echo ⚠️  للإيقاف اضغط Ctrl+C
echo ========================================
python -m http.server 8000
goto :end

:start_py
echo 🌐 جاري تشغيل الخادم على المنفذ 8000...
echo.
echo 📱 افتح المتصفح واذهب إلى:
echo    http://localhost:8000
echo.
echo 🔄 سيتم فتح المتصفح تلقائياً خلال 3 ثوان...
timeout /t 3 /nobreak >nul
start http://localhost:8000
echo.
echo ⚠️  للإيقاف اضغط Ctrl+C
echo ========================================
py -m http.server 8000
goto :end

:start_python3_alt
echo 🌐 جاري تشغيل الخادم على المنفذ 8000...
echo.
echo 📱 افتح المتصفح واذهب إلى:
echo    http://localhost:8000
echo.
echo 🔄 سيتم فتح المتصفح تلقائياً خلال 3 ثوان...
timeout /t 3 /nobreak >nul
start http://localhost:8000
echo.
echo ⚠️  للإيقاف اضغط Ctrl+C
echo ========================================
python3 -m http.server 8000
goto :end

:end
echo.
echo 🛑 تم إيقاف الخادم
echo 👋 شكراً لاستخدام نظام إدارة التوصيل
pause
