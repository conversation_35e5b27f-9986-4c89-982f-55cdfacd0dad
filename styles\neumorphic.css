/* Advanced Neumorphic Design System */
:root {
    /* Enhanced Shadow Colors */
    --shadow-pressed-light: rgba(255, 255, 255, 0.5);
    --shadow-pressed-dark: rgba(174, 174, 192, 0.6);

    /* Enhanced Gradients */
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --secondary-gradient: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
    --success-gradient: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
    --warning-gradient: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);
    --danger-gradient: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
    --info-gradient: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);

    /* Elevation Levels */
    --elevation-1: 2px 2px 4px var(--shadow-dark), -2px -2px 4px var(--shadow-light);
    --elevation-2: 4px 4px 8px var(--shadow-dark), -4px -4px 8px var(--shadow-light);
    --elevation-3: 6px 6px 12px var(--shadow-dark), -6px -6px 12px var(--shadow-light);
    --elevation-4: 8px 8px 16px var(--shadow-dark), -8px -8px 16px var(--shadow-light);
    --elevation-5: 12px 12px 24px var(--shadow-dark), -12px -12px 24px var(--shadow-light);

    /* Inset Shadows */
    --inset-1: inset 2px 2px 4px var(--shadow-inset-dark), inset -2px -2px 4px var(--shadow-inset-light);
    --inset-2: inset 4px 4px 8px var(--shadow-inset-dark), inset -4px -4px 8px var(--shadow-inset-light);
    --inset-3: inset 6px 6px 12px var(--shadow-inset-dark), inset -6px -6px 12px var(--shadow-inset-light);

    /* Pressed State */
    --pressed-shadow: inset 3px 3px 6px var(--shadow-pressed-dark), inset -3px -3px 6px var(--shadow-pressed-light);
}

/* Neumorphic Design Components */

/* Buttons */
.btn-primary,
.btn-secondary,
.btn-success,
.btn-warning,
.btn-danger,
.btn-icon {
    padding: var(--spacing-md) var(--spacing-lg);
    border: none;
    border-radius: var(--radius-md);
    font-family: inherit;
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-fast);
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    text-decoration: none;
    box-shadow: 
        4px 4px 8px var(--shadow-dark),
        -4px -4px 8px var(--shadow-light);
}

.btn-primary {
    background: var(--primary-gradient);
    color: white;
    position: relative;
    overflow: hidden;
}

.btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0) 50%, rgba(0,0,0,0.1) 100%);
    opacity: 0;
    transition: opacity var(--transition-fast);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--elevation-3);
}

.btn-primary:hover::before {
    opacity: 1;
}

.btn-primary:active {
    transform: translateY(0);
    box-shadow: var(--pressed-shadow);
}

.btn-primary:active::before {
    opacity: 0.5;
}

.btn-secondary {
    background: var(--bg-secondary);
    color: var(--text-primary);
    position: relative;
    overflow: hidden;
}

.btn-secondary::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--secondary-gradient);
    opacity: 0;
    transition: opacity var(--transition-fast);
}

.btn-secondary:hover {
    transform: translateY(-2px);
    box-shadow: var(--elevation-3);
    color: white;
}

.btn-secondary:hover::before {
    opacity: 1;
}

.btn-secondary:active {
    transform: translateY(0);
    box-shadow: var(--pressed-shadow);
}

.btn-success {
    background: var(--success-gradient);
    color: white;
    position: relative;
    overflow: hidden;
}

.btn-success::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0) 50%, rgba(0,0,0,0.1) 100%);
    opacity: 0;
    transition: opacity var(--transition-fast);
}

.btn-success:hover {
    transform: translateY(-2px);
    box-shadow: var(--elevation-3);
}

.btn-success:hover::before {
    opacity: 1;
}

.btn-success:active {
    transform: translateY(0);
    box-shadow: var(--pressed-shadow);
}

.btn-warning {
    background: var(--warning-gradient);
    color: white;
    position: relative;
    overflow: hidden;
}

.btn-warning::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0) 50%, rgba(0,0,0,0.1) 100%);
    opacity: 0;
    transition: opacity var(--transition-fast);
}

.btn-warning:hover {
    transform: translateY(-2px);
    box-shadow: var(--elevation-3);
}

.btn-warning:hover::before {
    opacity: 1;
}

.btn-warning:active {
    transform: translateY(0);
    box-shadow: var(--pressed-shadow);
}

.btn-danger {
    background: var(--danger-gradient);
    color: white;
    position: relative;
    overflow: hidden;
}

.btn-danger::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0) 50%, rgba(0,0,0,0.1) 100%);
    opacity: 0;
    transition: opacity var(--transition-fast);
}

.btn-danger:hover {
    transform: translateY(-2px);
    box-shadow: var(--elevation-3);
}

.btn-danger:hover::before {
    opacity: 1;
}

.btn-danger:active {
    transform: translateY(0);
    box-shadow: var(--pressed-shadow);
}

.btn-icon {
    width: 40px;
    height: 40px;
    padding: 0;
    border-radius: 50%;
    justify-content: center;
    background: var(--bg-secondary);
    color: var(--text-secondary);
}

.btn-icon:hover {
    color: var(--primary-color);
    transform: translateY(-1px);
}

/* Form Elements */
.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-label {
    display: block;
    margin-bottom: var(--spacing-sm);
    font-weight: 500;
    color: var(--text-primary);
}

.form-input,
.form-select,
.form-textarea {
    width: 100%;
    padding: var(--spacing-md);
    border: none;
    border-radius: var(--radius-md);
    background: var(--bg-primary);
    color: var(--text-primary);
    font-family: inherit;
    font-size: 1rem;
    transition: all var(--transition-fast);
    box-shadow: 
        inset 4px 4px 8px var(--shadow-dark),
        inset -4px -4px 8px var(--shadow-light);
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
    outline: none;
    box-shadow: 
        inset 2px 2px 4px var(--shadow-dark),
        inset -2px -2px 4px var(--shadow-light),
        0 0 0 2px var(--primary-color);
}

.form-textarea {
    resize: vertical;
    min-height: 100px;
}

/* Cards */
.card {
    background: var(--bg-secondary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xl);
    box-shadow: 
        8px 8px 16px var(--shadow-dark),
        -8px -8px 16px var(--shadow-light);
    transition: transform var(--transition-fast);
}

.card:hover {
    transform: translateY(-2px);
}

.card-header {
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-md);
    border-bottom: 1px solid var(--border-color);
}

.card-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.card-subtitle {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

/* Status Badges */
.status-badge {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: 0.8rem;
    font-weight: 500;
    box-shadow: 
        2px 2px 4px var(--shadow-dark),
        -2px -2px 4px var(--shadow-light);
}

.status-delivered {
    background: var(--success-color);
    color: white;
}

.status-pending {
    background: var(--warning-color);
    color: white;
}

.status-returned {
    background: var(--danger-color);
    color: white;
}

.status-partial {
    background: var(--info-color);
    color: white;
}

/* Modal */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 2000;
    backdrop-filter: blur(4px);
}

.modal-overlay.active {
    display: flex;
}

.modal {
    background: var(--bg-secondary);
    border-radius: var(--radius-xl);
    padding: var(--spacing-2xl);
    max-width: 600px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 
        16px 16px 32px var(--shadow-dark),
        -16px -16px 32px var(--shadow-light);
    transform: scale(0.9);
    transition: transform var(--transition-normal);
}

.modal-overlay.active .modal {
    transform: scale(1);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-xl);
    padding-bottom: var(--spacing-md);
    border-bottom: 1px solid var(--border-color);
}

.modal-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--text-secondary);
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: 50%;
    transition: all var(--transition-fast);
}

.modal-close:hover {
    color: var(--danger-color);
    background: var(--bg-primary);
}

.modal-body {
    margin-bottom: var(--spacing-xl);
}

.modal-footer {
    display: flex;
    gap: var(--spacing-md);
    justify-content: flex-end;
    padding-top: var(--spacing-md);
    border-top: 1px solid var(--border-color);
}

/* Drivers Grid */
.drivers-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: var(--spacing-xl);
}

.driver-card {
    background: var(--bg-secondary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xl);
    box-shadow: 
        8px 8px 16px var(--shadow-dark),
        -8px -8px 16px var(--shadow-light);
    transition: transform var(--transition-fast);
}

.driver-card:hover {
    transform: translateY(-2px);
}

.driver-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.driver-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    font-weight: 600;
    box-shadow: 
        4px 4px 8px var(--shadow-dark),
        -4px -4px 8px var(--shadow-light);
}

.driver-info h3 {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.driver-info p {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.driver-stats {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.driver-stat {
    text-align: center;
    padding: var(--spacing-md);
    background: var(--bg-primary);
    border-radius: var(--radius-md);
    box-shadow: 
        inset 2px 2px 4px var(--shadow-dark),
        inset -2px -2px 4px var(--shadow-light);
}

.driver-stat-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: var(--spacing-xs);
}

.driver-stat-label {
    font-size: 0.8rem;
    color: var(--text-secondary);
}

.driver-actions {
    display: flex;
    gap: var(--spacing-sm);
}

/* Report Content */
.report-content {
    background: var(--bg-secondary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xl);
    box-shadow: 
        8px 8px 16px var(--shadow-dark),
        -8px -8px 16px var(--shadow-light);
    margin-top: var(--spacing-xl);
}

.report-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.report-item {
    text-align: center;
    padding: var(--spacing-lg);
    background: var(--bg-primary);
    border-radius: var(--radius-md);
    box-shadow: 
        inset 4px 4px 8px var(--shadow-dark),
        inset -4px -4px 8px var(--shadow-light);
}

.report-value {
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: var(--spacing-xs);
}

.report-label {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

/* Accounting Summary */
.accounting-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-2xl);
}

.summary-card {
    background: var(--bg-secondary);
    padding: var(--spacing-xl);
    border-radius: var(--radius-lg);
    text-align: center;
    box-shadow: 
        8px 8px 16px var(--shadow-dark),
        -8px -8px 16px var(--shadow-light);
}

.summary-card h3 {
    font-size: 1.1rem;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-md);
}

.amount {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-color);
}

/* Settings */
.settings-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: var(--spacing-xl);
}

.settings-card {
    background: var(--bg-secondary);
    padding: var(--spacing-xl);
    border-radius: var(--radius-lg);
    box-shadow: 
        8px 8px 16px var(--shadow-dark),
        -8px -8px 16px var(--shadow-light);
}

.settings-card h3 {
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-md);
    border-bottom: 1px solid var(--border-color);
}

.setting-item {
    margin-bottom: var(--spacing-lg);
}

.setting-item:last-child {
    margin-bottom: 0;
}

.setting-item label {
    display: block;
    margin-bottom: var(--spacing-sm);
    font-weight: 500;
    color: var(--text-primary);
}

.setting-item input,
.setting-item select {
    width: 100%;
    padding: var(--spacing-md);
    border: none;
    border-radius: var(--radius-md);
    background: var(--bg-primary);
    color: var(--text-primary);
    font-family: inherit;
    box-shadow: 
        inset 4px 4px 8px var(--shadow-dark),
        inset -4px -4px 8px var(--shadow-light);
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.3s ease-in-out;
}

.slide-in-right {
    animation: slideInRight 0.3s ease-in-out;
}

.bounce-in {
    animation: bounceIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideInRight {
    from { transform: translateX(100%); }
    to { transform: translateX(0); }
}

@keyframes bounceIn {
    0% { transform: scale(0.3); opacity: 0; }
    50% { transform: scale(1.05); }
    70% { transform: scale(0.9); }
    100% { transform: scale(1); opacity: 1; }
}

/* Loading Spinner */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid var(--bg-primary);
    border-radius: 50%;
    border-top-color: var(--primary-color);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Tooltip */
.tooltip {
    position: relative;
    display: inline-block;
}

.tooltip .tooltiptext {
    visibility: hidden;
    width: 120px;
    background-color: var(--text-primary);
    color: var(--bg-secondary);
    text-align: center;
    border-radius: var(--radius-sm);
    padding: 5px;
    position: absolute;
    z-index: 1;
    bottom: 125%;
    left: 50%;
    margin-left: -60px;
    opacity: 0;
    transition: opacity 0.3s;
    font-size: 12px;
}

.tooltip:hover .tooltiptext {
    visibility: visible;
    opacity: 1;
}

/* Success/Error States */
.form-input.success {
    border: 2px solid var(--success-color);
    box-shadow:
        inset 4px 4px 8px var(--shadow-dark),
        inset -4px -4px 8px var(--shadow-light),
        0 0 0 2px var(--success-color);
}

.form-input.error {
    border: 2px solid var(--danger-color);
    box-shadow:
        inset 4px 4px 8px var(--shadow-dark),
        inset -4px -4px 8px var(--shadow-light),
        0 0 0 2px var(--danger-color);
}

/* Disabled State */
.btn-primary:disabled,
.btn-secondary:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

/* Hover Effects for Cards */
.stat-card:hover,
.driver-card:hover,
.card:hover {
    box-shadow:
        12px 12px 24px var(--shadow-dark),
        -12px -12px 24px var(--shadow-light);
}

/* Focus States */
.form-input:focus,
.form-select:focus,
.form-textarea:focus {
    transform: translateY(-1px);
}

/* Form Help Text */
.form-help {
    display: block;
    margin-top: var(--spacing-xs);
    font-size: 0.8rem;
    color: var(--text-secondary);
    font-style: italic;
}

/* Error Message */
.error-message {
    margin-top: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
    background: var(--danger-color);
    color: white;
    border-radius: var(--radius-sm);
    font-size: 0.8rem;
}

/* Tracking Number Styles */
.tracking-number {
    display: inline-block;
    padding: var(--spacing-xs) var(--spacing-sm);
    background: var(--primary-color);
    color: white;
    border-radius: var(--radius-sm);
    font-family: 'Courier New', monospace;
    font-weight: 600;
    cursor: pointer;
    transition: all var(--transition-fast);
    font-size: 0.9rem;
}

.tracking-number:hover {
    background: #4338ca;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.no-tracking {
    color: var(--text-secondary);
    font-style: italic;
}

/* Copy notification */
.copy-notification {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: var(--success-color);
    color: white;
    padding: var(--spacing-md) var(--spacing-lg);
    border-radius: var(--radius-md);
    font-weight: 500;
    z-index: 3000;
    animation: fadeInOut 2s ease-in-out;
}

@keyframes fadeInOut {
    0% { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
    20% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
    80% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
    100% { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
}

/* Tracking Input Group */
.tracking-input-group {
    display: flex;
    gap: var(--spacing-sm);
    align-items: center;
}

.tracking-input-group .form-input {
    flex: 1;
}

.generate-tracking-btn {
    flex-shrink: 0;
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: 0.9rem;
    white-space: nowrap;
}

.generate-tracking-btn i {
    margin-left: var(--spacing-xs);
}

/* Quick Tracking Search */
.quick-tracking-search {
    display: flex;
    gap: var(--spacing-sm);
    align-items: center;
    margin-top: var(--spacing-md);
}

.tracking-search-input {
    flex: 1;
    padding: var(--spacing-sm) var(--spacing-md);
    border: none;
    border-radius: var(--radius-md);
    background: var(--bg-primary);
    color: var(--text-primary);
    font-family: 'Courier New', monospace;
    font-size: 1rem;
    box-shadow:
        inset 4px 4px 8px var(--shadow-dark),
        inset -4px -4px 8px var(--shadow-light);
    transition: all var(--transition-fast);
}

.tracking-search-input:focus {
    outline: none;
    box-shadow:
        inset 2px 2px 4px var(--shadow-dark),
        inset -2px -2px 4px var(--shadow-light),
        0 0 0 2px var(--primary-color);
}

.tracking-search-input::placeholder {
    color: var(--text-secondary);
    font-family: inherit;
}

/* Responsive tracking input */
@media (max-width: 768px) {
    .tracking-input-group {
        flex-direction: column;
        align-items: stretch;
    }

    .generate-tracking-btn {
        width: 100%;
        justify-content: center;
    }

    .quick-tracking-search {
        flex-direction: column;
        align-items: stretch;
    }

    .quick-tracking-search button {
        width: 100%;
        justify-content: center;
    }
}

/* Assignment Section Styles */
.assignment-container {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: var(--spacing-lg);
    margin-top: var(--spacing-lg);
}

.assignment-form-card,
.assignment-history-card,
.multiple-assignment-card {
    background: var(--bg-primary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    box-shadow:
        8px 8px 16px var(--shadow-dark),
        -8px -8px 16px var(--shadow-light);
}

.assignment-form-card h3,
.assignment-history-card h3 {
    margin-bottom: var(--spacing-lg);
    color: var(--primary-color);
    font-weight: 600;
}

/* Tracking Search Group */
.tracking-search-group {
    display: flex;
    gap: var(--spacing-sm);
    align-items: center;
}

.tracking-search-group .tracking-input {
    flex: 1;
    font-family: 'Courier New', monospace;
    font-weight: 500;
}

.tracking-search-group .search-btn {
    flex-shrink: 0;
    padding: var(--spacing-sm) var(--spacing-md);
}

/* Order Details Card */
.order-details-card {
    margin: var(--spacing-lg) 0;
    padding: var(--spacing-lg);
    background: var(--bg-secondary);
    border-radius: var(--radius-md);
    border: 2px solid var(--success-color);
    box-shadow:
        inset 2px 2px 4px var(--shadow-dark),
        inset -2px -2px 4px var(--shadow-light);
}

.order-details-card h4 {
    margin-bottom: var(--spacing-md);
    color: var(--success-color);
    font-weight: 600;
}

.order-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-md);
}

.info-item {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.info-label {
    font-size: 0.9rem;
    color: var(--text-secondary);
    font-weight: 500;
}

.info-value {
    font-weight: 600;
    color: var(--text-primary);
    padding: var(--spacing-xs) var(--spacing-sm);
    background: var(--bg-primary);
    border-radius: var(--radius-sm);
    box-shadow:
        inset 2px 2px 4px var(--shadow-dark),
        inset -2px -2px 4px var(--shadow-light);
}

/* Form Actions */
.form-actions {
    display: flex;
    gap: var(--spacing-md);
    justify-content: flex-end;
    margin-top: var(--spacing-lg);
}

/* Assignment History */
.assignment-history {
    max-height: 400px;
    overflow-y: auto;
}

.history-item {
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-sm);
    background: var(--bg-secondary);
    border-radius: var(--radius-md);
    box-shadow:
        inset 2px 2px 4px var(--shadow-dark),
        inset -2px -2px 4px var(--shadow-light);
}

.history-item:last-child {
    margin-bottom: 0;
}

.history-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-xs);
}

.history-action {
    font-weight: 600;
    color: var(--primary-color);
}

.history-time {
    font-size: 0.8rem;
    color: var(--text-secondary);
}

.history-details {
    font-size: 0.9rem;
    color: var(--text-primary);
}

.no-history {
    text-align: center;
    color: var(--text-secondary);
    font-style: italic;
    padding: var(--spacing-lg);
}

/* Multiple Assignment Styles */
.multiple-assignment-card {
    border: 2px solid var(--primary-color);
}

.multiple-assignment-card h3 {
    color: var(--primary-color);
    margin-bottom: var(--spacing-lg);
}

.form-textarea {
    width: 100%;
    min-height: 120px;
    padding: var(--spacing-md);
    border: none;
    border-radius: var(--radius-md);
    background: var(--bg-secondary);
    color: var(--text-primary);
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    line-height: 1.4;
    resize: vertical;
    box-shadow:
        inset 4px 4px 8px var(--shadow-dark),
        inset -4px -4px 8px var(--shadow-light);
    transition: all var(--transition-fast);
}

.form-textarea:focus {
    outline: none;
    box-shadow:
        inset 2px 2px 4px var(--shadow-dark),
        inset -2px -2px 4px var(--shadow-light),
        0 0 0 2px var(--primary-color);
}

.form-textarea::placeholder {
    color: var(--text-secondary);
    font-family: inherit;
}

.multiple-orders-display {
    margin-top: var(--spacing-lg);
    padding: var(--spacing-lg);
    background: var(--bg-secondary);
    border-radius: var(--radius-md);
    box-shadow:
        inset 2px 2px 4px var(--shadow-dark),
        inset -2px -2px 4px var(--shadow-light);
}

.multiple-orders-display h4 {
    margin-bottom: var(--spacing-md);
    color: var(--success-color);
    font-weight: 600;
}

.multiple-orders-list {
    max-height: 300px;
    overflow-y: auto;
}

.multiple-order-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-sm) var(--spacing-md);
    margin-bottom: var(--spacing-xs);
    background: var(--bg-primary);
    border-radius: var(--radius-sm);
    box-shadow:
        2px 2px 4px var(--shadow-dark),
        -2px -2px 4px var(--shadow-light);
}

.multiple-order-item:last-child {
    margin-bottom: 0;
}

.order-item-info {
    flex: 1;
}

.order-item-tracking {
    font-family: 'Courier New', monospace;
    font-weight: 600;
    color: var(--primary-color);
}

.order-item-details {
    font-size: 0.8rem;
    color: var(--text-secondary);
    margin-top: var(--spacing-xs);
}

.order-item-status {
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: 0.8rem;
    font-weight: 500;
}

.order-item-status.valid {
    background: var(--success-color);
    color: white;
}

.order-item-status.invalid {
    background: var(--danger-color);
    color: white;
}

.order-item-status.assigned {
    background: var(--warning-color);
    color: white;
}

/* Multiple Assignment Summary */
.multiple-assignment-summary {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md);
    margin-top: var(--spacing-md);
    background: var(--bg-primary);
    border-radius: var(--radius-md);
    box-shadow:
        inset 2px 2px 4px var(--shadow-dark),
        inset -2px -2px 4px var(--shadow-light);
}

.summary-stat {
    text-align: center;
}

.summary-stat-value {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: var(--spacing-xs);
}

.summary-stat-label {
    font-size: 0.8rem;
    color: var(--text-secondary);
}

.summary-stat.valid .summary-stat-value {
    color: var(--success-color);
}

.summary-stat.invalid .summary-stat-value {
    color: var(--danger-color);
}

.summary-stat.assigned .summary-stat-value {
    color: var(--warning-color);
}

/* Responsive Assignment */
@media (max-width: 768px) {
    .assignment-container {
        grid-template-columns: 1fr;
    }

    .tracking-search-group {
        flex-direction: column;
        align-items: stretch;
    }

    .tracking-search-group .search-btn {
        width: 100%;
    }

    .order-info-grid {
        grid-template-columns: 1fr;
    }

    .form-actions {
        flex-direction: column;
    }

    .multiple-assignment-summary {
        flex-direction: column;
        gap: var(--spacing-sm);
    }

    .multiple-order-item {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-xs);
    }
}

/* Bulk Assignment Modal */
.bulk-assignment-modal .modal-content {
    max-width: 90vw;
    max-height: 90vh;
    width: 1200px;
}

.bulk-assignment-container {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.assignment-controls {
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
    gap: var(--spacing-lg);
    padding: var(--spacing-lg);
    background: var(--bg-secondary);
    border-radius: var(--radius-md);
    box-shadow:
        inset 2px 2px 4px var(--shadow-dark),
        inset -2px -2px 4px var(--shadow-light);
}

.assignment-controls .form-group {
    flex: 1;
    margin-bottom: 0;
}

.bulk-actions {
    display: flex;
    gap: var(--spacing-sm);
    flex-shrink: 0;
}

.orders-selection-container h4 {
    margin-bottom: var(--spacing-md);
    color: var(--primary-color);
    font-weight: 600;
}

.orders-selection-container .table-container {
    max-height: 400px;
    overflow-y: auto;
}

.selected-count {
    font-weight: 600;
    padding: var(--spacing-sm) var(--spacing-md);
    background: var(--bg-secondary);
    border-radius: var(--radius-sm);
    box-shadow:
        inset 2px 2px 4px var(--shadow-dark),
        inset -2px -2px 4px var(--shadow-light);
}

/* Checkbox Styles */
.order-checkbox,
#selectAllCheckbox {
    width: 18px;
    height: 18px;
    accent-color: var(--primary-color);
    cursor: pointer;
}

.order-checkbox:checked,
#selectAllCheckbox:checked {
    background: var(--primary-color);
}

/* Responsive Bulk Assignment */
@media (max-width: 768px) {
    .bulk-assignment-modal .modal-content {
        width: 95vw;
        max-width: none;
    }

    .assignment-controls {
        flex-direction: column;
        align-items: stretch;
    }

    .bulk-actions {
        justify-content: center;
        flex-wrap: wrap;
    }

    .orders-selection-container .table-container {
        max-height: 300px;
    }
}

/* Custom Assignment Notifications */
.custom-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    max-width: 400px;
    padding: var(--spacing-lg);
    border-radius: var(--radius-lg);
    box-shadow:
        8px 8px 16px var(--shadow-dark),
        -8px -8px 16px var(--shadow-light);
    z-index: 3000;
    transform: translateX(100%);
    opacity: 0;
    transition: all 0.3s ease;
}

.custom-notification.show {
    transform: translateX(0);
    opacity: 1;
}

.custom-notification.notification-success {
    background: var(--success-color);
    color: white;
}

.custom-notification.notification-error {
    background: var(--danger-color);
    color: white;
}

.custom-notification.notification-warning {
    background: var(--warning-color);
    color: white;
}

.custom-notification.notification-info {
    background: var(--primary-color);
    color: white;
}

.assignment-notification .notification-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-md);
    font-size: 1.1rem;
}

.assignment-notification .notification-header i {
    font-size: 1.2rem;
}

.assignment-notification .notification-details {
    font-size: 0.9rem;
    opacity: 0.9;
    line-height: 1.4;
}

.assignment-notification .notification-details div {
    margin-bottom: var(--spacing-xs);
}

.assignment-notification .notification-details div:last-child {
    margin-bottom: 0;
}

/* Responsive Custom Notifications */
@media (max-width: 768px) {
    .custom-notification {
        right: 10px;
        left: 10px;
        max-width: none;
        transform: translateY(-100%);
    }

    .custom-notification.show {
        transform: translateY(0);
    }
}

/* Assignment Report Modal */
.assignment-report-modal .modal-content {
    max-width: 800px;
    max-height: 90vh;
}

.assignment-report-summary {
    margin-bottom: var(--spacing-lg);
}

.report-summary-card {
    background: var(--bg-secondary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    box-shadow:
        inset 2px 2px 4px var(--shadow-dark),
        inset -2px -2px 4px var(--shadow-light);
}

.summary-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-md);
    border-bottom: 2px solid var(--primary-color);
}

.summary-header h4 {
    color: var(--primary-color);
    margin: 0;
}

.summary-date {
    font-size: 0.9rem;
    color: var(--text-secondary);
}

.summary-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.summary-stat {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    background: var(--bg-primary);
    border-radius: var(--radius-md);
    box-shadow:
        4px 4px 8px var(--shadow-dark),
        -4px -4px 8px var(--shadow-light);
}

.stat-icon {
    font-size: 1.5rem;
}

.summary-stat.success .stat-icon {
    color: var(--success-color);
}

.summary-stat.error .stat-icon {
    color: var(--danger-color);
}

.summary-stat.total .stat-icon {
    color: var(--primary-color);
}

.stat-info {
    flex: 1;
}

.stat-value {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: var(--spacing-xs);
}

.summary-stat.success .stat-value {
    color: var(--success-color);
}

.summary-stat.error .stat-value {
    color: var(--danger-color);
}

.summary-stat.total .stat-value {
    color: var(--primary-color);
}

.stat-label {
    font-size: 0.8rem;
    color: var(--text-secondary);
}

.summary-driver {
    text-align: center;
    padding: var(--spacing-md);
    background: var(--primary-color);
    color: white;
    border-radius: var(--radius-md);
    font-size: 1.1rem;
}

.assignment-errors {
    margin-bottom: var(--spacing-lg);
}

.assignment-errors h4 {
    color: var(--danger-color);
    margin-bottom: var(--spacing-md);
}

.errors-list {
    background: var(--bg-secondary);
    border-radius: var(--radius-md);
    padding: var(--spacing-md);
    max-height: 200px;
    overflow-y: auto;
    box-shadow:
        inset 2px 2px 4px var(--shadow-dark),
        inset -2px -2px 4px var(--shadow-light);
}

.error-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm);
    margin-bottom: var(--spacing-xs);
    background: var(--bg-primary);
    border-radius: var(--radius-sm);
    border-left: 4px solid var(--danger-color);
}

.error-item:last-child {
    margin-bottom: 0;
}

.error-item i {
    color: var(--danger-color);
}

.assignment-recommendations h4 {
    color: var(--primary-color);
    margin-bottom: var(--spacing-md);
}

.assignment-recommendations ul {
    background: var(--bg-secondary);
    border-radius: var(--radius-md);
    padding: var(--spacing-lg);
    box-shadow:
        inset 2px 2px 4px var(--shadow-dark),
        inset -2px -2px 4px var(--shadow-light);
}

.assignment-recommendations li {
    margin-bottom: var(--spacing-sm);
    padding-right: var(--spacing-md);
    position: relative;
}

.assignment-recommendations li:before {
    content: '✓';
    position: absolute;
    right: 0;
    color: var(--success-color);
    font-weight: bold;
}

.assignment-recommendations li:last-child {
    margin-bottom: 0;
}

/* Responsive Assignment Report */
@media (max-width: 768px) {
    .assignment-report-modal .modal-content {
        width: 95vw;
        max-width: none;
    }

    .summary-stats {
        grid-template-columns: 1fr;
    }

    .summary-stat {
        justify-content: center;
        text-align: center;
    }

    .summary-header {
        flex-direction: column;
        gap: var(--spacing-sm);
        text-align: center;
    }
}

/* Active Button States */
.btn-primary:active,
.btn-secondary:active {
    box-shadow:
        inset 4px 4px 8px var(--shadow-dark),
        inset -4px -4px 8px var(--shadow-light);
}

/* Customers Grid */
.customers-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: var(--spacing-xl);
}

.customer-card {
    background: var(--bg-secondary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xl);
    box-shadow:
        8px 8px 16px var(--shadow-dark),
        -8px -8px 16px var(--shadow-light);
    transition: transform var(--transition-fast);
}

.customer-card:hover {
    transform: translateY(-2px);
}

.customer-header {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
    position: relative;
}

.customer-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    box-shadow:
        4px 4px 8px var(--shadow-dark),
        -4px -4px 8px var(--shadow-light);
    flex-shrink: 0;
}

.customer-info {
    flex: 1;
}

.customer-info h3 {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.customer-info p {
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin-bottom: var(--spacing-xs);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.customer-type {
    font-weight: 500;
    color: var(--primary-color);
}

.customer-status {
    position: absolute;
    top: 0;
    left: 0;
}

.customer-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.customer-stat {
    text-align: center;
    padding: var(--spacing-md);
    background: var(--bg-primary);
    border-radius: var(--radius-md);
    box-shadow:
        inset 2px 2px 4px var(--shadow-dark),
        inset -2px -2px 4px var(--shadow-light);
}

.customer-stat-value {
    font-size: 1.2rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: var(--spacing-xs);
}

.customer-stat-label {
    font-size: 0.8rem;
    color: var(--text-secondary);
}

.customer-contact {
    margin-bottom: var(--spacing-lg);
    padding: var(--spacing-md);
    background: var(--bg-primary);
    border-radius: var(--radius-md);
}

.customer-contact p {
    margin: 0;
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.customer-actions {
    display: flex;
    gap: var(--spacing-sm);
    flex-wrap: wrap;
}

.customer-actions .btn-secondary,
.customer-actions .btn-success,
.customer-actions .btn-danger {
    flex: 1;
    min-width: 80px;
    font-size: 0.9rem;
    padding: var(--spacing-sm) var(--spacing-md);
}

/* Customer Table Styles */
.customer-name-cell {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.customer-name-cell i {
    color: var(--primary-color);
}

.type-badge {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: 0.8rem;
    font-weight: 500;
}

.type-individual {
    background: var(--info-color);
    color: white;
}

.type-company {
    background: var(--success-color);
    color: white;
}

/* Page Actions */
.page-actions {
    display: flex;
    gap: var(--spacing-md);
    align-items: center;
}

/* Customer Summary */
.customer-summary {
    margin-top: var(--spacing-xl);
    padding: var(--spacing-lg);
    background: var(--bg-primary);
    border-radius: var(--radius-md);
}

.customer-summary h4 {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
}

/* Responsive Design for Customers */
@media (max-width: 768px) {
    .customers-grid {
        grid-template-columns: 1fr;
    }

    .customer-header {
        flex-direction: column;
        align-items: center;
        text-align: center;
    }

    .customer-status {
        position: static;
        margin-top: var(--spacing-sm);
    }

    .customer-stats {
        grid-template-columns: 1fr;
    }

    .customer-actions {
        flex-direction: column;
    }

    .page-actions {
        flex-direction: column;
        align-items: stretch;
    }
}

/* Enhanced Neumorphic Components */

/* Advanced Cards */
.neumorphic-card {
    background: var(--bg-primary);
    border-radius: var(--radius-xl);
    padding: var(--spacing-xl);
    box-shadow: var(--elevation-2);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.neumorphic-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
}

.neumorphic-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--elevation-4);
}

.neumorphic-card.pressed {
    transform: translateY(0);
    box-shadow: var(--inset-1);
}

/* Toggle Switches */
.neumorphic-toggle {
    width: 60px;
    height: 30px;
    background: var(--bg-secondary);
    border-radius: 15px;
    position: relative;
    cursor: pointer;
    box-shadow: var(--inset-1);
    transition: all var(--transition-normal);
}

.neumorphic-toggle::before {
    content: '';
    position: absolute;
    top: 3px;
    right: 3px;
    width: 24px;
    height: 24px;
    background: var(--bg-primary);
    border-radius: 50%;
    box-shadow: var(--elevation-1);
    transition: all var(--transition-normal);
}

.neumorphic-toggle.active {
    background: var(--primary-color);
    box-shadow: var(--inset-1);
}

.neumorphic-toggle.active::before {
    right: 33px;
    background: white;
}

/* Progress Bars */
.neumorphic-progress {
    width: 100%;
    height: 20px;
    background: var(--bg-secondary);
    border-radius: 10px;
    box-shadow: var(--inset-1);
    overflow: hidden;
    position: relative;
}

.neumorphic-progress-fill {
    height: 100%;
    background: var(--primary-gradient);
    border-radius: 10px;
    box-shadow: var(--elevation-1);
    transition: width var(--transition-slow);
    position: relative;
}

.neumorphic-progress-fill::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, rgba(255,255,255,0.3), transparent, rgba(255,255,255,0.3));
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* Tabs */
.neumorphic-tabs {
    display: flex;
    background: var(--bg-secondary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xs);
    box-shadow: var(--inset-1);
}

.neumorphic-tab {
    flex: 1;
    padding: var(--spacing-md);
    border: none;
    background: transparent;
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all var(--transition-normal);
    color: var(--text-secondary);
}

.neumorphic-tab.active {
    background: var(--bg-primary);
    box-shadow: var(--elevation-1);
    color: var(--text-primary);
}

/* Dark Theme Support */
@media (prefers-color-scheme: dark) {
    :root {
        --bg-primary: #2d3748;
        --bg-secondary: #4a5568;
        --bg-tertiary: #718096;

        --shadow-light: rgba(255, 255, 255, 0.1);
        --shadow-dark: rgba(0, 0, 0, 0.3);
        --shadow-inset-light: rgba(255, 255, 255, 0.05);
        --shadow-inset-dark: rgba(0, 0, 0, 0.2);
        --shadow-pressed-light: rgba(255, 255, 255, 0.05);
        --shadow-pressed-dark: rgba(0, 0, 0, 0.4);

        --text-primary: #f7fafc;
        --text-secondary: #e2e8f0;
        --text-muted: #a0aec0;

        --border-color: rgba(255, 255, 255, 0.1);
    }
}

/* Enhanced Neumorphic Shadows - Better Contrast */
.neumorphic-enhanced {
    box-shadow:
        8px 8px 16px rgba(163, 177, 198, 0.9),
        -8px -8px 16px rgba(255, 255, 255, 0.9);
    background: linear-gradient(145deg, #f0f2f5, #e6e9ed);
    transition: all var(--transition-normal);
}

.neumorphic-enhanced:hover {
    box-shadow:
        12px 12px 24px rgba(163, 177, 198, 0.9),
        -12px -12px 24px rgba(255, 255, 255, 0.9);
    transform: translateY(-2px);
}

.neumorphic-enhanced:active {
    box-shadow:
        inset 4px 4px 8px rgba(163, 177, 198, 0.8),
        inset -4px -4px 8px rgba(255, 255, 255, 0.8);
    transform: translateY(0);
}

/* RTL Specific Enhancements */
[dir="rtl"] .neumorphic-card::before {
    background: linear-gradient(-90deg, transparent, rgba(255,255,255,0.3), transparent);
}

[dir="rtl"] .neumorphic-toggle::before {
    right: auto;
    left: 3px;
}

[dir="rtl"] .neumorphic-toggle.active::before {
    left: 33px;
    right: auto;
}

/* Responsive Neumorphic Enhancements */
@media (max-width: 768px) {
    .neumorphic-enhanced {
        box-shadow:
            4px 4px 8px rgba(163, 177, 198, 0.8),
            -4px -4px 8px rgba(255, 255, 255, 0.8);
    }

    .neumorphic-enhanced:hover {
        box-shadow:
            6px 6px 12px rgba(163, 177, 198, 0.8),
            -6px -6px 12px rgba(255, 255, 255, 0.8);
        transform: translateY(-1px);
    }
}
