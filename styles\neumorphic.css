/* Neumorphic Design Components */

/* Buttons */
.btn-primary,
.btn-secondary,
.btn-success,
.btn-warning,
.btn-danger,
.btn-icon {
    padding: var(--spacing-md) var(--spacing-lg);
    border: none;
    border-radius: var(--radius-md);
    font-family: inherit;
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-fast);
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    text-decoration: none;
    box-shadow: 
        4px 4px 8px var(--shadow-dark),
        -4px -4px 8px var(--shadow-light);
}

.btn-primary {
    background: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background: #4338ca;
    transform: translateY(-1px);
    box-shadow: 
        6px 6px 12px var(--shadow-dark),
        -6px -6px 12px var(--shadow-light);
}

.btn-primary:active {
    transform: translateY(0);
    box-shadow: 
        2px 2px 4px var(--shadow-dark),
        -2px -2px 4px var(--shadow-light);
}

.btn-secondary {
    background: var(--bg-secondary);
    color: var(--text-primary);
}

.btn-secondary:hover {
    background: var(--bg-primary);
    transform: translateY(-1px);
}

.btn-success {
    background: var(--success-color);
    color: white;
}

.btn-warning {
    background: var(--warning-color);
    color: white;
}

.btn-danger {
    background: var(--danger-color);
    color: white;
}

.btn-icon {
    width: 40px;
    height: 40px;
    padding: 0;
    border-radius: 50%;
    justify-content: center;
    background: var(--bg-secondary);
    color: var(--text-secondary);
}

.btn-icon:hover {
    color: var(--primary-color);
    transform: translateY(-1px);
}

/* Form Elements */
.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-label {
    display: block;
    margin-bottom: var(--spacing-sm);
    font-weight: 500;
    color: var(--text-primary);
}

.form-input,
.form-select,
.form-textarea {
    width: 100%;
    padding: var(--spacing-md);
    border: none;
    border-radius: var(--radius-md);
    background: var(--bg-primary);
    color: var(--text-primary);
    font-family: inherit;
    font-size: 1rem;
    transition: all var(--transition-fast);
    box-shadow: 
        inset 4px 4px 8px var(--shadow-dark),
        inset -4px -4px 8px var(--shadow-light);
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
    outline: none;
    box-shadow: 
        inset 2px 2px 4px var(--shadow-dark),
        inset -2px -2px 4px var(--shadow-light),
        0 0 0 2px var(--primary-color);
}

.form-textarea {
    resize: vertical;
    min-height: 100px;
}

/* Cards */
.card {
    background: var(--bg-secondary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xl);
    box-shadow: 
        8px 8px 16px var(--shadow-dark),
        -8px -8px 16px var(--shadow-light);
    transition: transform var(--transition-fast);
}

.card:hover {
    transform: translateY(-2px);
}

.card-header {
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-md);
    border-bottom: 1px solid var(--border-color);
}

.card-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.card-subtitle {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

/* Status Badges */
.status-badge {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: 0.8rem;
    font-weight: 500;
    box-shadow: 
        2px 2px 4px var(--shadow-dark),
        -2px -2px 4px var(--shadow-light);
}

.status-delivered {
    background: var(--success-color);
    color: white;
}

.status-pending {
    background: var(--warning-color);
    color: white;
}

.status-returned {
    background: var(--danger-color);
    color: white;
}

.status-partial {
    background: var(--info-color);
    color: white;
}

/* Modal */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 2000;
    backdrop-filter: blur(4px);
}

.modal-overlay.active {
    display: flex;
}

.modal {
    background: var(--bg-secondary);
    border-radius: var(--radius-xl);
    padding: var(--spacing-2xl);
    max-width: 600px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 
        16px 16px 32px var(--shadow-dark),
        -16px -16px 32px var(--shadow-light);
    transform: scale(0.9);
    transition: transform var(--transition-normal);
}

.modal-overlay.active .modal {
    transform: scale(1);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-xl);
    padding-bottom: var(--spacing-md);
    border-bottom: 1px solid var(--border-color);
}

.modal-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--text-secondary);
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: 50%;
    transition: all var(--transition-fast);
}

.modal-close:hover {
    color: var(--danger-color);
    background: var(--bg-primary);
}

.modal-body {
    margin-bottom: var(--spacing-xl);
}

.modal-footer {
    display: flex;
    gap: var(--spacing-md);
    justify-content: flex-end;
    padding-top: var(--spacing-md);
    border-top: 1px solid var(--border-color);
}

/* Drivers Grid */
.drivers-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: var(--spacing-xl);
}

.driver-card {
    background: var(--bg-secondary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xl);
    box-shadow: 
        8px 8px 16px var(--shadow-dark),
        -8px -8px 16px var(--shadow-light);
    transition: transform var(--transition-fast);
}

.driver-card:hover {
    transform: translateY(-2px);
}

.driver-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.driver-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    font-weight: 600;
    box-shadow: 
        4px 4px 8px var(--shadow-dark),
        -4px -4px 8px var(--shadow-light);
}

.driver-info h3 {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.driver-info p {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.driver-stats {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.driver-stat {
    text-align: center;
    padding: var(--spacing-md);
    background: var(--bg-primary);
    border-radius: var(--radius-md);
    box-shadow: 
        inset 2px 2px 4px var(--shadow-dark),
        inset -2px -2px 4px var(--shadow-light);
}

.driver-stat-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: var(--spacing-xs);
}

.driver-stat-label {
    font-size: 0.8rem;
    color: var(--text-secondary);
}

.driver-actions {
    display: flex;
    gap: var(--spacing-sm);
}

/* Report Content */
.report-content {
    background: var(--bg-secondary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xl);
    box-shadow: 
        8px 8px 16px var(--shadow-dark),
        -8px -8px 16px var(--shadow-light);
    margin-top: var(--spacing-xl);
}

.report-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.report-item {
    text-align: center;
    padding: var(--spacing-lg);
    background: var(--bg-primary);
    border-radius: var(--radius-md);
    box-shadow: 
        inset 4px 4px 8px var(--shadow-dark),
        inset -4px -4px 8px var(--shadow-light);
}

.report-value {
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: var(--spacing-xs);
}

.report-label {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

/* Accounting Summary */
.accounting-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-2xl);
}

.summary-card {
    background: var(--bg-secondary);
    padding: var(--spacing-xl);
    border-radius: var(--radius-lg);
    text-align: center;
    box-shadow: 
        8px 8px 16px var(--shadow-dark),
        -8px -8px 16px var(--shadow-light);
}

.summary-card h3 {
    font-size: 1.1rem;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-md);
}

.amount {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-color);
}

/* Settings */
.settings-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: var(--spacing-xl);
}

.settings-card {
    background: var(--bg-secondary);
    padding: var(--spacing-xl);
    border-radius: var(--radius-lg);
    box-shadow: 
        8px 8px 16px var(--shadow-dark),
        -8px -8px 16px var(--shadow-light);
}

.settings-card h3 {
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-md);
    border-bottom: 1px solid var(--border-color);
}

.setting-item {
    margin-bottom: var(--spacing-lg);
}

.setting-item:last-child {
    margin-bottom: 0;
}

.setting-item label {
    display: block;
    margin-bottom: var(--spacing-sm);
    font-weight: 500;
    color: var(--text-primary);
}

.setting-item input,
.setting-item select {
    width: 100%;
    padding: var(--spacing-md);
    border: none;
    border-radius: var(--radius-md);
    background: var(--bg-primary);
    color: var(--text-primary);
    font-family: inherit;
    box-shadow: 
        inset 4px 4px 8px var(--shadow-dark),
        inset -4px -4px 8px var(--shadow-light);
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.3s ease-in-out;
}

.slide-in-right {
    animation: slideInRight 0.3s ease-in-out;
}

.bounce-in {
    animation: bounceIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideInRight {
    from { transform: translateX(100%); }
    to { transform: translateX(0); }
}

@keyframes bounceIn {
    0% { transform: scale(0.3); opacity: 0; }
    50% { transform: scale(1.05); }
    70% { transform: scale(0.9); }
    100% { transform: scale(1); opacity: 1; }
}

/* Loading Spinner */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid var(--bg-primary);
    border-radius: 50%;
    border-top-color: var(--primary-color);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Tooltip */
.tooltip {
    position: relative;
    display: inline-block;
}

.tooltip .tooltiptext {
    visibility: hidden;
    width: 120px;
    background-color: var(--text-primary);
    color: var(--bg-secondary);
    text-align: center;
    border-radius: var(--radius-sm);
    padding: 5px;
    position: absolute;
    z-index: 1;
    bottom: 125%;
    left: 50%;
    margin-left: -60px;
    opacity: 0;
    transition: opacity 0.3s;
    font-size: 12px;
}

.tooltip:hover .tooltiptext {
    visibility: visible;
    opacity: 1;
}

/* Success/Error States */
.form-input.success {
    border: 2px solid var(--success-color);
    box-shadow:
        inset 4px 4px 8px var(--shadow-dark),
        inset -4px -4px 8px var(--shadow-light),
        0 0 0 2px var(--success-color);
}

.form-input.error {
    border: 2px solid var(--danger-color);
    box-shadow:
        inset 4px 4px 8px var(--shadow-dark),
        inset -4px -4px 8px var(--shadow-light),
        0 0 0 2px var(--danger-color);
}

/* Disabled State */
.btn-primary:disabled,
.btn-secondary:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

/* Hover Effects for Cards */
.stat-card:hover,
.driver-card:hover,
.card:hover {
    box-shadow:
        12px 12px 24px var(--shadow-dark),
        -12px -12px 24px var(--shadow-light);
}

/* Focus States */
.form-input:focus,
.form-select:focus,
.form-textarea:focus {
    transform: translateY(-1px);
}

/* Form Help Text */
.form-help {
    display: block;
    margin-top: var(--spacing-xs);
    font-size: 0.8rem;
    color: var(--text-secondary);
    font-style: italic;
}

/* Active Button States */
.btn-primary:active,
.btn-secondary:active {
    box-shadow:
        inset 4px 4px 8px var(--shadow-dark),
        inset -4px -4px 8px var(--shadow-light);
}

/* Customers Grid */
.customers-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: var(--spacing-xl);
}

.customer-card {
    background: var(--bg-secondary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xl);
    box-shadow:
        8px 8px 16px var(--shadow-dark),
        -8px -8px 16px var(--shadow-light);
    transition: transform var(--transition-fast);
}

.customer-card:hover {
    transform: translateY(-2px);
}

.customer-header {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
    position: relative;
}

.customer-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    box-shadow:
        4px 4px 8px var(--shadow-dark),
        -4px -4px 8px var(--shadow-light);
    flex-shrink: 0;
}

.customer-info {
    flex: 1;
}

.customer-info h3 {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.customer-info p {
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin-bottom: var(--spacing-xs);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.customer-type {
    font-weight: 500;
    color: var(--primary-color);
}

.customer-status {
    position: absolute;
    top: 0;
    left: 0;
}

.customer-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.customer-stat {
    text-align: center;
    padding: var(--spacing-md);
    background: var(--bg-primary);
    border-radius: var(--radius-md);
    box-shadow:
        inset 2px 2px 4px var(--shadow-dark),
        inset -2px -2px 4px var(--shadow-light);
}

.customer-stat-value {
    font-size: 1.2rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: var(--spacing-xs);
}

.customer-stat-label {
    font-size: 0.8rem;
    color: var(--text-secondary);
}

.customer-contact {
    margin-bottom: var(--spacing-lg);
    padding: var(--spacing-md);
    background: var(--bg-primary);
    border-radius: var(--radius-md);
}

.customer-contact p {
    margin: 0;
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.customer-actions {
    display: flex;
    gap: var(--spacing-sm);
    flex-wrap: wrap;
}

.customer-actions .btn-secondary,
.customer-actions .btn-success,
.customer-actions .btn-danger {
    flex: 1;
    min-width: 80px;
    font-size: 0.9rem;
    padding: var(--spacing-sm) var(--spacing-md);
}

/* Customer Table Styles */
.customer-name-cell {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.customer-name-cell i {
    color: var(--primary-color);
}

.type-badge {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: 0.8rem;
    font-weight: 500;
}

.type-individual {
    background: var(--info-color);
    color: white;
}

.type-company {
    background: var(--success-color);
    color: white;
}

/* Page Actions */
.page-actions {
    display: flex;
    gap: var(--spacing-md);
    align-items: center;
}

/* Customer Summary */
.customer-summary {
    margin-top: var(--spacing-xl);
    padding: var(--spacing-lg);
    background: var(--bg-primary);
    border-radius: var(--radius-md);
}

.customer-summary h4 {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
}

/* Responsive Design for Customers */
@media (max-width: 768px) {
    .customers-grid {
        grid-template-columns: 1fr;
    }

    .customer-header {
        flex-direction: column;
        align-items: center;
        text-align: center;
    }

    .customer-status {
        position: static;
        margin-top: var(--spacing-sm);
    }

    .customer-stats {
        grid-template-columns: 1fr;
    }

    .customer-actions {
        flex-direction: column;
    }

    .page-actions {
        flex-direction: column;
        align-items: stretch;
    }
}
