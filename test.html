<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار النظام</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>اختبار نظام إدارة التوصيل</h1>
        <p>هذه الصفحة تختبر الوظائف الأساسية للنظام</p>
        
        <button onclick="testStorage()">اختبار التخزين</button>
        <button onclick="testSampleData()">اختبار البيانات التجريبية</button>
        <button onclick="testCommissionSystem()">اختبار نظام العمولة</button>
        <button onclick="testTrackingNumbers()">اختبار أرقام الوصل</button>
        <button onclick="runAllTests()">تشغيل جميع الاختبارات</button>
        
        <div id="testResults"></div>
    </div>

    <script>
        let testResults = [];

        function addResult(test, status, message) {
            testResults.push({ test, status, message });
            updateDisplay();
        }

        function updateDisplay() {
            const container = document.getElementById('testResults');
            container.innerHTML = testResults.map(result => `
                <div class="test-result ${result.status}">
                    <strong>${result.test}:</strong> ${result.message}
                </div>
            `).join('');
        }

        async function testStorage() {
            try {
                // Test if IndexedDB is available
                if (!window.indexedDB) {
                    addResult('التخزين', 'error', 'IndexedDB غير متاح في هذا المتصفح');
                    return;
                }

                // Test basic storage operations
                const testData = { name: 'اختبار', value: 123 };
                
                addResult('التخزين', 'success', 'IndexedDB متاح ويعمل بشكل صحيح');
            } catch (error) {
                addResult('التخزين', 'error', `خطأ في التخزين: ${error.message}`);
            }
        }

        async function testSampleData() {
            try {
                // Test sample data structure
                const sampleDrivers = [
                    { name: 'مندوب تجريبي', fixedCommission: 1000 }
                ];
                
                if (sampleDrivers.length > 0 && sampleDrivers[0].fixedCommission) {
                    addResult('البيانات التجريبية', 'success', 'هيكل البيانات التجريبية صحيح');
                } else {
                    addResult('البيانات التجريبية', 'error', 'هيكل البيانات التجريبية غير صحيح');
                }
            } catch (error) {
                addResult('البيانات التجريبية', 'error', `خطأ في البيانات التجريبية: ${error.message}`);
            }
        }

        async function testCommissionSystem() {
            try {
                // Test commission calculation
                const driver = { fixedCommission: 1000 };
                const deliveredOrders = [1, 2, 3]; // 3 orders
                const totalCommission = deliveredOrders.length * driver.fixedCommission;
                
                if (totalCommission === 3000) {
                    addResult('نظام العمولة', 'success', 'حساب العمولة الثابتة يعمل بشكل صحيح');
                } else {
                    addResult('نظام العمولة', 'error', `خطأ في حساب العمولة: متوقع 3000، حصلت على ${totalCommission}`);
                }
            } catch (error) {
                addResult('نظام العمولة', 'error', `خطأ في نظام العمولة: ${error.message}`);
            }
        }

        async function testTrackingNumbers() {
            try {
                // Test tracking number generation
                const year = new Date().getFullYear();
                const randomNum = Math.floor(Math.random() * 1000000).toString().padStart(6, '0');
                const trackingNumber = `TRK${year}${randomNum}`;
                
                if (trackingNumber.startsWith('TRK') && trackingNumber.length === 11) {
                    addResult('أرقام الوصل', 'success', `تم إنشاء رقم وصل صحيح: ${trackingNumber}`);
                } else {
                    addResult('أرقام الوصل', 'error', `تنسيق رقم الوصل غير صحيح: ${trackingNumber}`);
                }
            } catch (error) {
                addResult('أرقام الوصل', 'error', `خطأ في أرقام الوصل: ${error.message}`);
            }
        }

        async function runAllTests() {
            testResults = [];
            addResult('بدء الاختبارات', 'info', 'جاري تشغيل جميع الاختبارات...');
            
            await testStorage();
            await testSampleData();
            await testCommissionSystem();
            await testTrackingNumbers();
            
            const successCount = testResults.filter(r => r.status === 'success').length;
            const errorCount = testResults.filter(r => r.status === 'error').length;
            
            addResult('النتيجة النهائية', successCount > errorCount ? 'success' : 'error', 
                `نجح ${successCount} اختبار، فشل ${errorCount} اختبار`);
        }

        // Auto-run tests on page load
        window.addEventListener('load', () => {
            setTimeout(runAllTests, 1000);
        });
    </script>
</body>
</html>
